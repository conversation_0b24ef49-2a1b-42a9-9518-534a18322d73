import{r as y,bS as oe,bT as ie,bU as ae,bV as le,cl as s,cn as P,bv as F,co as de,bO as J,cm as z,bw as C,c0 as Y,c1 as G}from"./index.CbQtRkVt.js";var pe=["title","size","color","overrides"];function D(){return D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},D.apply(this,arguments)}function fe(e,t){if(e==null)return{};var r=ye(e,t),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ye(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}function ge(e,t){return me(e)||ve(e,t)||he(e,t)||be()}function be(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function he(e,t){if(e){if(typeof e=="string")return Q(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Q(e,t)}}function Q(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ve(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,i=!1,a,l;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!(t&&n.length===t));o=!0);}catch(u){i=!0,l=u}finally{try{!o&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function me(e){if(Array.isArray(e))return e}function $e(e,t){var r=oe(),n=ge(r,2),o=n[1],i=e.title,a=i===void 0?"Hide":i,l=e.size,u=e.color,c=e.overrides,d=c===void 0?{}:c,g=fe(e,pe),b=ie({component:o.icons&&o.icons.Hide?o.icons.Hide:null},d&&d.Svg?ae(d.Svg):{});return y.createElement(le,D({viewBox:"0 0 20 20",ref:t,title:a,size:l,color:u,overrides:{Svg:b}},g),y.createElement("path",{d:"M12.81 4.36l-1.77 1.78a4 4 0 00-4.9 4.9l-2.76 2.75C2.06 12.79.96 11.49.2 10a11 11 0 0112.6-5.64zm3.8 1.85c1.33 1 2.43 2.3 3.2 3.79a11 11 0 01-12.62 5.64l1.77-1.78a4 4 0 004.9-4.9l2.76-2.75zm-.25-3.99l1.42 1.42L3.64 17.78l-1.42-1.42L16.36 2.22z"}))}const Oe=y.forwardRef($e);var Ce=["title","size","color","overrides"];function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},V.apply(this,arguments)}function Se(e,t){if(e==null)return{};var r=_e(e,t),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function _e(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}function Ie(e,t){return Te(e)||Fe(e,t)||Pe(e,t)||we()}function we(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Pe(e,t){if(e){if(typeof e=="string")return X(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return X(e,t)}}function X(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Fe(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,i=!1,a,l;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!(t&&n.length===t));o=!0);}catch(u){i=!0,l=u}finally{try{!o&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function Te(e){if(Array.isArray(e))return e}function ke(e,t){var r=oe(),n=Ie(r,2),o=n[1],i=e.title,a=i===void 0?"Show":i,l=e.size,u=e.color,c=e.overrides,d=c===void 0?{}:c,g=Se(e,Ce),b=ie({component:o.icons&&o.icons.Show?o.icons.Show:null},d&&d.Svg?ae(d.Svg):{});return y.createElement(le,V({viewBox:"0 0 20 20",ref:t,title:a,size:l,color:u,overrides:{Svg:b}},g),y.createElement("path",{d:"M.2 10a11 11 0 0119.6 0A11 11 0 01.2 10zm9.8 4a4 4 0 100-8 4 4 0 000 8zm0-2a2 2 0 110-4 2 2 0 010 4z"}))}const Ee=y.forwardRef(ke);function ee(e,t){var r=e.disabled,n=e.error,o=e.positive,i=e.adjoined,a=e.size,l=e.required,u=e.resize,c=e.readOnly,d=t.isFocused;return{$isFocused:d,$disabled:r,$error:n,$positive:o,$adjoined:i,$size:a,$required:l,$resize:u,$isReadOnly:c}}function te(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?te(Object(r),!0).forEach(function(n){p(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):te(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function p(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var K=F("button",function(e){var t,r=e.$theme,n=e.$size,o=e.$isFocusVisible,i=(t={},p(t,s.mini,r.sizing.scale400),p(t,s.compact,r.sizing.scale400),p(t,s.default,r.sizing.scale300),p(t,s.large,r.sizing.scale200),t)[n];return{display:"flex",alignItems:"center",borderTopStyle:"none",borderBottomStyle:"none",borderLeftStyle:"none",borderRightStyle:"none",background:"none",paddingLeft:i,paddingRight:i,outline:o?"solid 3px ".concat(r.colors.accent):"none",color:r.colors.contentPrimary}});K.displayName="StyledMaskToggleButton";K.displayName="StyledMaskToggleButton";var Z=F("div",function(e){var t,r=e.$alignTop,n=r===void 0?!1:r,o=e.$size,i=e.$theme,a=(t={},p(t,s.mini,i.sizing.scale200),p(t,s.compact,i.sizing.scale200),p(t,s.default,i.sizing.scale100),p(t,s.large,i.sizing.scale0),t)[o];return{display:"flex",alignItems:n?"flex-start":"center",paddingLeft:a,paddingRight:a,paddingTop:n?i.sizing.scale500:"0px",color:i.colors.contentPrimary}});Z.displayName="StyledClearIconContainer";Z.displayName="StyledClearIconContainer";var H=F(de,function(e){var t=e.$theme,r=e.$isFocusVisible;return{cursor:"pointer",outline:r?"solid 3px ".concat(t.colors.accent):"none"}});H.displayName="StyledClearIcon";H.displayName="StyledClearIcon";function Re(e,t){var r;return(r={},p(r,s.mini,{paddingTop:t.scale100,paddingBottom:t.scale100,paddingLeft:t.scale550,paddingRight:t.scale550}),p(r,s.compact,{paddingTop:t.scale200,paddingBottom:t.scale200,paddingLeft:t.scale550,paddingRight:t.scale550}),p(r,s.default,{paddingTop:t.scale400,paddingBottom:t.scale400,paddingLeft:t.scale550,paddingRight:t.scale550}),p(r,s.large,{paddingTop:t.scale550,paddingBottom:t.scale550,paddingLeft:t.scale550,paddingRight:t.scale550}),r)[e]}function xe(e,t,r,n,o){var i=e===P.both||e===P.left&&n!=="rtl"||e===P.right&&n==="rtl"||o&&n==="rtl",a=e===P.both||e===P.right&&n!=="rtl"||e===P.left&&n==="rtl"||o&&n!=="rtl";return{paddingLeft:i?r.scale550:"0px",paddingRight:a?r.scale550:"0px"}}function A(e,t){var r;return(r={},p(r,s.mini,t.font100),p(r,s.compact,t.font200),p(r,s.default,t.font300),p(r,s.large,t.font400),r)[e]}function Be(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,o=arguments.length>4?arguments[4]:void 0;return e?{borderLeftColor:o.inputFillDisabled,borderRightColor:o.inputFillDisabled,borderTopColor:o.inputFillDisabled,borderBottomColor:o.inputFillDisabled,backgroundColor:o.inputFillDisabled}:t?{borderLeftColor:o.borderSelected,borderRightColor:o.borderSelected,borderTopColor:o.borderSelected,borderBottomColor:o.borderSelected,backgroundColor:o.inputFillActive}:r?{borderLeftColor:o.inputBorderError,borderRightColor:o.inputBorderError,borderTopColor:o.inputBorderError,borderBottomColor:o.inputBorderError,backgroundColor:o.inputFillError}:n?{borderLeftColor:o.inputBorderPositive,borderRightColor:o.inputBorderPositive,borderTopColor:o.inputBorderPositive,borderBottomColor:o.inputBorderPositive,backgroundColor:o.inputFillPositive}:{borderLeftColor:o.inputBorder,borderRightColor:o.inputBorder,borderTopColor:o.inputBorder,borderBottomColor:o.inputBorder,backgroundColor:o.inputFill}}function je(e,t){var r=t.inputBorderRadius;return e===s.mini&&(r=t.inputBorderRadiusMini),{borderTopLeftRadius:r,borderBottomLeftRadius:r,borderTopRightRadius:r,borderBottomRightRadius:r}}var Ae=function(t){var r=t.$isFocused,n=t.$adjoined,o=t.$error,i=t.$disabled,a=t.$positive,l=t.$size,u=t.$theme,c=t.$theme,d=c.borders,g=c.colors,b=c.sizing,I=c.typography,$=c.animation,_=t.$hasIconTrailing;return h(h(h(h({boxSizing:"border-box",display:"flex",overflow:"hidden",width:"100%",borderLeftWidth:"2px",borderRightWidth:"2px",borderTopWidth:"2px",borderBottomWidth:"2px",borderLeftStyle:"solid",borderRightStyle:"solid",borderTopStyle:"solid",borderBottomStyle:"solid",transitionProperty:"border",transitionDuration:$.timing200,transitionTimingFunction:$.easeOutCurve},je(l,d)),A(l,I)),Be(i,r,o,a,g)),xe(n,l,b,u.direction,_))},se=F("div",Ae);se.displayName="Root";se.displayName="Root";function Me(e,t){var r;return(r={},p(r,s.mini,{paddingRight:t.scale400,paddingLeft:t.scale400}),p(r,s.compact,{paddingRight:t.scale400,paddingLeft:t.scale400}),p(r,s.default,{paddingRight:t.scale300,paddingLeft:t.scale300}),p(r,s.large,{paddingRight:t.scale200,paddingLeft:t.scale200}),r)[e]}function Le(e,t,r,n,o){return e?{color:o.inputEnhancerTextDisabled,backgroundColor:o.inputFillDisabled}:t?{color:o.contentPrimary,backgroundColor:o.inputFillActive}:r?{color:o.contentPrimary,backgroundColor:o.inputFillError}:n?{color:o.contentPrimary,backgroundColor:o.inputFillPositive}:{color:o.contentPrimary,backgroundColor:o.inputFill}}var ue=F("div",function(e){var t=e.$size,r=e.$disabled,n=e.$isFocused,o=e.$error,i=e.$positive,a=e.$theme,l=a.colors,u=a.sizing,c=a.typography,d=a.animation;return h(h(h({display:"flex",alignItems:"center",justifyContent:"center",transitionProperty:"color, background-color",transitionDuration:d.timing200,transitionTimingFunction:d.easeOutCurve},A(t,c)),Me(t,u)),Le(r,n,o,i,l))});ue.displayName="InputEnhancer";ue.displayName="InputEnhancer";function ze(e,t,r,n,o){return e?{color:o.inputTextDisabled,backgroundColor:o.inputFillDisabled}:t?{color:o.contentPrimary,backgroundColor:o.inputFillActive}:r?{color:o.contentPrimary,backgroundColor:o.inputFillError}:n?{color:o.contentPrimary,backgroundColor:o.inputFillPositive}:{color:o.contentPrimary,backgroundColor:o.inputFill}}var De=function(t){var r=t.$isFocused,n=t.$error,o=t.$disabled,i=t.$positive,a=t.$size,l=t.$theme,u=l.colors,c=l.typography,d=l.animation;return h(h({display:"flex",width:"100%",transitionProperty:"background-color",transitionDuration:d.timing200,transitionTimingFunction:d.easeOutCurve},A(a,c)),ze(o,r,n,i,u))},U=F("div",De);U.displayName="InputContainer";U.displayName="InputContainer";function Ve(e,t,r,n){return e?{color:n.inputTextDisabled,"-webkit-text-fill-color":n.inputTextDisabled,caretColor:n.contentPrimary,"::placeholder":{color:n.inputPlaceholderDisabled}}:{color:n.contentPrimary,caretColor:n.contentPrimary,"::placeholder":{color:n.inputPlaceholder}}}var Ne=function(t){var r=t.$disabled,n=t.$isFocused,o=t.$error,i=t.$size,a=t.$theme,l=a.colors,u=a.sizing,c=a.typography;return h(h(h({boxSizing:"border-box",backgroundColor:"transparent",borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0,borderLeftStyle:"none",borderRightStyle:"none",borderTopStyle:"none",borderBottomStyle:"none",outline:"none",width:"100%",minWidth:0,maxWidth:"100%",cursor:r?"not-allowed":"text",margin:"0",paddingTop:"0",paddingBottom:"0",paddingLeft:"0",paddingRight:"0"},A(i,c)),Re(i,u)),Ve(r,n,o,l))},q=F("input",Ne);q.displayName="Input";q.displayName="Input";function We(e){var t;return typeof window.Event=="function"?t=new window.Event(e,{bubbles:!0,cancelable:!0}):(t=document.createEvent("Event"),t.initEvent(e,!0,!0)),t}function N(e){"@babel/helpers - typeof";return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},N(e)}function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},m.apply(this,arguments)}function S(e,t){return Ue(e)||He(e,t)||Ze(e,t)||Ke()}function Ke(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ze(e,t){if(e){if(typeof e=="string")return re(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return re(e,t)}}function re(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function He(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,i=!1,a,l;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!(t&&n.length===t));o=!0);}catch(u){i=!0,l=u}finally{try{!o&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function Ue(e){if(Array.isArray(e))return e}function qe(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Je(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ye(e,t,r){return t&&Je(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ge(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&W(e,t)}function W(e,t){return W=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},W(e,t)}function Qe(e){var t=et();return function(){var n=j(e),o;if(t){var i=j(this).constructor;o=Reflect.construct(n,arguments,i)}else o=n.apply(this,arguments);return Xe(this,o)}}function Xe(e,t){if(t&&(N(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return v(e)}function v(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function et(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function j(e){return j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},j(e)}function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ne=function(){return null},tt=function(e){Ge(r,e);var t=Qe(r);function r(){var n;qe(this,r);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=t.call.apply(t,[this].concat(i)),f(v(n),"inputRef",n.props.inputRef||y.createRef()),f(v(n),"state",{isFocused:n.props.autoFocus||!1,isMasked:n.props.type==="password",initialType:n.props.type,isFocusVisibleForClear:!1,isFocusVisibleForMaskToggle:!1}),f(v(n),"onInputKeyDown",function(l){n.props.clearOnEscape&&l.key==="Escape"&&n.inputRef.current&&!n.props.readOnly&&(n.clearValue(),l.stopPropagation())}),f(v(n),"onClearIconClick",function(){n.inputRef.current&&n.clearValue(),n.inputRef.current&&n.inputRef.current.focus()}),f(v(n),"onFocus",function(l){n.setState({isFocused:!0}),n.props.onFocus(l)}),f(v(n),"onBlur",function(l){n.setState({isFocused:!1}),n.props.onBlur(l)}),f(v(n),"handleFocusForMaskToggle",function(l){J(l)&&n.setState({isFocusVisibleForMaskToggle:!0})}),f(v(n),"handleBlurForMaskToggle",function(l){n.state.isFocusVisibleForMaskToggle!==!1&&n.setState({isFocusVisibleForMaskToggle:!1})}),f(v(n),"handleFocusForClear",function(l){J(l)&&n.setState({isFocusVisibleForClear:!0})}),f(v(n),"handleBlurForClear",function(l){n.state.isFocusVisibleForClear!==!1&&n.setState({isFocusVisibleForClear:!1})}),n}return Ye(r,[{key:"componentDidMount",value:function(){var o=this.props,i=o.autoFocus,a=o.clearable;this.inputRef.current&&(i&&this.inputRef.current.focus(),a&&this.inputRef.current.addEventListener("keydown",this.onInputKeyDown))}},{key:"componentWillUnmount",value:function(){var o=this.props.clearable;o&&this.inputRef.current&&this.inputRef.current.removeEventListener("keydown",this.onInputKeyDown)}},{key:"clearValue",value:function(){var o=this.inputRef.current;if(o){var i=Object.getOwnPropertyDescriptor(this.props.type===z.textarea?HTMLTextAreaElement.prototype:HTMLInputElement.prototype,"value");if(i){var a=i.set;if(a){a.call(o,"");var l=We("input");o.dispatchEvent(l)}}}}},{key:"getInputType",value:function(){return this.props.type==="password"?this.state.isMasked?"password":"text":this.props.type}},{key:"renderMaskToggle",value:function(){var o,i=this;if(this.props.type!=="password")return null;var a=C(this.props.overrides.MaskToggleButton,K),l=S(a,2),u=l[0],c=l[1],d=C(this.props.overrides.MaskToggleShowIcon,Ee),g=S(d,2),b=g[0],I=g[1],$=C(this.props.overrides.MaskToggleHideIcon,Oe),_=S($,2),T=_[0],x=_[1],O=this.state.isMasked?"Show password text":"Hide password text",k=(o={},f(o,s.mini,"12px"),f(o,s.compact,"16px"),f(o,s.default,"20px"),f(o,s.large,"24px"),o)[this.props.size];return y.createElement(u,m({$size:this.props.size,$isFocusVisible:this.state.isFocusVisibleForMaskToggle,"aria-label":O,onClick:function(){return i.setState(function(E){return{isMasked:!E.isMasked}})},title:O,type:"button"},c,{onFocus:G(c,this.handleFocusForMaskToggle),onBlur:Y(c,this.handleBlurForMaskToggle)}),this.state.isMasked?y.createElement(b,m({size:k,title:O},I)):y.createElement(T,m({size:k,title:O},x)))}},{key:"renderClear",value:function(){var o,i=this,a=this.props,l=a.clearable,u=a.value,c=a.disabled,d=a.readOnly,g=a.overrides,b=g===void 0?{}:g;if(c||d||!l||u==null||typeof u=="string"&&u.length===0)return null;var I=C(b.ClearIconContainer,Z),$=S(I,2),_=$[0],T=$[1],x=C(b.ClearIcon,H),O=S(x,2),k=O[0],w=O[1],E="Clear value",B=ee(this.props,this.state),M=(o={},f(o,s.mini,"14px"),f(o,s.compact,"14px"),f(o,s.default,"16px"),f(o,s.large,"22px"),o)[this.props.size];return y.createElement(_,m({$alignTop:this.props.type===z.textarea},B,T),y.createElement(k,m({size:M,tabIndex:0,title:E,"aria-label":E,onClick:this.onClearIconClick,onKeyDown:function(R){R.key&&(R.key==="Enter"||R.key===" ")&&(R.preventDefault(),i.onClearIconClick())},role:"button",$isFocusVisible:this.state.isFocusVisibleForClear},B,w,{onFocus:G(w,this.handleFocusForClear),onBlur:Y(w,this.handleBlurForClear)})))}},{key:"render",value:function(){var o=this.props.overrides,i=o.InputContainer,a=o.Input,l=o.Before,u=o.After,c=this.state.initialType==="password"&&this.props.autoComplete===r.defaultProps.autoComplete?"new-password":this.props.autoComplete,d=ee(this.props,this.state),g=C(i,U),b=S(g,2),I=b[0],$=b[1],_=C(a,q),T=S(_,2),x=T[0],O=T[1],k=C(l,ne),w=S(k,2),E=w[0],B=w[1],M=C(u,ne),L=S(M,2),R=L[0],ce=L[1];return y.createElement(I,m({"data-baseweb":this.props["data-baseweb"]||"base-input"},d,$),y.createElement(E,m({},d,B)),y.createElement(x,m({ref:this.inputRef,"aria-activedescendant":this.props["aria-activedescendant"],"aria-autocomplete":this.props["aria-autocomplete"],"aria-controls":this.props["aria-controls"],"aria-errormessage":this.props["aria-errormessage"],"aria-haspopup":this.props["aria-haspopup"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-describedby":this.props["aria-describedby"],"aria-invalid":this.props.error,"aria-required":this.props.required,autoComplete:c,disabled:this.props.disabled,readOnly:this.props.readOnly,id:this.props.id,inputMode:this.props.inputMode,maxLength:this.props.maxLength,name:this.props.name,onBlur:this.onBlur,onChange:this.props.onChange,onFocus:this.onFocus,onKeyDown:this.props.onKeyDown,onKeyPress:this.props.onKeyPress,onKeyUp:this.props.onKeyUp,pattern:this.props.pattern,placeholder:this.props.placeholder,type:this.getInputType(),required:this.props.required,role:this.props.role,value:this.props.value,min:this.props.min,max:this.props.max,step:this.props.step,rows:this.props.type===z.textarea?this.props.rows:null},d,O)),this.renderClear(),this.renderMaskToggle(),y.createElement(R,m({},d,ce)))}}]),r}(y.Component);f(tt,"defaultProps",{"aria-activedescendant":null,"aria-autocomplete":null,"aria-controls":null,"aria-errormessage":null,"aria-haspopup":null,"aria-label":null,"aria-labelledby":null,"aria-describedby":null,adjoined:P.none,autoComplete:"on",autoFocus:!1,disabled:!1,error:!1,positive:!1,name:"",inputMode:"text",onBlur:function(){},onChange:function(){},onKeyDown:function(){},onKeyPress:function(){},onKeyUp:function(){},onFocus:function(){},onClear:function(){},clearable:!1,clearOnEscape:!0,overrides:{},pattern:null,placeholder:"",required:!1,role:null,size:s.default,type:"text",readOnly:!1});export{tt as B,q as I,se as R,De as a,Ne as b,ue as c,ee as d,Ae as g};
