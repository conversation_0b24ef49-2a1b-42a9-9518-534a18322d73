const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./data-grid-overlay-editor.CoquyZNK.js","./index.CbQtRkVt.js","../css/index.CJVRHjQZ.css","./FormClearHelper.Cdw5Y7_m.js","./withFullScreenWrapper.DWXejOhQ.js","./Toolbar.D6yqQ65-.js","./checkbox.C7QR6llE.js","./mergeWith.DvOME7eH.js","./sprintf.D7DtBTRn.js","./createDownloadLinkElement.DZMwyjvU.js","./toConsumableArray.CbNz0Ciu.js","./possibleConstructorReturn.C5GiK_Ob.js","./createSuper.C5k_2vfB.js","./FileDownload.esm.Dy1V9a2E.js","./number-overlay-editor.BRNxOzEZ.js","./es6.CVz13CSz.js"])))=>i.map(i=>d[i]);
import{r as d,E as Ps,_ as Ne,c as oh,g as ar,i as $a,d as so,e as ah,f as sh,h as lh,m as kc,n as Mc,R as Nt,o as uh,p as Rc,q as ch,t as dh,u as fh,v as hh,w as gh,x as mh,a as Cr,y as _s,s as mi,z as $r,j as Qe,F as Ec,A as ma,T as Ic,B as pa,C as In,D as nr,G as va,H as cn,I as Tc,J as ai,K as ph,M as vh,N as Ae,O as Dc,Q as st,S as qi,U as zr,V as Ba,W as bh,X as wh,Y as yh,Z as Il,$ as Ch,a0 as Sh,a1 as xh,a2 as kh,a3 as Oc,a4 as Mh,a5 as Rh,a6 as bs,a7 as Pc,a8 as _c,a9 as Eh,aa as Ih,ab as Th,ac as Dh,ad as Oh,ae as Ph,af as _h,ag as Fh,ah as Lh,ai as Fc,aj as Ah,ak as Hh,al as zh,am as Vh,an as Nh,ao as $h,ap as Bh,aq as Wh,ar as Uh,as as qh,at as Gh,au as Yh,av as Xh,aw as jh,ax as Cn,l as Fs,ay as Gi,az as sr,aA as _e,aB as Kh,b as Lc,aC as Ac,k as Zh,aD as Jh,aE as Qh,aF as Wa,aG as eg,aH as tg,aI as ng,L as rg,aJ as ig}from"./index.CbQtRkVt.js";import{u as og}from"./FormClearHelper.Cdw5Y7_m.js";import{w as ag,E as sg}from"./withFullScreenWrapper.DWXejOhQ.js";import{T as lg,a as ii}from"./Toolbar.D6yqQ65-.js";import{L as ug,S as cg,a as dg}from"./checkbox.C7QR6llE.js";import{m as fg}from"./mergeWith.DvOME7eH.js";import{s as hg}from"./sprintf.D7DtBTRn.js";import{c as gg}from"./createDownloadLinkElement.DZMwyjvU.js";import{_ as rr,a as Ls,C as mg}from"./toConsumableArray.CbNz0Ciu.js";import{_ as pg,a as vg,b as bg}from"./possibleConstructorReturn.C5GiK_Ob.js";import{_ as wg}from"./createSuper.C5k_2vfB.js";import{D as yg,F as Cg}from"./FileDownload.esm.Dy1V9a2E.js";var Hc=d.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(Ps,Ne({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))});Hc.displayName="Add";var zc=d.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(Ps,Ne({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))});zc.displayName="Search";var Vc=d.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(Ps,Ne({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M12 6a9.77 9.77 0 018.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0112 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 010 5 2.5 2.5 0 010-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"}))});Vc.displayName="Visibility";var Ua,Tl;function Sg(){if(Tl)return Ua;Tl=1;var e=Object.prototype,t=e.hasOwnProperty;function n(r,i){return r!=null&&t.call(r,i)}return Ua=n,Ua}var qa,Dl;function xg(){if(Dl)return qa;Dl=1;var e=Sg(),t=oh();function n(r,i){return r!=null&&t(r,i,e)}return qa=n,qa}var kg=xg();const Mg=ar(kg);function Nc(e="This should not happen"){throw new Error(e)}function Tn(e,t="Assertion failed"){if(!e)return Nc(t)}function eo(e,t){return Nc(t??"Hell froze over")}function Rg(e,t){try{return e()}catch{return t}}const Ol=Object.prototype.hasOwnProperty;function hi(e,t){let n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&hi(e[r],t[r]););return r===-1}if(!n||typeof e=="object"){r=0;for(n in e)if(Ol.call(e,n)&&++r&&!Ol.call(t,n)||!(n in t)||!hi(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}const ea=null,As=void 0;var Z;(function(e){e.Uri="uri",e.Text="text",e.Image="image",e.RowID="row-id",e.Number="number",e.Bubble="bubble",e.Boolean="boolean",e.Loading="loading",e.Markdown="markdown",e.Drilldown="drilldown",e.Protected="protected",e.Custom="custom"})(Z||(Z={}));var Pl;(function(e){e.HeaderRowID="headerRowID",e.HeaderCode="headerCode",e.HeaderNumber="headerNumber",e.HeaderString="headerString",e.HeaderBoolean="headerBoolean",e.HeaderAudioUri="headerAudioUri",e.HeaderVideoUri="headerVideoUri",e.HeaderEmoji="headerEmoji",e.HeaderImage="headerImage",e.HeaderUri="headerUri",e.HeaderPhone="headerPhone",e.HeaderMarkdown="headerMarkdown",e.HeaderDate="headerDate",e.HeaderTime="headerTime",e.HeaderEmail="headerEmail",e.HeaderReference="headerReference",e.HeaderIfThenElse="headerIfThenElse",e.HeaderSingleValue="headerSingleValue",e.HeaderLookup="headerLookup",e.HeaderTextTemplate="headerTextTemplate",e.HeaderMath="headerMath",e.HeaderRollup="headerRollup",e.HeaderJoinStrings="headerJoinStrings",e.HeaderSplitString="headerSplitString",e.HeaderGeoDistance="headerGeoDistance",e.HeaderArray="headerArray",e.RowOwnerOverlay="rowOwnerOverlay",e.ProtectedColumnOverlay="protectedColumnOverlay"})(Pl||(Pl={}));var ta;(function(e){e.Triangle="triangle",e.Dots="dots"})(ta||(ta={}));function Po(e){return"width"in e&&typeof e.width=="number"}async function _l(e){return typeof e=="object"?e:await e()}function si(e){return!(e.kind===Z.Loading||e.kind===Z.Bubble||e.kind===Z.RowID||e.kind===Z.Protected||e.kind===Z.Drilldown)}function ui(e){return e.kind===An.Marker||e.kind===An.NewRow}function Yi(e){if(!si(e)||e.kind===Z.Image)return!1;switch(e.kind){case Z.Text:case Z.Number:case Z.Markdown:case Z.Uri:case Z.Custom:case Z.Boolean:return e.readonly!==!0;default:eo(e,"A cell was passed with an invalid kind")}}function Eg(e){return Mg(e,"editor")}function Hs(e){return!(e.readonly??!1)}var An;(function(e){e.NewRow="new-row",e.Marker="marker"})(An||(An={}));function Ig(e){if(e.length===0)return[];const t=[...e],n=[];t.sort(function(r,i){return r[0]-i[0]}),n.push([...t[0]]);for(const r of t.slice(1)){const i=n[n.length-1];i[1]<r[0]?n.push([...r]):i[1]<r[1]&&(i[1]=r[1])}return n}let Fl;class rt{items;constructor(t){this.items=t}static empty=()=>Fl??(Fl=new rt([]));static fromSingleSelection=t=>rt.empty().add(t);offset(t){if(t===0)return this;const n=this.items.map(r=>[r[0]+t,r[1]+t]);return new rt(n)}add(t){const n=typeof t=="number"?[t,t+1]:t,r=Ig([...this.items,n]);return new rt(r)}remove(t){const n=[...this.items],r=typeof t=="number"?t:t[0],i=typeof t=="number"?t+1:t[1];for(const[o,s]of n.entries()){const[a,l]=s;if(a<=i&&r<=l){const u=[];a<r&&u.push([a,r]),i<l&&u.push([i,l]),n.splice(o,1,...u)}}return new rt(n)}first(){if(this.items.length!==0)return this.items[0][0]}last(){if(this.items.length!==0)return this.items.slice(-1)[0][1]-1}hasIndex(t){for(let n=0;n<this.items.length;n++){const[r,i]=this.items[n];if(t>=r&&t<i)return!0}return!1}hasAll(t){for(let n=t[0];n<t[1];n++)if(!this.hasIndex(n))return!1;return!0}some(t){for(const n of this)if(t(n))return!0;return!1}equals(t){if(t===this)return!0;if(t.items.length!==this.items.length)return!1;for(let n=0;n<this.items.length;n++){const r=t.items[n],i=this.items[n];if(r[0]!==i[0]||r[1]!==i[1])return!1}return!0}toArray(){const t=[];for(const[n,r]of this.items)for(let i=n;i<r;i++)t.push(i);return t}get length(){let t=0;for(const[n,r]of this.items)t+=r-n;return t}*[Symbol.iterator](){for(const[t,n]of this.items)for(let r=t;r<n;r++)yield r}}var Tg=function(){const t=Array.prototype.slice.call(arguments).filter(Boolean),n={},r=[];t.forEach(o=>{(o?o.split(" "):[]).forEach(a=>{if(a.startsWith("atm_")){const[,l]=a.split("_");n[l]=a}else r.push(a)})});const i=[];for(const o in n)Object.prototype.hasOwnProperty.call(n,o)&&i.push(n[o]);return i.push(...r),i.join(" ")},Ll=Tg,Dg=e=>e.toUpperCase()===e,Og=e=>t=>e.indexOf(t)===-1,$c=(e,t)=>{const n={};return Object.keys(e).filter(Og(t)).forEach(r=>{n[r]=e[r]}),n};function Pg(e,t,n){const r=$c(t,n);if(!e){const i=typeof $a=="function"?{default:$a}:$a;Object.keys(r).forEach(o=>{i.default(o)||delete r[o]})}return r}var _g=(e,t)=>{};function Fg(e){let t="";return n=>{const r=(o,s)=>{const{as:a=e,class:l=t}=o,u=n.propsAsIs===void 0?!(typeof a=="string"&&a.indexOf("-")===-1&&!Dg(a[0])):n.propsAsIs,c=Pg(u,o,["as","class"]);c.ref=s,c.className=n.atomic?Ll(n.class,c.className||l):Ll(c.className||l,n.class);const{vars:f}=n;if(f){const g={};for(const p in f){const w=f[p],b=w[0],v=w[1]||"",S=typeof b=="function"?b(o):b;_g(S,n.name),g[`--${p}`]=`${S}${v}`}const h=c.style||{},m=Object.keys(h);m.length>0&&m.forEach(p=>{g[p]=h[p]}),c.style=g}return e.__wyw_meta&&e!==a?(c.as=a,d.createElement(e,c)):d.createElement(a,c)},i=d.forwardRef?d.forwardRef(r):o=>{const s=$c(o,["innerRef"]);return r(s,o.innerRef)};return i.displayName=n.name,i.__wyw_meta={className:n.class||t,extends:e},i}}var fn=Fg;const Lg=fn("div")({name:"ImageOverlayEditorStyle",class:"gdg-i2iowwq",propsAsIs:!1});var Ga={},_i={},_o={},Fo={},Al;function Ag(){return Al||(Al=1,function(e){(function(t,n){n(e,so(),ah())})(Fo,function(t,n,r){Object.defineProperty(t,"__esModule",{value:!0}),t.setHasSupportToCaptureOption=m;var i=s(n),o=s(r);function s(v){return v&&v.__esModule?v:{default:v}}var a=Object.assign||function(v){for(var S=1;S<arguments.length;S++){var O=arguments[S];for(var R in O)Object.prototype.hasOwnProperty.call(O,R)&&(v[R]=O[R])}return v};function l(v,S){var O={};for(var R in v)S.indexOf(R)>=0||Object.prototype.hasOwnProperty.call(v,R)&&(O[R]=v[R]);return O}function u(v,S){if(!(v instanceof S))throw new TypeError("Cannot call a class as a function")}var c=function(){function v(S,O){for(var R=0;R<O.length;R++){var M=O[R];M.enumerable=M.enumerable||!1,M.configurable=!0,"value"in M&&(M.writable=!0),Object.defineProperty(S,M.key,M)}}return function(S,O,R){return O&&v(S.prototype,O),R&&v(S,R),S}}();function f(v,S){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:v}function g(v,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);v.prototype=Object.create(S&&S.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(v,S):v.__proto__=S)}var h=!1;function m(v){h=v}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){m(!0)}}))}catch{}function p(){var v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{capture:!0};return h?v:v.capture}function w(v){if("touches"in v){var S=v.touches[0],O=S.pageX,R=S.pageY;return{x:O,y:R}}var M=v.screenX,_=v.screenY;return{x:M,y:_}}var b=function(v){g(S,v);function S(){var O;u(this,S);for(var R=arguments.length,M=Array(R),_=0;_<R;_++)M[_]=arguments[_];var E=f(this,(O=S.__proto__||Object.getPrototypeOf(S)).call.apply(O,[this].concat(M)));return E._handleSwipeStart=E._handleSwipeStart.bind(E),E._handleSwipeMove=E._handleSwipeMove.bind(E),E._handleSwipeEnd=E._handleSwipeEnd.bind(E),E._onMouseDown=E._onMouseDown.bind(E),E._onMouseMove=E._onMouseMove.bind(E),E._onMouseUp=E._onMouseUp.bind(E),E._setSwiperRef=E._setSwiperRef.bind(E),E}return c(S,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(R){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(R))}},{key:"_onMouseMove",value:function(R){this.mouseDown&&this._handleSwipeMove(R)}},{key:"_onMouseUp",value:function(R){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(R)}},{key:"_handleSwipeStart",value:function(R){var M=w(R),_=M.x,E=M.y;this.moveStart={x:_,y:E},this.props.onSwipeStart(R)}},{key:"_handleSwipeMove",value:function(R){if(this.moveStart){var M=w(R),_=M.x,E=M.y,k=_-this.moveStart.x,F=E-this.moveStart.y;this.moving=!0;var D=this.props.onSwipeMove({x:k,y:F},R);D&&R.cancelable&&R.preventDefault(),this.movePosition={deltaX:k,deltaY:F}}}},{key:"_handleSwipeEnd",value:function(R){this.props.onSwipeEnd(R);var M=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-M?this.props.onSwipeLeft(1,R):this.movePosition.deltaX>M&&this.props.onSwipeRight(1,R),this.movePosition.deltaY<-M?this.props.onSwipeUp(1,R):this.movePosition.deltaY>M&&this.props.onSwipeDown(1,R)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(R){this.swiper=R,this.props.innerRef(R)}},{key:"render",value:function(){var R=this.props;R.tagName;var M=R.className,_=R.style,E=R.children;R.allowMouseEvents,R.onSwipeUp,R.onSwipeDown,R.onSwipeLeft,R.onSwipeRight,R.onSwipeStart,R.onSwipeMove,R.onSwipeEnd,R.innerRef,R.tolerance;var k=l(R,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]);return i.default.createElement(this.props.tagName,a({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:M,style:_},k),E)}}]),S}(n.Component);b.displayName="ReactSwipe",b.propTypes={tagName:o.default.string,className:o.default.string,style:o.default.object,children:o.default.node,allowMouseEvents:o.default.bool,onSwipeUp:o.default.func,onSwipeDown:o.default.func,onSwipeLeft:o.default.func,onSwipeRight:o.default.func,onSwipeStart:o.default.func,onSwipeMove:o.default.func,onSwipeEnd:o.default.func,innerRef:o.default.func,tolerance:o.default.number.isRequired},b.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},t.default=b})}(Fo)),Fo}var Hl;function Bc(){return Hl||(Hl=1,function(e){(function(t,n){n(e,Ag())})(_o,function(t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=i(n);function i(o){return o&&o.__esModule?o:{default:o}}t.default=r.default})}(_o)),_o}var Fi={},zl;function Wc(){if(zl)return Fi;zl=1,Object.defineProperty(Fi,"__esModule",{value:!0}),Fi.default=void 0;var e=t(sh());function t(i){return i&&i.__esModule?i:{default:i}}function n(i,o,s){return o in i?Object.defineProperty(i,o,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[o]=s,i}var r={ROOT:function(o){return(0,e.default)(n({"carousel-root":!0},o||"",!!o))},CAROUSEL:function(o){return(0,e.default)({carousel:!0,"carousel-slider":o})},WRAPPER:function(o,s){return(0,e.default)({"thumbs-wrapper":!o,"slider-wrapper":o,"axis-horizontal":s==="horizontal","axis-vertical":s!=="horizontal"})},SLIDER:function(o,s){return(0,e.default)({thumbs:!o,slider:o,animated:!s})},ITEM:function(o,s,a){return(0,e.default)({thumb:!o,slide:o,selected:s,previous:a})},ARROW_PREV:function(o){return(0,e.default)({"control-arrow control-prev":!0,"control-disabled":o})},ARROW_NEXT:function(o){return(0,e.default)({"control-arrow control-next":!0,"control-disabled":o})},DOT:function(o){return(0,e.default)({dot:!0,selected:o})}};return Fi.default=r,Fi}var Li={},Ai={},Vl;function Hg(){if(Vl)return Ai;Vl=1,Object.defineProperty(Ai,"__esModule",{value:!0}),Ai.outerWidth=void 0;var e=function(n){var r=n.offsetWidth,i=getComputedStyle(n);return r+=parseInt(i.marginLeft)+parseInt(i.marginRight),r};return Ai.outerWidth=e,Ai}var Hi={},Nl;function zs(){if(Nl)return Hi;Nl=1,Object.defineProperty(Hi,"__esModule",{value:!0}),Hi.default=void 0;var e=function(n,r,i){var o=n===0?n:n+r,s=i==="horizontal"?[o,0,0]:[0,o,0],a="translate3d",l="("+s.join(",")+")";return a+l};return Hi.default=e,Hi}var zi={},$l;function Uc(){if($l)return zi;$l=1,Object.defineProperty(zi,"__esModule",{value:!0}),zi.default=void 0;var e=function(){return window};return zi.default=e,zi}var Bl;function qc(){if(Bl)return Li;Bl=1,Object.defineProperty(Li,"__esModule",{value:!0}),Li.default=void 0;var e=l(so()),t=s(Wc()),n=Hg(),r=s(zs()),i=s(Bc()),o=s(Uc());function s(E){return E&&E.__esModule?E:{default:E}}function a(){if(typeof WeakMap!="function")return null;var E=new WeakMap;return a=function(){return E},E}function l(E){if(E&&E.__esModule)return E;if(E===null||u(E)!=="object"&&typeof E!="function")return{default:E};var k=a();if(k&&k.has(E))return k.get(E);var F={},D=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var C in E)if(Object.prototype.hasOwnProperty.call(E,C)){var I=D?Object.getOwnPropertyDescriptor(E,C):null;I&&(I.get||I.set)?Object.defineProperty(F,C,I):F[C]=E[C]}return F.default=E,k&&k.set(E,F),F}function u(E){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?u=function(F){return typeof F}:u=function(F){return F&&typeof Symbol=="function"&&F.constructor===Symbol&&F!==Symbol.prototype?"symbol":typeof F},u(E)}function c(){return c=Object.assign||function(E){for(var k=1;k<arguments.length;k++){var F=arguments[k];for(var D in F)Object.prototype.hasOwnProperty.call(F,D)&&(E[D]=F[D])}return E},c.apply(this,arguments)}function f(E,k){if(!(E instanceof k))throw new TypeError("Cannot call a class as a function")}function g(E,k){for(var F=0;F<k.length;F++){var D=k[F];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(E,D.key,D)}}function h(E,k,F){return k&&g(E.prototype,k),E}function m(E,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(k&&k.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),k&&p(E,k)}function p(E,k){return p=Object.setPrototypeOf||function(D,C){return D.__proto__=C,D},p(E,k)}function w(E){var k=S();return function(){var D=O(E),C;if(k){var I=O(this).constructor;C=Reflect.construct(D,arguments,I)}else C=D.apply(this,arguments);return b(this,C)}}function b(E,k){return k&&(u(k)==="object"||typeof k=="function")?k:v(E)}function v(E){if(E===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}function S(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function O(E){return O=Object.setPrototypeOf?Object.getPrototypeOf:function(F){return F.__proto__||Object.getPrototypeOf(F)},O(E)}function R(E,k,F){return k in E?Object.defineProperty(E,k,{value:F,enumerable:!0,configurable:!0,writable:!0}):E[k]=F,E}var M=function(k){return k.hasOwnProperty("key")},_=function(E){m(F,E);var k=w(F);function F(D){var C;return f(this,F),C=k.call(this,D),R(v(C),"itemsWrapperRef",void 0),R(v(C),"itemsListRef",void 0),R(v(C),"thumbsRef",void 0),R(v(C),"setItemsWrapperRef",function(I){C.itemsWrapperRef=I}),R(v(C),"setItemsListRef",function(I){C.itemsListRef=I}),R(v(C),"setThumbsRef",function(I,T){C.thumbsRef||(C.thumbsRef=[]),C.thumbsRef[T]=I}),R(v(C),"updateSizes",function(){if(!(!C.props.children||!C.itemsWrapperRef||!C.thumbsRef)){var I=e.Children.count(C.props.children),T=C.itemsWrapperRef.clientWidth,x=C.props.thumbWidth?C.props.thumbWidth:(0,n.outerWidth)(C.thumbsRef[0]),$=Math.floor(T/x),q=$<I,X=q?I-$:0;C.setState(function(oe,Q){return{itemSize:x,visibleItems:$,firstItem:q?C.getFirstItem(Q.selectedItem):0,lastPosition:X,showArrows:q}})}}),R(v(C),"handleClickItem",function(I,T,x){if(!M(x)||x.key==="Enter"){var $=C.props.onSelectItem;typeof $=="function"&&$(I,T)}}),R(v(C),"onSwipeStart",function(){C.setState({swiping:!0})}),R(v(C),"onSwipeEnd",function(){C.setState({swiping:!1})}),R(v(C),"onSwipeMove",function(I){var T=I.x;if(!C.state.itemSize||!C.itemsWrapperRef||!C.state.visibleItems)return!1;var x=0,$=e.Children.count(C.props.children),q=-(C.state.firstItem*100)/C.state.visibleItems,X=Math.max($-C.state.visibleItems,0),oe=-X*100/C.state.visibleItems;q===x&&T>0&&(T=0),q===oe&&T<0&&(T=0);var Q=C.itemsWrapperRef.clientWidth,J=q+100/(Q/T);return C.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(te){C.itemsListRef.style[te]=(0,r.default)(J,"%",C.props.axis)}),!0}),R(v(C),"slideRight",function(I){C.moveTo(C.state.firstItem-(typeof I=="number"?I:1))}),R(v(C),"slideLeft",function(I){C.moveTo(C.state.firstItem+(typeof I=="number"?I:1))}),R(v(C),"moveTo",function(I){I=I<0?0:I,I=I>=C.state.lastPosition?C.state.lastPosition:I,C.setState({firstItem:I})}),C.state={selectedItem:D.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},C}return h(F,[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(C){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==C.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(C){var I=C;return C>=this.state.lastPosition&&(I=this.state.lastPosition),C<this.state.firstItem+this.state.visibleItems&&(I=this.state.firstItem),C<this.state.firstItem&&(I=C),I}},{key:"renderItems",value:function(){var C=this;return this.props.children.map(function(I,T){var x=t.default.ITEM(!1,T===C.state.selectedItem),$={key:T,ref:function(X){return C.setThumbsRef(X,T)},className:x,onClick:C.handleClickItem.bind(C,T,C.props.children[T]),onKeyDown:C.handleClickItem.bind(C,T,C.props.children[T]),"aria-label":"".concat(C.props.labels.item," ").concat(T+1),style:{width:C.props.thumbWidth}};return e.default.createElement("li",c({},$,{role:"button",tabIndex:0}),I)})}},{key:"render",value:function(){var C=this;if(!this.props.children)return null;var I=e.Children.count(this.props.children)>1,T=this.state.showArrows&&this.state.firstItem>0,x=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,$={},q=-this.state.firstItem*(this.state.itemSize||0),X=(0,r.default)(q,"px",this.props.axis),oe=this.props.transitionTime+"ms";return $={WebkitTransform:X,MozTransform:X,MsTransform:X,OTransform:X,transform:X,msTransform:X,WebkitTransitionDuration:oe,MozTransitionDuration:oe,MsTransitionDuration:oe,OTransitionDuration:oe,transitionDuration:oe,msTransitionDuration:oe},e.default.createElement("div",{className:t.default.CAROUSEL(!1)},e.default.createElement("div",{className:t.default.WRAPPER(!1),ref:this.setItemsWrapperRef},e.default.createElement("button",{type:"button",className:t.default.ARROW_PREV(!T),onClick:function(){return C.slideRight()},"aria-label":this.props.labels.leftArrow}),I?e.default.createElement(i.default,{tagName:"ul",className:t.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:$,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):e.default.createElement("ul",{className:t.default.SLIDER(!1,this.state.swiping),ref:function(J){return C.setItemsListRef(J)},style:$},this.renderItems()),e.default.createElement("button",{type:"button",className:t.default.ARROW_NEXT(!x),onClick:function(){return C.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}]),F}(e.Component);return Li.default=_,R(_,"displayName","Thumbs"),R(_,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350}),Li}var Vi={},Wl;function zg(){if(Wl)return Vi;Wl=1,Object.defineProperty(Vi,"__esModule",{value:!0}),Vi.default=void 0;var e=function(){return document};return Vi.default=e,Vi}var En={},Ul;function Gc(){if(Ul)return En;Ul=1,Object.defineProperty(En,"__esModule",{value:!0}),En.setPosition=En.getPosition=En.isKeyboardEvent=En.defaultStatusFormatter=En.noop=void 0;var e=so(),t=n(zs());function n(l){return l&&l.__esModule?l:{default:l}}var r=function(){};En.noop=r;var i=function(u,c){return"".concat(u," of ").concat(c)};En.defaultStatusFormatter=i;var o=function(u){return u?u.hasOwnProperty("key"):!1};En.isKeyboardEvent=o;var s=function(u,c){if(c.infiniteLoop&&++u,u===0)return 0;var f=e.Children.count(c.children);if(c.centerMode&&c.axis==="horizontal"){var g=-u*c.centerSlidePercentage,h=f-1;return u&&(u!==h||c.infiniteLoop)?g+=(100-c.centerSlidePercentage)/2:u===h&&(g+=100-c.centerSlidePercentage),g}return-u*100};En.getPosition=s;var a=function(u,c){var f={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(g){f[g]=(0,t.default)(u,"%",c)}),f};return En.setPosition=a,En}var Bn={},ql;function Vg(){if(ql)return Bn;ql=1,Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.fadeAnimationHandler=Bn.slideStopSwipingHandler=Bn.slideSwipeAnimationHandler=Bn.slideAnimationHandler=void 0;var e=so(),t=r(zs()),n=Gc();function r(f){return f&&f.__esModule?f:{default:f}}function i(f,g){var h=Object.keys(f);if(Object.getOwnPropertySymbols){var m=Object.getOwnPropertySymbols(f);g&&(m=m.filter(function(p){return Object.getOwnPropertyDescriptor(f,p).enumerable})),h.push.apply(h,m)}return h}function o(f){for(var g=1;g<arguments.length;g++){var h=arguments[g]!=null?arguments[g]:{};g%2?i(Object(h),!0).forEach(function(m){s(f,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(f,Object.getOwnPropertyDescriptors(h)):i(Object(h)).forEach(function(m){Object.defineProperty(f,m,Object.getOwnPropertyDescriptor(h,m))})}return f}function s(f,g,h){return g in f?Object.defineProperty(f,g,{value:h,enumerable:!0,configurable:!0,writable:!0}):f[g]=h,f}var a=function(g,h){var m={},p=h.selectedItem,w=p,b=e.Children.count(g.children)-1,v=g.infiniteLoop&&(p<0||p>b);if(v)return w<0?g.centerMode&&g.centerSlidePercentage&&g.axis==="horizontal"?m.itemListStyle=(0,n.setPosition)(-(b+2)*g.centerSlidePercentage-(100-g.centerSlidePercentage)/2,g.axis):m.itemListStyle=(0,n.setPosition)(-(b+2)*100,g.axis):w>b&&(m.itemListStyle=(0,n.setPosition)(0,g.axis)),m;var S=(0,n.getPosition)(p,g),O=(0,t.default)(S,"%",g.axis),R=g.transitionTime+"ms";return m.itemListStyle={WebkitTransform:O,msTransform:O,OTransform:O,transform:O},h.swiping||(m.itemListStyle=o(o({},m.itemListStyle),{},{WebkitTransitionDuration:R,MozTransitionDuration:R,OTransitionDuration:R,transitionDuration:R,msTransitionDuration:R})),m};Bn.slideAnimationHandler=a;var l=function(g,h,m,p){var w={},b=h.axis==="horizontal",v=e.Children.count(h.children),S=0,O=(0,n.getPosition)(m.selectedItem,h),R=h.infiniteLoop?(0,n.getPosition)(v-1,h)-100:(0,n.getPosition)(v-1,h),M=b?g.x:g.y,_=M;O===S&&M>0&&(_=0),O===R&&M<0&&(_=0);var E=O+100/(m.itemSize/_),k=Math.abs(M)>h.swipeScrollTolerance;return h.infiniteLoop&&k&&(m.selectedItem===0&&E>-100?E-=v*100:m.selectedItem===v-1&&E<-v*100&&(E+=v*100)),(!h.preventMovementUntilSwipeScrollTolerance||k||m.swipeMovementStarted)&&(m.swipeMovementStarted||p({swipeMovementStarted:!0}),w.itemListStyle=(0,n.setPosition)(E,h.axis)),k&&!m.cancelClick&&p({cancelClick:!0}),w};Bn.slideSwipeAnimationHandler=l;var u=function(g,h){var m=(0,n.getPosition)(h.selectedItem,g),p=(0,n.setPosition)(m,g.axis);return{itemListStyle:p}};Bn.slideStopSwipingHandler=u;var c=function(g,h){var m=g.transitionTime+"ms",p="ease-in-out",w={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:p,msTransitionTimingFunction:p,MozTransitionTimingFunction:p,WebkitTransitionTimingFunction:p,OTransitionTimingFunction:p};return h.swiping||(w=o(o({},w),{},{WebkitTransitionDuration:m,MozTransitionDuration:m,OTransitionDuration:m,transitionDuration:m,msTransitionDuration:m})),{slideStyle:w,selectedStyle:o(o({},w),{},{opacity:1,position:"relative"}),prevStyle:o({},w)}};return Bn.fadeAnimationHandler=c,Bn}var Gl;function Ng(){if(Gl)return _i;Gl=1,Object.defineProperty(_i,"__esModule",{value:!0}),_i.default=void 0;var e=c(so()),t=l(Bc()),n=l(Wc()),r=l(qc()),i=l(zg()),o=l(Uc()),s=Gc(),a=Vg();function l(D){return D&&D.__esModule?D:{default:D}}function u(){if(typeof WeakMap!="function")return null;var D=new WeakMap;return u=function(){return D},D}function c(D){if(D&&D.__esModule)return D;if(D===null||f(D)!=="object"&&typeof D!="function")return{default:D};var C=u();if(C&&C.has(D))return C.get(D);var I={},T=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var x in D)if(Object.prototype.hasOwnProperty.call(D,x)){var $=T?Object.getOwnPropertyDescriptor(D,x):null;$&&($.get||$.set)?Object.defineProperty(I,x,$):I[x]=D[x]}return I.default=D,C&&C.set(D,I),I}function f(D){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?f=function(I){return typeof I}:f=function(I){return I&&typeof Symbol=="function"&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},f(D)}function g(){return g=Object.assign||function(D){for(var C=1;C<arguments.length;C++){var I=arguments[C];for(var T in I)Object.prototype.hasOwnProperty.call(I,T)&&(D[T]=I[T])}return D},g.apply(this,arguments)}function h(D,C){var I=Object.keys(D);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(D);C&&(T=T.filter(function(x){return Object.getOwnPropertyDescriptor(D,x).enumerable})),I.push.apply(I,T)}return I}function m(D){for(var C=1;C<arguments.length;C++){var I=arguments[C]!=null?arguments[C]:{};C%2?h(Object(I),!0).forEach(function(T){k(D,T,I[T])}):Object.getOwnPropertyDescriptors?Object.defineProperties(D,Object.getOwnPropertyDescriptors(I)):h(Object(I)).forEach(function(T){Object.defineProperty(D,T,Object.getOwnPropertyDescriptor(I,T))})}return D}function p(D,C){if(!(D instanceof C))throw new TypeError("Cannot call a class as a function")}function w(D,C){for(var I=0;I<C.length;I++){var T=C[I];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(D,T.key,T)}}function b(D,C,I){return C&&w(D.prototype,C),D}function v(D,C){if(typeof C!="function"&&C!==null)throw new TypeError("Super expression must either be null or a function");D.prototype=Object.create(C&&C.prototype,{constructor:{value:D,writable:!0,configurable:!0}}),C&&S(D,C)}function S(D,C){return S=Object.setPrototypeOf||function(T,x){return T.__proto__=x,T},S(D,C)}function O(D){var C=_();return function(){var T=E(D),x;if(C){var $=E(this).constructor;x=Reflect.construct(T,arguments,$)}else x=T.apply(this,arguments);return R(this,x)}}function R(D,C){return C&&(f(C)==="object"||typeof C=="function")?C:M(D)}function M(D){if(D===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return D}function _(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function E(D){return E=Object.setPrototypeOf?Object.getPrototypeOf:function(I){return I.__proto__||Object.getPrototypeOf(I)},E(D)}function k(D,C,I){return C in D?Object.defineProperty(D,C,{value:I,enumerable:!0,configurable:!0,writable:!0}):D[C]=I,D}var F=function(D){v(I,D);var C=O(I);function I(T){var x;p(this,I),x=C.call(this,T),k(M(x),"thumbsRef",void 0),k(M(x),"carouselWrapperRef",void 0),k(M(x),"listRef",void 0),k(M(x),"itemsRef",void 0),k(M(x),"timer",void 0),k(M(x),"animationHandler",void 0),k(M(x),"setThumbsRef",function(q){x.thumbsRef=q}),k(M(x),"setCarouselWrapperRef",function(q){x.carouselWrapperRef=q}),k(M(x),"setListRef",function(q){x.listRef=q}),k(M(x),"setItemsRef",function(q,X){x.itemsRef||(x.itemsRef=[]),x.itemsRef[X]=q}),k(M(x),"autoPlay",function(){e.Children.count(x.props.children)<=1||(x.clearAutoPlay(),x.props.autoPlay&&(x.timer=setTimeout(function(){x.increment()},x.props.interval)))}),k(M(x),"clearAutoPlay",function(){x.timer&&clearTimeout(x.timer)}),k(M(x),"resetAutoPlay",function(){x.clearAutoPlay(),x.autoPlay()}),k(M(x),"stopOnHover",function(){x.setState({isMouseEntered:!0},x.clearAutoPlay)}),k(M(x),"startOnLeave",function(){x.setState({isMouseEntered:!1},x.autoPlay)}),k(M(x),"isFocusWithinTheCarousel",function(){return x.carouselWrapperRef?!!((0,i.default)().activeElement===x.carouselWrapperRef||x.carouselWrapperRef.contains((0,i.default)().activeElement)):!1}),k(M(x),"navigateWithKeyboard",function(q){if(x.isFocusWithinTheCarousel()){var X=x.props.axis,oe=X==="horizontal",Q={ArrowUp:38,ArrowRight:39,ArrowDown:40,ArrowLeft:37},J=oe?Q.ArrowRight:Q.ArrowDown,te=oe?Q.ArrowLeft:Q.ArrowUp;J===q.keyCode?x.increment():te===q.keyCode&&x.decrement()}}),k(M(x),"updateSizes",function(){if(!(!x.state.initialized||!x.itemsRef||x.itemsRef.length===0)){var q=x.props.axis==="horizontal",X=x.itemsRef[0];if(X){var oe=q?X.clientWidth:X.clientHeight;x.setState({itemSize:oe}),x.thumbsRef&&x.thumbsRef.updateSizes()}}}),k(M(x),"setMountState",function(){x.setState({hasMount:!0}),x.updateSizes()}),k(M(x),"handleClickItem",function(q,X){if(e.Children.count(x.props.children)!==0){if(x.state.cancelClick){x.setState({cancelClick:!1});return}x.props.onClickItem(q,X),q!==x.state.selectedItem&&x.setState({selectedItem:q})}}),k(M(x),"handleOnChange",function(q,X){e.Children.count(x.props.children)<=1||x.props.onChange(q,X)}),k(M(x),"handleClickThumb",function(q,X){x.props.onClickThumb(q,X),x.moveTo(q)}),k(M(x),"onSwipeStart",function(q){x.setState({swiping:!0}),x.props.onSwipeStart(q)}),k(M(x),"onSwipeEnd",function(q){x.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),x.props.onSwipeEnd(q),x.clearAutoPlay(),x.state.autoPlay&&x.autoPlay()}),k(M(x),"onSwipeMove",function(q,X){x.props.onSwipeMove(X);var oe=x.props.swipeAnimationHandler(q,x.props,x.state,x.setState.bind(M(x)));return x.setState(m({},oe)),!!Object.keys(oe).length}),k(M(x),"decrement",function(){var q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;x.moveTo(x.state.selectedItem-(typeof q=="number"?q:1))}),k(M(x),"increment",function(){var q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;x.moveTo(x.state.selectedItem+(typeof q=="number"?q:1))}),k(M(x),"moveTo",function(q){if(typeof q=="number"){var X=e.Children.count(x.props.children)-1;q<0&&(q=x.props.infiniteLoop?X:0),q>X&&(q=x.props.infiniteLoop?0:X),x.selectItem({selectedItem:q}),x.state.autoPlay&&x.state.isMouseEntered===!1&&x.resetAutoPlay()}}),k(M(x),"onClickNext",function(){x.increment(1)}),k(M(x),"onClickPrev",function(){x.decrement(1)}),k(M(x),"onSwipeForward",function(){x.increment(1),x.props.emulateTouch&&x.setState({cancelClick:!0})}),k(M(x),"onSwipeBackwards",function(){x.decrement(1),x.props.emulateTouch&&x.setState({cancelClick:!0})}),k(M(x),"changeItem",function(q){return function(X){(!(0,s.isKeyboardEvent)(X)||X.key==="Enter")&&x.moveTo(q)}}),k(M(x),"selectItem",function(q){x.setState(m({previousItem:x.state.selectedItem},q),function(){x.setState(x.animationHandler(x.props,x.state))}),x.handleOnChange(q.selectedItem,e.Children.toArray(x.props.children)[q.selectedItem])}),k(M(x),"getInitialImage",function(){var q=x.props.selectedItem,X=x.itemsRef&&x.itemsRef[q],oe=X&&X.getElementsByTagName("img")||[];return oe[0]}),k(M(x),"getVariableItemHeight",function(q){var X=x.itemsRef&&x.itemsRef[q];if(x.state.hasMount&&X&&X.children.length){var oe=X.children[0].getElementsByTagName("img")||[];if(oe.length>0){var Q=oe[0];if(!Q.complete){var J=function le(){x.forceUpdate(),Q.removeEventListener("load",le)};Q.addEventListener("load",J)}}var te=oe[0]||X.children[0],ae=te.clientHeight;return ae>0?ae:null}return null});var $={initialized:!1,previousItem:T.selectedItem,selectedItem:T.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:T.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return x.animationHandler=typeof T.animationHandler=="function"&&T.animationHandler||T.animationHandler==="fade"&&a.fadeAnimationHandler||a.slideAnimationHandler,x.state=m(m({},$),x.animationHandler(T,$)),x}return b(I,[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(x,$){!x.children&&this.props.children&&!this.state.initialized&&this.setupCarousel(),!x.autoFocus&&this.props.autoFocus&&this.forceFocus(),$.swiping&&!this.state.swiping&&this.setState(m({},this.props.stopSwipingHandler(this.props,this.state))),(x.selectedItem!==this.props.selectedItem||x.centerMode!==this.props.centerMode)&&(this.updateSizes(),this.moveTo(this.props.selectedItem)),x.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var x=this;this.bindEvents(),this.state.autoPlay&&e.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},function(){var $=x.getInitialImage();$&&!$.complete?$.addEventListener("load",x.setMountState):x.setMountState()})}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var x=this.carouselWrapperRef;this.props.stopOnHover&&x&&(x.addEventListener("mouseenter",this.stopOnHover),x.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var x=this.carouselWrapperRef;this.props.stopOnHover&&x&&(x.removeEventListener("mouseenter",this.stopOnHover),x.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,i.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var x=this.getInitialImage();x&&x.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,i.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var x;(x=this.carouselWrapperRef)===null||x===void 0||x.focus()}},{key:"renderItems",value:function(x){var $=this;return this.props.children?e.Children.map(this.props.children,function(q,X){var oe=X===$.state.selectedItem,Q=X===$.state.previousItem,J=oe&&$.state.selectedStyle||Q&&$.state.prevStyle||$.state.slideStyle||{};$.props.centerMode&&$.props.axis==="horizontal"&&(J=m(m({},J),{},{minWidth:$.props.centerSlidePercentage+"%"})),$.state.swiping&&$.state.swipeMovementStarted&&(J=m(m({},J),{},{pointerEvents:"none"}));var te={ref:function(le){return $.setItemsRef(le,X)},key:"itemKey"+X+(x?"clone":""),className:n.default.ITEM(!0,X===$.state.selectedItem,X===$.state.previousItem),onClick:$.handleClickItem.bind($,X,q),style:J};return e.default.createElement("li",te,$.props.renderItem(q,{isSelected:X===$.state.selectedItem,isPrevious:X===$.state.previousItem}))}):[]}},{key:"renderControls",value:function(){var x=this,$=this.props,q=$.showIndicators,X=$.labels,oe=$.renderIndicator,Q=$.children;return q?e.default.createElement("ul",{className:"control-dots"},e.Children.map(Q,function(J,te){return oe&&oe(x.changeItem(te),te===x.state.selectedItem,te,X.item)})):null}},{key:"renderStatus",value:function(){return this.props.showStatus?e.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,e.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return!this.props.showThumbs||!this.props.children||e.Children.count(this.props.children)===0?null:e.default.createElement(r.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children))}},{key:"render",value:function(){var x=this;if(!this.props.children||e.Children.count(this.props.children)===0)return null;var $=this.props.swipeable&&e.Children.count(this.props.children)>1,q=this.props.axis==="horizontal",X=this.props.showArrows&&e.Children.count(this.props.children)>1,oe=X&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,Q=X&&(this.state.selectedItem<e.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,J=this.renderItems(!0),te=J.shift(),ae=J.pop(),le={className:n.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},fe={};if(q){if(le.onSwipeLeft=this.onSwipeForward,le.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var re=this.getVariableItemHeight(this.state.selectedItem);fe.height=re||"auto"}}else le.onSwipeUp=this.props.verticalSwipe==="natural"?this.onSwipeBackwards:this.onSwipeForward,le.onSwipeDown=this.props.verticalSwipe==="natural"?this.onSwipeForward:this.onSwipeBackwards,le.style=m(m({},le.style),{},{height:this.state.itemSize}),fe.height=this.state.itemSize;return e.default.createElement("div",{"aria-label":this.props.ariaLabel,className:n.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},e.default.createElement("div",{className:n.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,oe,this.props.labels.leftArrow),e.default.createElement("div",{className:n.default.WRAPPER(!0,this.props.axis),style:fe},$?e.default.createElement(t.default,g({tagName:"ul",innerRef:this.setListRef},le,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&ae,this.renderItems(),this.props.infiniteLoop&&te):e.default.createElement("ul",{className:n.default.SLIDER(!0,this.state.swiping),ref:function(P){return x.setListRef(P)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&ae,this.renderItems(),this.props.infiniteLoop&&te)),this.props.renderArrowNext(this.onClickNext,Q,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}]),I}(e.default.Component);return _i.default=F,k(F,"displayName","Carousel"),k(F,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:s.noop,onClickThumb:s.noop,onChange:s.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(C,I,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_PREV(!I),onClick:C})},renderArrowNext:function(C,I,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_NEXT(!I),onClick:C})},renderIndicator:function(C,I,T,x){return e.default.createElement("li",{className:n.default.DOT(I),onClick:C,onKeyDown:C,value:T,key:T,role:"button",tabIndex:0,"aria-label":"".concat(x," ").concat(T+1)})},renderItem:function(C){return C},renderThumbs:function(C){var I=e.Children.map(C,function(T){var x=T;if(T.type!=="img"&&(x=e.Children.toArray(T.props.children).find(function($){return $.type==="img"})),!!x)return x});return I.filter(function(T){return T}).length===0?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):I},statusFormatter:s.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:a.slideSwipeAnimationHandler,stopSwipingHandler:a.slideStopSwipingHandler}),_i}var Yl={},Xl;function $g(){return Xl||(Xl=1),Yl}var jl;function Bg(){return jl||(jl=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Carousel",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"CarouselProps",{enumerable:!0,get:function(){return n.CarouselProps}}),Object.defineProperty(e,"Thumbs",{enumerable:!0,get:function(){return r.default}});var t=i(Ng()),n=$g(),r=i(qc());function i(o){return o&&o.__esModule?o:{default:o}}}(Ga)),Ga}var Wg=Bg(),Ya,Kl;function Ug(){if(Kl)return Ya;Kl=1;var e=lh(),t=function(){return e.Date.now()};return Ya=t,Ya}var Xa,Zl;function Yc(){if(Zl)return Xa;Zl=1;var e=kc(),t=Ug(),n=Mc(),r="Expected a function",i=Math.max,o=Math.min;function s(a,l,u){var c,f,g,h,m,p,w=0,b=!1,v=!1,S=!0;if(typeof a!="function")throw new TypeError(r);l=n(l)||0,e(u)&&(b=!!u.leading,v="maxWait"in u,g=v?i(n(u.maxWait)||0,l):g,S="trailing"in u?!!u.trailing:S);function O(I){var T=c,x=f;return c=f=void 0,w=I,h=a.apply(x,T),h}function R(I){return w=I,m=setTimeout(E,l),b?O(I):h}function M(I){var T=I-p,x=I-w,$=l-T;return v?o($,g-x):$}function _(I){var T=I-p,x=I-w;return p===void 0||T>=l||T<0||v&&x>=g}function E(){var I=t();if(_(I))return k(I);m=setTimeout(E,M(I))}function k(I){return m=void 0,S&&c?O(I):(c=f=void 0,h)}function F(){m!==void 0&&clearTimeout(m),w=0,c=p=f=m=void 0}function D(){return m===void 0?h:k(t())}function C(){var I=t(),T=_(I);if(c=arguments,f=this,p=I,T){if(m===void 0)return R(p);if(v)return clearTimeout(m),m=setTimeout(E,l),O(p)}return m===void 0&&(m=setTimeout(E,l)),h}return C.cancel=F,C.flush=D,C}return Xa=s,Xa}var qg=Yc();const Xc=ar(qg);function dn(e,t,n,r,i=!1){const o=d.useRef();o.current=t,d.useEffect(()=>{if(n===null||n.addEventListener===void 0)return;const s=n,a=l=>{o.current?.call(s,l)};return s.addEventListener(e,a,{passive:r,capture:i}),()=>{s.removeEventListener(e,a,{capture:i})}},[e,n,r,i])}function br(e,t){return e===void 0?void 0:t}const Gg=Math.PI;function Jl(e){return e*Gg/180}const jc=(e,t,n)=>({x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}),Kc=(e,t,n,r,i)=>{switch(e){case"left":return Math.floor(t)+r+i/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-r-i/2}},Zc=(e,t,n)=>Math.min(e,t-n*2),Jc=(e,t,n)=>n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2,Vs=e=>{const t=e.fgColor??"currentColor";return d.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},d.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),d.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},Yg=e=>{const t=e.fgColor??"currentColor";return d.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},d.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};function Xg(e,t,n){const[r,i]=d.useState(e),o=d.useRef(!0);d.useEffect(()=>()=>{o.current=!1},[]);const s=d.useRef(Xc(a=>{o.current&&i(a)},n));return d.useLayoutEffect(()=>{o.current&&s.current(()=>e())},t),r}const jg="֑-߿יִ-﷽ﹰ-ﻼ",Kg="A-Za-zÀ-ÖØ-öø-ʸ̀-֐ࠀ-῿‎Ⰰ-﬜︀-﹯﻽-￿",Zg=new RegExp("^[^"+Kg+"]*["+jg+"]");function Ns(e){return Zg.test(e)?"rtl":"not-rtl"}let Lo;function ws(){if(typeof document>"u")return 0;if(Lo!==void 0)return Lo;const e=document.createElement("p");e.style.width="100%",e.style.height="200px";const t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Lo=n-r,Lo}const Ar=Symbol();function Jg(e){const t=d.useRef([Ar,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;const[n,r]=d.useState(e),[,i]=d.useState(),o=d.useCallback(a=>{const l=t.current[0];l!==Ar&&(a=typeof a=="function"?a(l):a,a===l)||(l!==Ar&&i({}),r(u=>typeof a=="function"?a(l===Ar?u:l):a),t.current[0]=Ar)},[]),s=d.useCallback(()=>{t.current[0]=Ar,i({})},[]);return[t.current[0]===Ar?n:t.current[0],o,s]}function Qc(e){if(e.length===0)return"";let t=0,n=0;for(const r of e){if(n+=r.length,n>1e4)break;t++}return e.slice(0,t).join(", ")}function Qg(e){const t=d.useRef(e);return hi(e,t.current)||(t.current=e),t.current}const em=e=>{const{urls:t,canWrite:n,onEditClick:r,renderImage:i}=e,o=t.filter(a=>a!=="");if(o.length===0)return null;const s=o.length>1;return d.createElement(Lg,{"data-testid":"GDG-default-image-overlay-editor"},d.createElement(Wg.Carousel,{showArrows:s,showThumbs:!1,swipeable:s,emulateTouch:s,infiniteLoop:s},o.map(a=>{const l=i?.(a)??d.createElement("img",{draggable:!1,src:a});return d.createElement("div",{className:"gdg-centering-container",key:a},l)})),n&&r&&d.createElement("button",{className:"gdg-edit-icon",onClick:r},d.createElement(Vs,null)))};function ed(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Br=ed();function tm(e){Br=e}const td=/[&<>"']/,nm=new RegExp(td.source,"g"),nd=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,rm=new RegExp(nd.source,"g"),im={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ql=e=>im[e];function vn(e,t){if(t){if(td.test(e))return e.replace(nm,Ql)}else if(nd.test(e))return e.replace(rm,Ql);return e}const om=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function rd(e){return e.replace(om,(t,n)=>(n=n.toLowerCase(),n==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):""))}const am=/(^|[^\[])\^/g;function Ht(e,t){e=typeof e=="string"?e:e.source,t=t||"";const n={replace:(r,i)=>(i=i.source||i,i=i.replace(am,"$1"),e=e.replace(r,i),n),getRegex:()=>new RegExp(e,t)};return n}const sm=/[^\w:]/g,lm=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function eu(e,t,n){if(e){let r;try{r=decodeURIComponent(rd(n)).replace(sm,"").toLowerCase()}catch{return null}if(r.indexOf("javascript:")===0||r.indexOf("vbscript:")===0||r.indexOf("data:")===0)return null}t&&!lm.test(n)&&(n=fm(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const Ao={},um=/^[^:]+:\/*[^/]*$/,cm=/^([^:]+:)[\s\S]*$/,dm=/^([^:]+:\/*[^/]*)[\s\S]*$/;function fm(e,t){Ao[" "+e]||(um.test(e)?Ao[" "+e]=e+"/":Ao[" "+e]=jo(e,"/",!0)),e=Ao[" "+e];const n=e.indexOf(":")===-1;return t.substring(0,2)==="//"?n?t:e.replace(cm,"$1")+t:t.charAt(0)==="/"?n?t:e.replace(dm,"$1")+t:e+t}const na={exec:function(){}};function tu(e,t){const n=e.replace(/\|/g,(o,s,a)=>{let l=!1,u=s;for(;--u>=0&&a[u]==="\\";)l=!l;return l?"|":" |"}),r=n.split(/ \|/);let i=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(/\\\|/g,"|");return r}function jo(e,t,n){const r=e.length;if(r===0)return"";let i=0;for(;i<r;){const o=e.charAt(r-i-1);if(o===t&&!n)i++;else if(o!==t&&n)i++;else break}return e.slice(0,r-i)}function hm(e,t){if(e.indexOf(t[1])===-1)return-1;const n=e.length;let r=0,i=0;for(;i<n;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return-1}function gm(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function nu(e,t){if(t<1)return"";let n="";for(;t>1;)t&1&&(n+=e),t>>=1,e+=e;return n+e}function ru(e,t,n,r){const i=t.href,o=t.title?vn(t.title):null,s=e[1].replace(/\\([\[\]])/g,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;const a={type:"link",raw:n,href:i,title:o,text:s,tokens:r.inlineTokens(s)};return r.state.inLink=!1,a}return{type:"image",raw:n,href:i,title:o,text:vn(s)}}function mm(e,t){const n=e.match(/^(\s+)(?:```)/);if(n===null)return t;const r=n[1];return t.split(`
`).map(i=>{const o=i.match(/^\s+/);if(o===null)return i;const[s]=o;return s.length>=r.length?i.slice(r.length):i}).join(`
`)}class $s{constructor(t){this.options=t||Br}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:jo(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],i=mm(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline._escapes,"$1"):n[2],text:i}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const i=jo(r,"#");(this.options.pedantic||!i||/ $/.test(i))&&(r=i.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=n[0].replace(/^ *>[ \t]?/gm,""),i=this.lexer.state.top;this.lexer.state.top=!0;const o=this.lexer.blockTokens(r);return this.lexer.state.top=i,{type:"blockquote",raw:n[0],tokens:o,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r,i,o,s,a,l,u,c,f,g,h,m,p=n[1].trim();const w=p.length>1,b={type:"list",raw:"",ordered:w,start:w?+p.slice(0,-1):"",loose:!1,items:[]};p=w?`\\d{1,9}\\${p.slice(-1)}`:`\\${p}`,this.options.pedantic&&(p=w?p:"[*+-]");const v=new RegExp(`^( {0,3}${p})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;t&&(m=!1,!(!(n=v.exec(t))||this.rules.block.hr.test(t)));){if(r=n[0],t=t.substring(r.length),c=n[2].split(`
`,1)[0].replace(/^\t+/,O=>" ".repeat(3*O.length)),f=t.split(`
`,1)[0],this.options.pedantic?(s=2,h=c.trimLeft()):(s=n[2].search(/[^ ]/),s=s>4?1:s,h=c.slice(s),s+=n[1].length),l=!1,!c&&/^ *$/.test(f)&&(r+=f+`
`,t=t.substring(f.length+1),m=!0),!m){const O=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),R=new RegExp(`^ {0,${Math.min(3,s-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),M=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:\`\`\`|~~~)`),_=new RegExp(`^ {0,${Math.min(3,s-1)}}#`);for(;t&&(g=t.split(`
`,1)[0],f=g,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(M.test(f)||_.test(f)||O.test(f)||R.test(t)));){if(f.search(/[^ ]/)>=s||!f.trim())h+=`
`+f.slice(s);else{if(l||c.search(/[^ ]/)>=4||M.test(c)||_.test(c)||R.test(c))break;h+=`
`+f}!l&&!f.trim()&&(l=!0),r+=g+`
`,t=t.substring(g.length+1),c=f.slice(s)}}b.loose||(u?b.loose=!0:/\n *\n *$/.test(r)&&(u=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(h),i&&(o=i[0]!=="[ ] ",h=h.replace(/^\[[ xX]\] +/,""))),b.items.push({type:"list_item",raw:r,task:!!i,checked:o,loose:!1,text:h}),b.raw+=r}b.items[b.items.length-1].raw=r.trimRight(),b.items[b.items.length-1].text=h.trimRight(),b.raw=b.raw.trimRight();const S=b.items.length;for(a=0;a<S;a++)if(this.lexer.state.top=!1,b.items[a].tokens=this.lexer.blockTokens(b.items[a].text,[]),!b.loose){const O=b.items[a].tokens.filter(M=>M.type==="space"),R=O.length>0&&O.some(M=>/\n.*\n/.test(M.raw));b.loose=R}if(b.loose)for(a=0;a<S;a++)b.items[a].loose=!0;return b}}html(t){const n=this.rules.block.html.exec(t);if(n){const r={type:"html",raw:n[0],pre:!this.options.sanitizer&&(n[1]==="pre"||n[1]==="script"||n[1]==="style"),text:n[0]};if(this.options.sanitize){const i=this.options.sanitizer?this.options.sanitizer(n[0]):vn(n[0]);r.type="paragraph",r.text=i,r.tokens=this.lexer.inline(i)}return r}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),i=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",o=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline._escapes,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:i,title:o}}}table(t){const n=this.rules.block.table.exec(t);if(n){const r={type:"table",header:tu(n[1]).map(i=>({text:i})),align:n[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(r.header.length===r.align.length){r.raw=n[0];let i=r.align.length,o,s,a,l;for(o=0;o<i;o++)/^ *-+: *$/.test(r.align[o])?r.align[o]="right":/^ *:-+: *$/.test(r.align[o])?r.align[o]="center":/^ *:-+ *$/.test(r.align[o])?r.align[o]="left":r.align[o]=null;for(i=r.rows.length,o=0;o<i;o++)r.rows[o]=tu(r.rows[o],r.header.length).map(u=>({text:u}));for(i=r.header.length,s=0;s<i;s++)r.header[s].tokens=this.lexer.inline(r.header[s].text);for(i=r.rows.length,s=0;s<i;s++)for(l=r.rows[s],a=0;a<l.length;a++)l[a].tokens=this.lexer.inline(l[a].text);return r}}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:vn(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):vn(n[0]):n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const s=jo(r.slice(0,-1),"\\");if((r.length-s.length)%2===0)return}else{const s=hm(n[2],"()");if(s>-1){const l=(n[0].indexOf("!")===0?5:4)+n[1].length+s;n[2]=n[2].substring(0,s),n[0]=n[0].substring(0,l).trim(),n[3]=""}}let i=n[2],o="";if(this.options.pedantic){const s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);s&&(i=s[1],o=s[3])}else o=n[3]?n[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(this.options.pedantic&&!/>$/.test(r)?i=i.slice(1):i=i.slice(1,-1)),ru(n,{href:i&&i.replace(this.rules.inline._escapes,"$1"),title:o&&o.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){let i=(r[2]||r[1]).replace(/\s+/g," ");if(i=n[i.toLowerCase()],!i){const o=r[0].charAt(0);return{type:"text",raw:o,text:o}}return ru(r,i,r[0],this.lexer)}}emStrong(t,n,r=""){let i=this.rules.inline.emStrong.lDelim.exec(t);if(!i||i[3]&&r.match(/[\p{L}\p{N}]/u))return;const o=i[1]||i[2]||"";if(!o||o&&(r===""||this.rules.inline.punctuation.exec(r))){const s=i[0].length-1;let a,l,u=s,c=0;const f=i[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(f.lastIndex=0,n=n.slice(-1*t.length+s);(i=f.exec(n))!=null;){if(a=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!a)continue;if(l=a.length,i[3]||i[4]){u+=l;continue}else if((i[5]||i[6])&&s%3&&!((s+l)%3)){c+=l;continue}if(u-=l,u>0)continue;l=Math.min(l,l+u+c);const g=t.slice(0,s+i.index+(i[0].length-a.length)+l);if(Math.min(s,l)%2){const m=g.slice(1,-1);return{type:"em",raw:g,text:m,tokens:this.lexer.inlineTokens(m)}}const h=g.slice(2,-2);return{type:"strong",raw:g,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const i=/[^ ]/.test(r),o=/^ /.test(r)&&/ $/.test(r);return i&&o&&(r=r.substring(1,r.length-1)),r=vn(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t,n){const r=this.rules.inline.autolink.exec(t);if(r){let i,o;return r[2]==="@"?(i=vn(this.options.mangle?n(r[1]):r[1]),o="mailto:"+i):(i=vn(r[1]),o=i),{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}url(t,n){let r;if(r=this.rules.inline.url.exec(t)){let i,o;if(r[2]==="@")i=vn(this.options.mangle?n(r[0]):r[0]),o="mailto:"+i;else{let s;do s=r[0],r[0]=this.rules.inline._backpedal.exec(r[0])[0];while(s!==r[0]);i=vn(r[0]),r[1]==="www."?o="http://"+r[0]:o=r[0]}return{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t,n){const r=this.rules.inline.text.exec(t);if(r){let i;return this.lexer.state.inRawBlock?i=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):vn(r[0]):r[0]:i=vn(this.options.smartypants?n(r[0]):r[0]),{type:"text",raw:r[0],text:i}}}}const He={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:na,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};He._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;He._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;He.def=Ht(He.def).replace("label",He._label).replace("title",He._title).getRegex();He.bullet=/(?:[*+-]|\d{1,9}[.)])/;He.listItemStart=Ht(/^( *)(bull) */).replace("bull",He.bullet).getRegex();He.list=Ht(He.list).replace(/bull/g,He.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+He.def.source+")").getRegex();He._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";He._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;He.html=Ht(He.html,"i").replace("comment",He._comment).replace("tag",He._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();He.paragraph=Ht(He._paragraph).replace("hr",He.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",He._tag).getRegex();He.blockquote=Ht(He.blockquote).replace("paragraph",He.paragraph).getRegex();He.normal={...He};He.gfm={...He.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};He.gfm.table=Ht(He.gfm.table).replace("hr",He.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",He._tag).getRegex();He.gfm.paragraph=Ht(He._paragraph).replace("hr",He.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",He.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",He._tag).getRegex();He.pedantic={...He.normal,html:Ht(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",He._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:na,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Ht(He.normal._paragraph).replace("hr",He.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",He.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const Ce={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:na,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:na,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};Ce._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~";Ce.punctuation=Ht(Ce.punctuation).replace(/punctuation/g,Ce._punctuation).getRegex();Ce.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g;Ce.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g;Ce._comment=Ht(He._comment).replace("(?:-->|$)","-->").getRegex();Ce.emStrong.lDelim=Ht(Ce.emStrong.lDelim).replace(/punct/g,Ce._punctuation).getRegex();Ce.emStrong.rDelimAst=Ht(Ce.emStrong.rDelimAst,"g").replace(/punct/g,Ce._punctuation).getRegex();Ce.emStrong.rDelimUnd=Ht(Ce.emStrong.rDelimUnd,"g").replace(/punct/g,Ce._punctuation).getRegex();Ce._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g;Ce._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;Ce._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;Ce.autolink=Ht(Ce.autolink).replace("scheme",Ce._scheme).replace("email",Ce._email).getRegex();Ce._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;Ce.tag=Ht(Ce.tag).replace("comment",Ce._comment).replace("attribute",Ce._attribute).getRegex();Ce._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;Ce._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;Ce._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;Ce.link=Ht(Ce.link).replace("label",Ce._label).replace("href",Ce._href).replace("title",Ce._title).getRegex();Ce.reflink=Ht(Ce.reflink).replace("label",Ce._label).replace("ref",He._label).getRegex();Ce.nolink=Ht(Ce.nolink).replace("ref",He._label).getRegex();Ce.reflinkSearch=Ht(Ce.reflinkSearch,"g").replace("reflink",Ce.reflink).replace("nolink",Ce.nolink).getRegex();Ce.normal={...Ce};Ce.pedantic={...Ce.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:Ht(/^!?\[(label)\]\((.*?)\)/).replace("label",Ce._label).getRegex(),reflink:Ht(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ce._label).getRegex()};Ce.gfm={...Ce.normal,escape:Ht(Ce.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};Ce.gfm.url=Ht(Ce.gfm.url,"i").replace("email",Ce.gfm._extended_email).getRegex();Ce.breaks={...Ce.gfm,br:Ht(Ce.br).replace("{2,}","*").getRegex(),text:Ht(Ce.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};function pm(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function iu(e){let t="",n,r;const i=e.length;for(n=0;n<i;n++)r=e.charCodeAt(n),Math.random()>.5&&(r="x"+r.toString(16)),t+="&#"+r+";";return t}class Sr{constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Br,this.options.tokenizer=this.options.tokenizer||new $s,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:He.normal,inline:Ce.normal};this.options.pedantic?(n.block=He.pedantic,n.inline=Ce.pedantic):this.options.gfm&&(n.block=He.gfm,this.options.breaks?n.inline=Ce.breaks:n.inline=Ce.gfm),this.tokenizer.rules=n}static get rules(){return{block:He,inline:Ce}}static lex(t,n){return new Sr(n).lex(t)}static lexInline(t,n){return new Sr(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);let n;for(;n=this.inlineQueue.shift();)this.inlineTokens(n.src,n.tokens);return this.tokens}blockTokens(t,n=[]){this.options.pedantic?t=t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t=t.replace(/^( *)(\t+)/gm,(a,l,u)=>l+"    ".repeat(u.length));let r,i,o,s;for(;t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>(r=a.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.space(t)){t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(r=this.tokenizer.fences(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let u;this.options.extensions.startBlock.forEach(function(c){u=c.call({lexer:this},l),typeof u=="number"&&u>=0&&(a=Math.min(a,u))}),a<1/0&&a>=0&&(o=t.substring(0,a+1))}if(this.state.top&&(r=this.tokenizer.paragraph(o))){i=n[n.length-1],s&&i.type==="paragraph"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r),s=o.length!==t.length,t=t.substring(r.raw.length);continue}if(r=this.tokenizer.text(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&i.type==="text"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,i,o,s=t,a,l,u;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(s))!=null;)c.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,a.index)+"["+nu("a",a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.blockSkip.exec(s))!=null;)s=s.slice(0,a.index)+"["+nu("a",a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(a=this.tokenizer.rules.inline.escapedEmSt.exec(s))!=null;)s=s.slice(0,a.index+a[0].length-2)+"++"+s.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;t;)if(l||(u=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(r=c.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.escape(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.tag(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.link(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.emStrong(t,s,u)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.codespan(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.br(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.del(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.autolink(t,iu)){t=t.substring(r.raw.length),n.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(t,iu))){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const f=t.slice(1);let g;this.options.extensions.startInline.forEach(function(h){g=h.call({lexer:this},f),typeof g=="number"&&g>=0&&(c=Math.min(c,g))}),c<1/0&&c>=0&&(o=t.substring(0,c+1))}if(r=this.tokenizer.inlineText(o,pm)){t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(u=r.raw.slice(-1)),l=!0,i=n[n.length-1],i&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return n}}class Bs{constructor(t){this.options=t||Br}code(t,n,r){const i=(n||"").match(/\S*/)[0];if(this.options.highlight){const o=this.options.highlight(t,i);o!=null&&o!==t&&(r=!0,t=o)}return t=t.replace(/\n$/,"")+`
`,i?'<pre><code class="'+this.options.langPrefix+vn(i)+'">'+(r?t:vn(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:vn(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t){return t}heading(t,n,r,i){if(this.options.headerIds){const o=this.options.headerPrefix+i.slug(r);return`<h${n} id="${o}">${t}</h${n}>
`}return`<h${n}>${t}</h${n}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(t,n,r){const i=n?"ol":"ul",o=n&&r!==1?' start="'+r+'"':"";return"<"+i+o+`>
`+t+"</"+i+`>
`}listitem(t){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){if(t=eu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i='<a href="'+t+'"';return n&&(i+=' title="'+n+'"'),i+=">"+r+"</a>",i}image(t,n,r){if(t=eu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i=`<img src="${t}" alt="${r}"`;return n&&(i+=` title="${n}"`),i+=this.options.xhtml?"/>":">",i}text(t){return t}}class id{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class od{constructor(){this.seen={}}serialize(t){return t.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(t,n){let r=t,i=0;if(this.seen.hasOwnProperty(r)){i=this.seen[t];do i++,r=t+"-"+i;while(this.seen.hasOwnProperty(r))}return n||(this.seen[t]=i,this.seen[r]=0),r}slug(t,n={}){const r=this.serialize(t);return this.getNextSafeSlug(r,n.dryrun)}}class xr{constructor(t){this.options=t||Br,this.options.renderer=this.options.renderer||new Bs,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new id,this.slugger=new od}static parse(t,n){return new xr(n).parse(t)}static parseInline(t,n){return new xr(n).parseInline(t)}parse(t,n=!0){let r="",i,o,s,a,l,u,c,f,g,h,m,p,w,b,v,S,O,R,M;const _=t.length;for(i=0;i<_;i++){if(h=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[h.type]&&(M=this.options.extensions.renderers[h.type].call({parser:this},h),M!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(h.type))){r+=M||"";continue}switch(h.type){case"space":continue;case"hr":{r+=this.renderer.hr();continue}case"heading":{r+=this.renderer.heading(this.parseInline(h.tokens),h.depth,rd(this.parseInline(h.tokens,this.textRenderer)),this.slugger);continue}case"code":{r+=this.renderer.code(h.text,h.lang,h.escaped);continue}case"table":{for(f="",c="",a=h.header.length,o=0;o<a;o++)c+=this.renderer.tablecell(this.parseInline(h.header[o].tokens),{header:!0,align:h.align[o]});for(f+=this.renderer.tablerow(c),g="",a=h.rows.length,o=0;o<a;o++){for(u=h.rows[o],c="",l=u.length,s=0;s<l;s++)c+=this.renderer.tablecell(this.parseInline(u[s].tokens),{header:!1,align:h.align[s]});g+=this.renderer.tablerow(c)}r+=this.renderer.table(f,g);continue}case"blockquote":{g=this.parse(h.tokens),r+=this.renderer.blockquote(g);continue}case"list":{for(m=h.ordered,p=h.start,w=h.loose,a=h.items.length,g="",o=0;o<a;o++)v=h.items[o],S=v.checked,O=v.task,b="",v.task&&(R=this.renderer.checkbox(S),w?v.tokens.length>0&&v.tokens[0].type==="paragraph"?(v.tokens[0].text=R+" "+v.tokens[0].text,v.tokens[0].tokens&&v.tokens[0].tokens.length>0&&v.tokens[0].tokens[0].type==="text"&&(v.tokens[0].tokens[0].text=R+" "+v.tokens[0].tokens[0].text)):v.tokens.unshift({type:"text",text:R}):b+=R),b+=this.parse(v.tokens,w),g+=this.renderer.listitem(b,O,S);r+=this.renderer.list(g,m,p);continue}case"html":{r+=this.renderer.html(h.text);continue}case"paragraph":{r+=this.renderer.paragraph(this.parseInline(h.tokens));continue}case"text":{for(g=h.tokens?this.parseInline(h.tokens):h.text;i+1<_&&t[i+1].type==="text";)h=t[++i],g+=`
`+(h.tokens?this.parseInline(h.tokens):h.text);r+=n?this.renderer.paragraph(g):g;continue}default:{const E='Token with "'+h.type+'" type was not found.';if(this.options.silent){console.error(E);return}else throw new Error(E)}}}return r}parseInline(t,n){n=n||this.renderer;let r="",i,o,s;const a=t.length;for(i=0;i<a;i++){if(o=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]&&(s=this.options.extensions.renderers[o.type].call({parser:this},o),s!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type))){r+=s||"";continue}switch(o.type){case"escape":{r+=n.text(o.text);break}case"html":{r+=n.html(o.text);break}case"link":{r+=n.link(o.href,o.title,this.parseInline(o.tokens,n));break}case"image":{r+=n.image(o.href,o.title,o.text);break}case"strong":{r+=n.strong(this.parseInline(o.tokens,n));break}case"em":{r+=n.em(this.parseInline(o.tokens,n));break}case"codespan":{r+=n.codespan(o.text);break}case"br":{r+=n.br();break}case"del":{r+=n.del(this.parseInline(o.tokens,n));break}case"text":{r+=n.text(o.text);break}default:{const l='Token with "'+o.type+'" type was not found.';if(this.options.silent){console.error(l);return}else throw new Error(l)}}}return r}}class ys{constructor(t){this.options=t||Br}static passThroughHooks=new Set(["preprocess","postprocess"]);preprocess(t){return t}postprocess(t){return t}}function vm(e,t,n){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+vn(r.message+"",!0)+"</pre>";if(t)return Promise.resolve(i);if(n){n(null,i);return}return i}if(t)return Promise.reject(r);if(n){n(r);return}throw r}}function ad(e,t){return(n,r,i)=>{typeof r=="function"&&(i=r,r=null);const o={...r};r={...$e.defaults,...o};const s=vm(r.silent,r.async,i);if(typeof n>"u"||n===null)return s(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(gm(r),r.hooks&&(r.hooks.options=r),i){const a=r.highlight;let l;try{r.hooks&&(n=r.hooks.preprocess(n)),l=e(n,r)}catch(f){return s(f)}const u=function(f){let g;if(!f)try{r.walkTokens&&$e.walkTokens(l,r.walkTokens),g=t(l,r),r.hooks&&(g=r.hooks.postprocess(g))}catch(h){f=h}return r.highlight=a,f?s(f):i(null,g)};if(!a||a.length<3||(delete r.highlight,!l.length))return u();let c=0;$e.walkTokens(l,function(f){f.type==="code"&&(c++,setTimeout(()=>{a(f.text,f.lang,function(g,h){if(g)return u(g);h!=null&&h!==f.text&&(f.text=h,f.escaped=!0),c--,c===0&&u()})},0))}),c===0&&u();return}if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then(a=>e(a,r)).then(a=>r.walkTokens?Promise.all($e.walkTokens(a,r.walkTokens)).then(()=>a):a).then(a=>t(a,r)).then(a=>r.hooks?r.hooks.postprocess(a):a).catch(s);try{r.hooks&&(n=r.hooks.preprocess(n));const a=e(n,r);r.walkTokens&&$e.walkTokens(a,r.walkTokens);let l=t(a,r);return r.hooks&&(l=r.hooks.postprocess(l)),l}catch(a){return s(a)}}}function $e(e,t,n){return ad(Sr.lex,xr.parse)(e,t,n)}$e.options=$e.setOptions=function(e){return $e.defaults={...$e.defaults,...e},tm($e.defaults),$e};$e.getDefaults=ed;$e.defaults=Br;$e.use=function(...e){const t=$e.defaults.extensions||{renderers:{},childTokens:{}};e.forEach(n=>{const r={...n};if(r.async=$e.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if(i.renderer){const o=t.renderers[i.name];o?t.renderers[i.name]=function(...s){let a=i.renderer.apply(this,s);return a===!1&&(a=o.apply(this,s)),a}:t.renderers[i.name]=i.renderer}if(i.tokenizer){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");t[i.level]?t[i.level].unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),r.extensions=t),n.renderer){const i=$e.defaults.renderer||new Bs;for(const o in n.renderer){const s=i[o];i[o]=(...a)=>{let l=n.renderer[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.renderer=i}if(n.tokenizer){const i=$e.defaults.tokenizer||new $s;for(const o in n.tokenizer){const s=i[o];i[o]=(...a)=>{let l=n.tokenizer[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.tokenizer=i}if(n.hooks){const i=$e.defaults.hooks||new ys;for(const o in n.hooks){const s=i[o];ys.passThroughHooks.has(o)?i[o]=a=>{if($e.defaults.async)return Promise.resolve(n.hooks[o].call(i,a)).then(u=>s.call(i,u));const l=n.hooks[o].call(i,a);return s.call(i,l)}:i[o]=(...a)=>{let l=n.hooks[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.hooks=i}if(n.walkTokens){const i=$e.defaults.walkTokens;r.walkTokens=function(o){let s=[];return s.push(n.walkTokens.call(this,o)),i&&(s=s.concat(i.call(this,o))),s}}$e.setOptions(r)})};$e.walkTokens=function(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call($e,r)),r.type){case"table":{for(const i of r.header)n=n.concat($e.walkTokens(i.tokens,t));for(const i of r.rows)for(const o of i)n=n.concat($e.walkTokens(o.tokens,t));break}case"list":{n=n.concat($e.walkTokens(r.items,t));break}default:$e.defaults.extensions&&$e.defaults.extensions.childTokens&&$e.defaults.extensions.childTokens[r.type]?$e.defaults.extensions.childTokens[r.type].forEach(function(i){n=n.concat($e.walkTokens(r[i],t))}):r.tokens&&(n=n.concat($e.walkTokens(r.tokens,t)))}return n};$e.parseInline=ad(Sr.lexInline,xr.parseInline);$e.Parser=xr;$e.parser=xr.parse;$e.Renderer=Bs;$e.TextRenderer=id;$e.Lexer=Sr;$e.lexer=Sr.lex;$e.Tokenizer=$s;$e.Slugger=od;$e.Hooks=ys;$e.parse=$e;$e.options;$e.setOptions;$e.use;$e.walkTokens;$e.parseInline;xr.parse;Sr.lex;const bm=fn("div")({name:"MarkdownContainer",class:"gdg-mnuv029",propsAsIs:!1});class wm extends Nt.PureComponent{targetElement=null;renderMarkdownIntoDiv(){const{targetElement:t,props:n}=this;if(t===null)return;const{contents:r,createNode:i}=n,o=$e(r),s=document.createRange();s.selectNodeContents(t),s.deleteContents();let a=i?.(o);if(a===void 0){const u=document.createElement("template");u.innerHTML=o,a=u.content}t.append(a);const l=t.getElementsByTagName("a");for(const u of l)u.target="_blank",u.rel="noreferrer noopener"}containerRefHook=t=>{this.targetElement=t,this.renderMarkdownIntoDiv()};render(){return this.renderMarkdownIntoDiv(),Nt.createElement(bm,{ref:this.containerRefHook})}}const ym=fn("textarea")({name:"InputBox",class:"gdg-izpuzkl",propsAsIs:!1}),Cm=fn("div")({name:"ShadowBox",class:"gdg-s69h75o",propsAsIs:!1}),Sm=fn("div")({name:"GrowingEntryStyle",class:"gdg-g1y0xocz",propsAsIs:!1});let ou=0;const Wr=e=>{const{placeholder:t,value:n,onKeyDown:r,highlight:i,altNewline:o,validatedSelection:s,...a}=e,{onChange:l,className:u}=a,c=d.useRef(null),f=n??"";Tn(l!==void 0,"GrowingEntry must be a controlled input area");const[g]=d.useState(()=>"input-box-"+(ou=(ou+1)%1e7));d.useEffect(()=>{const m=c.current;if(m===null||m.disabled)return;const p=f.toString().length;m.focus(),m.setSelectionRange(i?0:p,p)},[]),d.useLayoutEffect(()=>{if(s!==void 0){const m=typeof s=="number"?[s,null]:s;c.current?.setSelectionRange(m[0],m[1])}},[s]);const h=d.useCallback(m=>{m.key==="Enter"&&m.shiftKey&&o===!0||r?.(m)},[o,r]);return d.createElement(Sm,{className:"gdg-growing-entry"},d.createElement(Cm,{className:u},f+`
`),d.createElement(ym,{...a,className:(u??"")+" gdg-input",id:g,ref:c,onKeyDown:h,value:f,placeholder:t,dir:"auto"}))},ja={};let pr=null;function xm(){const e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}function ra(e){const t=e.toLowerCase().trim();if(ja[t]!==void 0)return ja[t];pr=pr||xm(),pr.style.color="#000",pr.style.color=t;const n=getComputedStyle(pr).color;pr.style.color="#fff",pr.style.color=t;const r=getComputedStyle(pr).color;if(r!==n)return[0,0,0,1];let i=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return i.length<4&&i.push(1),i=i.map(o=>Number.isNaN(o)?0:o),ja[t]=i,i}function Nr(e,t){const[n,r,i]=ra(e);return`rgba(${n}, ${r}, ${i}, ${t})`}const au=new Map;function su(e,t){const n=`${e}-${t}`,r=au.get(n);if(r!==void 0)return r;const i=Dn(e,t);return au.set(n,i),i}function Dn(e,t){if(t===void 0)return e;const[n,r,i,o]=ra(e);if(o===1)return e;const[s,a,l,u]=ra(t),c=o+u*(1-o),f=(o*n+u*s*(1-o))/c,g=(o*r+u*a*(1-o))/c,h=(o*i+u*l*(1-o))/c;return`rgba(${f}, ${g}, ${h}, ${c})`}var li=new Map,ci=new Map,Cs=new Map;function km(){li.clear(),Cs.clear(),ci.clear()}function Mm(e,t,n,r,i){var o,s,a;let l=0,u={};for(let f of e)l+=(o=n.get(f))!=null?o:i,u[f]=((s=u[f])!=null?s:0)+1;let c=t-l;for(let f of Object.keys(u)){let g=u[f],h=(a=n.get(f))!=null?a:i,m=h*g/l,p=c*m*r/g,w=h+p;n.set(f,w)}}function Rm(e,t){var n;let r=new Map,i=0;for(let u of"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?"){let c=e.measureText(u).width;r.set(u,c),i+=c}let o=i/r.size,s=3,a=(t/o+s)/(s+1),l=r.keys();for(let u of l)r.set(u,((n=r.get(u))!=null?n:o)*a);return r}function ji(e,t,n,r){var i,o;let s=ci.get(n);if(r&&s!==void 0&&s.count>2e4){let u=Cs.get(n);if(u===void 0&&(u=Rm(e,s.size),Cs.set(n,u)),s.count>5e5){let f=0;for(let g of t)f+=(i=u.get(g))!=null?i:s.size;return f*1.01}let c=e.measureText(t);return Mm(t,c.width,u,Math.max(.05,1-s.count/2e5),s.size),ci.set(n,{count:s.count+t.length,size:s.size}),c.width}let a=e.measureText(t),l=a.width/t.length;if(((o=s?.count)!=null?o:0)>2e4)return a.width;if(s===void 0)ci.set(n,{count:t.length,size:l});else{let u=l-s.size,c=t.length/(s.count+t.length),f=s.size+u*c;ci.set(n,{count:s.count+t.length,size:f})}return a.width}function Em(e,t,n,r,i,o,s,a){if(t.length<=1)return t.length;if(i<n)return-1;let l=Math.floor(n/i*o),u=ji(e,t.slice(0,Math.max(0,l)),r,s);if(u!==n)if(u<n){for(;u<n;)l++,u=ji(e,t.slice(0,Math.max(0,l)),r,s);l--}else for(;u>n;){let c=t.lastIndexOf(" ",l-1);c>0?l=c:l--,u=ji(e,t.slice(0,Math.max(0,l)),r,s)}if(t[l]!==" "){let c=0;c=t.lastIndexOf(" ",l),c>0&&(l=c)}return l}function Im(e,t,n,r,i,o){let s=`${t}_${n}_${r}px`,a=li.get(s);if(a!==void 0)return a;if(r<=0)return[];let l=[],u=t.split(`
`),c=ci.get(n),f=c===void 0?t.length:r/c.size*1.5,g=i&&c!==void 0&&c.count>2e4;for(let h of u){let m=ji(e,h.slice(0,Math.max(0,f)),n,g),p=Math.min(h.length,f);if(m<=r)l.push(h);else{for(;m>r;){let w=Em(e,h,r,n,m,p,g),b=h.slice(0,Math.max(0,w));h=h.slice(b.length),l.push(b),m=ji(e,h.slice(0,Math.max(0,f)),n,g),p=Math.min(h.length,f)}m>0&&l.push(h)}}return l=l.map((h,m)=>m===0?h.trimEnd():h.trim()),li.set(s,l),li.size>500&&li.delete(li.keys().next().value),l}function Tm(e,t){return Nt.useMemo(()=>e.map((n,r)=>({group:n.group,grow:n.grow,hasMenu:n.hasMenu,icon:n.icon,id:n.id,menuIcon:n.menuIcon,overlayIcon:n.overlayIcon,sourceIndex:r,sticky:r<t,indicatorIcon:n.indicatorIcon,style:n.style,themeOverride:n.themeOverride,title:n.title,trailingRowOptions:n.trailingRowOptions,width:n.width,growOffset:n.growOffset,rowMarker:n.rowMarker,rowMarkerChecked:n.rowMarkerChecked,headerRowMarkerTheme:n.headerRowMarkerTheme,headerRowMarkerAlwaysVisible:n.headerRowMarkerAlwaysVisible,headerRowMarkerDisabled:n.headerRowMarkerDisabled})),[e,t])}function Dm(e,t){const[n,r]=t;if(e.columns.hasIndex(n)||e.rows.hasIndex(r))return!0;if(e.current!==void 0){if(Ki(e.current.cell,t))return!0;const i=[e.current.range,...e.current.rangeStack];for(const o of i)if(n>=o.x&&n<o.x+o.width&&r>=o.y&&r<o.y+o.height)return!0}return!1}function to(e,t){return(e??"")===(t??"")}function Om(e,t,n){return n.current===void 0||e[1]!==n.current.cell[1]?!1:t.span===void 0?n.current.cell[0]===e[0]:n.current.cell[0]>=t.span[0]&&n.current.cell[0]<=t.span[1]}function sd(e,t){const[n,r]=e;return n>=t.x&&n<t.x+t.width&&r>=t.y&&r<t.y+t.height}function Ki(e,t){return e?.[0]===t?.[0]&&e?.[1]===t?.[1]}function ld(e){return[e.x+e.width-1,e.y+e.height-1]}function lu(e,t,n){const r=n.x,i=n.x+n.width-1,o=n.y,s=n.y+n.height-1,[a,l]=e;if(l<o||l>s)return!1;if(t.span===void 0)return a>=r&&a<=i;const[u,c]=t.span;return u>=r&&u<=i||c>=r&&u<=i||u<r&&c>i}function Pm(e,t,n,r){let i=0;if(n.current===void 0)return i;const o=n.current.range;(r||o.height*o.width>1)&&lu(e,t,o)&&i++;for(const s of n.current.rangeStack)lu(e,t,s)&&i++;return i}function ud(e,t){let n=e;if(t!==void 0){let r=[...e];const i=n[t.src];t.src>t.dest?(r.splice(t.src,1),r.splice(t.dest,0,i)):(r.splice(t.dest+1,0,i),r.splice(t.src,1)),r=r.map((o,s)=>({...o,sticky:e[s].sticky})),n=r}return n}function gi(e,t){let n=0;const r=ud(e,t);for(let i=0;i<r.length;i++){const o=r[i];if(o.sticky)n+=o.width;else break}return n}function Ur(e,t,n){if(typeof n=="number")return t*n;{let r=0;for(let i=e-t;i<e;i++)r+=n(i);return r}}function Ss(e,t,n,r,i){const o=ud(e,r),s=[];for(const u of o)if(u.sticky)s.push(u);else break;if(s.length>0)for(const u of s)n-=u.width;let a=t,l=i??0;for(;l<=n&&a<o.length;)l+=o[a].width,a++;for(let u=t;u<a;u++){const c=o[u];c.sticky||s.push(c)}return s}function _m(e,t,n){let r=0;for(const i of t){const o=i.sticky?r:r+(n??0);if(e<=o+i.width)return i.sourceIndex;r+=i.width}return-1}function Fm(e,t,n,r,i,o,s,a,l,u){const c=r+i;if(n&&e<=i)return-2;if(e<=c)return-1;let f=t;for(let m=0;m<u;m++){const p=o-1-m,w=typeof s=="number"?s:s(p);if(f-=w,e>=f)return p}const g=o-u,h=e-(l??0);if(typeof s=="number"){const m=Math.floor((h-c)/s)+a;return m>=g?void 0:m}else{let m=c;for(let p=a;p<g;p++){const w=s(p);if(h<=m+w)return p;m+=w}return}}let Ko=0,Zi={};const Lm=typeof window>"u";async function Am(){Lm||document?.fonts?.ready===void 0||(await document.fonts.ready,Ko=0,Zi={},km())}Am();function cd(e,t,n,r){return`${e}_${r??t?.font}_${n}`}function qr(e,t,n,r="middle"){const i=cd(e,t,r,n);let o=Zi[i];return o===void 0&&(o=t.measureText(e),Zi[i]=o,Ko++),Ko>1e4&&(Zi={},Ko=0),o}function dd(e,t){const n=cd(e,void 0,"middle",t);return Zi[n]}function Zn(e,t){return typeof t!="string"&&(t=t.baseFontFull),Hm(e,t)}function uu(e,t){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZ";e.save(),e.textBaseline=t;const r=e.measureText(n);return e.restore(),r}const cu=[];function Hm(e,t){for(const o of cu)if(o.key===t)return o.val;const n=uu(e,"alphabetic"),i=-(uu(e,"middle").actualBoundingBoxDescent-n.actualBoundingBoxDescent)+n.actualBoundingBoxAscent/2;return cu.push({key:t,val:i}),i}function zm(e,t,n,r,i,o){const{ctx:s,rect:a,theme:l}=e;let u=Number.MAX_SAFE_INTEGER;const c=500;if(t!==void 0&&(u=n-t,u<c)){const f=1-u/c;s.globalAlpha=f,s.fillStyle=l.bgSearchResult,s.fillRect(a.x+1,a.y+1,a.width-(i?2:1),a.height-(o?2:1)),s.globalAlpha=1,r!==void 0&&(r.fillStyle=l.bgSearchResult)}return u<c}function lo(e,t,n){const{ctx:r,theme:i}=e,o=t??{},s=n??i.textDark;return s!==o.fillStyle&&(r.fillStyle=s,o.fillStyle=s),o}function Ws(e,t,n,r,i){const{rect:o,ctx:s,theme:a}=e;s.fillStyle=a.textDark,jn({ctx:s,rect:o,theme:a},t,n,r,i)}function fd(e,t,n,r,i,o,s,a,l){l==="right"?e.fillText(t,n+i-(a.cellHorizontalPadding+.5),r+o/2+s):l==="center"?e.fillText(t,n+i/2,r+o/2+s):e.fillText(t,n+a.cellHorizontalPadding+.5,r+o/2+s)}function Us(e,t){const n=qr("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function Vm(e,t){e.includes(`
`)&&(e=e.split(/\r?\n/,1)[0]);const n=t/4;return e.length>n&&(e=e.slice(0,n)),e}function Nm(e,t,n,r,i,o,s,a,l,u){const c=a.baseFontFull,f=Im(e,t,c,i-a.cellHorizontalPadding*2,u??!1),g=Us(e,c),h=a.lineHeight*g,m=g+h*(f.length-1),p=m+a.cellVerticalPadding>o;p&&(e.save(),e.rect(n,r,i,o),e.clip());const w=r+o/2-m/2;let b=Math.max(r+a.cellVerticalPadding,w);for(const v of f)if(fd(e,v,n,b,i,g,s,a,l),b+=h,b>r+o)break;p&&e.restore()}function jn(e,t,n,r,i){const{ctx:o,rect:s,theme:a}=e,{x:l,y:u,width:c,height:f}=s;r=r??!1,r||(t=Vm(t,c));const g=Zn(o,a),h=Ns(t)==="rtl";if(n===void 0&&h&&(n="right"),h&&(o.direction="rtl"),t.length>0){let m=!1;n==="right"?(o.textAlign="right",m=!0):n!==void 0&&n!=="left"&&(o.textAlign=n,m=!0),r?Nm(o,t,l,u,c,f,g,a,n,i):fd(o,t,l,u,c,f,g,a,n),m&&(o.textAlign="start"),h&&(o.direction="inherit")}}function Kn(e,t,n,r,i,o){typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.max(0,Math.min(o.tl,i/2,r/2)),tr:Math.max(0,Math.min(o.tr,i/2,r/2)),bl:Math.max(0,Math.min(o.bl,i/2,r/2)),br:Math.max(0,Math.min(o.br,i/2,r/2))},e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}function $m(e,t,n){e.arc(t,n-1.25*3.5,1.25,0,2*Math.PI,!1),e.arc(t,n,1.25,0,2*Math.PI,!1),e.arc(t,n******3.5,1.25,0,2*Math.PI,!1)}function Bm(e,t,n){const r=function(a,l){const u=l.x-a.x,c=l.y-a.y,f=Math.sqrt(u*u+c*c),g=u/f,h=c/f;return{x:u,y:l.y-a.y,len:f,nx:g,ny:h,ang:Math.atan2(h,g)}};let i;const o=t.length;let s=t[o-1];for(let a=0;a<o;a++){let l=t[a%o];const u=t[(a+1)%o],c=r(l,s),f=r(l,u),g=c.nx*f.ny-c.ny*f.nx,h=c.nx*f.nx-c.ny*-f.ny;let m=Math.asin(g<-1?-1:g>1?1:g),p=1,w=!1;h<0?m<0?m=Math.PI+m:(m=Math.PI-m,p=-1,w=!0):m>0&&(p=-1,w=!0),i=l.radius!==void 0?l.radius:n;const b=m/2;let v=Math.abs(Math.cos(b)*i/Math.sin(b)),S;v>Math.min(c.len/2,f.len/2)?(v=Math.min(c.len/2,f.len/2),S=Math.abs(v*Math.sin(b)/Math.cos(b))):S=i;let O=l.x+f.nx*v,R=l.y+f.ny*v;O+=-f.ny*S*p,R+=f.nx*S*p,e.arc(O,R,S,c.ang+Math.PI/2*p,f.ang-Math.PI/2*p,w),s=l,l=u}e.closePath()}function xs(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m){const p={x:0,y:o+u,width:0,height:0};if(e>=h.length||t>=c||t<-2||e<0)return p;const w=o-i;if(e>=f){const b=s>e?-1:1,v=gi(h);p.x+=v+l;for(let S=s;S!==e;S+=b)p.x+=h[b===1?S:S-1].width*b}else for(let b=0;b<e;b++)p.x+=h[b].width;if(p.width=h[e].width+1,t===-1)p.y=i,p.height=w;else if(t===-2){p.y=0,p.height=i;let b=e;const v=h[e].group,S=h[e].sticky;for(;b>0&&to(h[b-1].group,v)&&h[b-1].sticky===S;){const R=h[b-1];p.x-=R.width,p.width+=R.width,b--}let O=e;for(;O+1<h.length&&to(h[O+1].group,v)&&h[O+1].sticky===S;){const R=h[O+1];p.width+=R.width,O++}if(!S){const R=gi(h),M=p.x-R;M<0&&(p.x-=M,p.width+=M),p.x+p.width>n&&(p.width=n-p.x)}}else if(t>=c-g){let b=c-t;for(p.y=r;b>0;){const v=t+b-1;p.height=typeof m=="number"?m:m(v),p.y-=p.height,b--}p.height+=1}else{const b=a>t?-1:1;if(typeof m=="number"){const v=t-a;p.y+=v*m}else for(let v=a;v!==t;v+=b)p.y+=m(v)*b;p.height=(typeof m=="number"?m:m(t))+1}return p}const qs=1<<21;function Wn(e,t){return(t+2)*qs+e}function hd(e){return e%qs}function Gs(e){return Math.floor(e/qs)-2}function Ys(e){const t=hd(e),n=Gs(e);return[t,n]}class gd{visibleWindow={x:0,y:0,width:0,height:0};freezeCols=0;freezeRows=[];isInWindow=t=>{const n=hd(t),r=Gs(t),i=this.visibleWindow,o=n>=i.x&&n<=i.x+i.width||n<this.freezeCols,s=r>=i.y&&r<=i.y+i.height||this.freezeRows.includes(r);return o&&s};setWindow(t,n,r){this.visibleWindow.x===t.x&&this.visibleWindow.y===t.y&&this.visibleWindow.width===t.width&&this.visibleWindow.height===t.height&&this.freezeCols===n&&hi(this.freezeRows,r)||(this.visibleWindow=t,this.freezeCols=n,this.freezeRows=r,this.clearOutOfWindow())}}class Wm extends gd{cache=new Map;setValue=(t,n)=>{this.cache.set(Wn(t[0],t[1]),n)};getValue=t=>this.cache.get(Wn(t[0],t[1]));clearOutOfWindow=()=>{for(const[t]of this.cache.entries())this.isInWindow(t)||this.cache.delete(t)}}class Ji{cells;constructor(t=[]){this.cells=new Set(t.map(n=>Wn(n[0],n[1])))}add(t){this.cells.add(Wn(t[0],t[1]))}has(t){return t===void 0?!1:this.cells.has(Wn(t[0],t[1]))}remove(t){this.cells.delete(Wn(t[0],t[1]))}clear(){this.cells.clear()}get size(){return this.cells.size}hasHeader(){for(const t of this.cells)if(Gs(t)<0)return!0;return!1}hasItemInRectangle(t){for(let n=t.y;n<t.y+t.height;n++)for(let r=t.x;r<t.x+t.width;r++)if(this.cells.has(Wn(r,n)))return!0;return!1}hasItemInRegion(t){for(const n of t)if(this.hasItemInRectangle(n))return!0;return!1}*values(){for(const t of this.cells)yield Ys(t)}}function Um(e){return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":e.textGroupHeader??e.textHeader,"--gdg-bg-group-header":e.bgGroupHeader??e.bgHeader,"--gdg-bg-group-header-hovered":e.bgGroupHeaderHovered??e.bgHeaderHovered,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bubble-height":`${e.bubbleHeight}px`,"--gdg-bubble-padding":`${e.bubblePadding}px`,"--gdg-bubble-margin":`${e.bubbleMargin}px`,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":e.horizontalBorderColor??e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":`${e.cellHorizontalPadding}px`,"--gdg-cell-vertical-padding":`${e.cellVerticalPadding}px`,"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize,"--gdg-checkbox-max-size":`${e.checkboxMaxSize}px`,...e.resizeIndicatorColor===void 0?{}:{"--gdg-resize-indicator-color":e.resizeIndicatorColor},...e.headerBottomBorderColor===void 0?{}:{"--gdg-header-bottom-border-color":e.headerBottomBorderColor},...e.roundingRadius===void 0?{}:{"--gdg-rounding-radius":`${e.roundingRadius}px`}}}const md={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bubbleHeight:20,bubblePadding:6,bubbleMargin:4,bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#353fb5",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4,checkboxMaxSize:18};function pd(){return md}const vd=Nt.createContext(md);function qm(){return Nt.useContext(vd)}function ir(e,...t){const n={...e};for(const r of t)if(r!==void 0)for(const i in r)r.hasOwnProperty(i)&&(i==="bgCell"?n[i]=Dn(r[i],n[i]):n[i]=r[i]);return(n.headerFontFull===void 0||e.fontFamily!==n.fontFamily||e.headerFontStyle!==n.headerFontStyle)&&(n.headerFontFull=`${n.headerFontStyle} ${n.fontFamily}`),(n.baseFontFull===void 0||e.fontFamily!==n.fontFamily||e.baseFontStyle!==n.baseFontStyle)&&(n.baseFontFull=`${n.baseFontStyle} ${n.fontFamily}`),(n.markerFontFull===void 0||e.fontFamily!==n.fontFamily||e.markerFontStyle!==n.markerFontStyle)&&(n.markerFontFull=`${n.markerFontStyle} ${n.fontFamily}`),n}const ks=150;function Gm(e,t,n,r){return r(t)?.measure?.(e,t,n)??ks}function bd(e,t,n,r,i,o,s,a,l){let u=0;const c=i===void 0?[]:i.map(h=>{const m=Gm(e,h[r],t,l);return u=Math.max(u,m),m});if(c.length>5&&a){u=0;let h=0;for(const p of c)h+=p;const m=h/c.length;for(let p=0;p<c.length;p++)c[p]>=m*2?c[p]=0:u=Math.max(u,c[p])}const f=e.font;e.font=t.headerFontFull,u=Math.max(u,e.measureText(n.title).width+t.cellHorizontalPadding*2+(n.icon===void 0?0:28)),e.font=f;const g=Math.max(Math.ceil(o),Math.min(Math.floor(s),Math.ceil(u)));return{...n,width:g}}function Ym(e,t,n,r,i,o,s,a,l){const u=d.useRef(t),c=d.useRef(n),f=d.useRef(s);u.current=t,c.current=n,f.current=s;const[g,h]=d.useMemo(()=>{if(typeof window>"u")return[null,null];const v=document.createElement("canvas");return v.style.display="none",v.style.opacity="0",v.style.position="fixed",[v,v.getContext("2d",{alpha:!1})]},[]);d.useLayoutEffect(()=>(g&&document.documentElement.append(g),()=>{g?.remove()}),[g]);const m=d.useRef({}),p=d.useRef(),[w,b]=d.useState();return d.useLayoutEffect(()=>{const v=c.current;if(v===void 0||e.every(Po))return;let S=Math.max(1,10-Math.floor(e.length/1e4)),O=0;S<u.current&&S>1&&(S--,O=1);const R={x:0,y:0,width:e.length,height:Math.min(u.current,S)},M={x:0,y:u.current-1,width:e.length,height:1};(async()=>{const E=v(R,l.signal),k=O>0?v(M,l.signal):void 0;let F;typeof E=="object"?F=E:F=await _l(E),k!==void 0&&(typeof k=="object"?F=[...F,...k]:F=[...F,...await _l(k)]),p.current=e,b(F)})()},[l.signal,e]),d.useMemo(()=>{let S=e.every(Po)?e:h===null?e.map(_=>Po(_)?_:{..._,width:ks}):(h.font=f.current.baseFontFull,e.map((_,E)=>{if(Po(_))return _;if(m.current[_.id]!==void 0)return{..._,width:m.current[_.id]};if(w===void 0||p.current!==e||_.id===void 0)return{..._,width:ks};const k=bd(h,s,_,E,w,i,o,!0,a);return m.current[_.id]=k.width,k})),O=0,R=0;const M=[];for(const[_,E]of S.entries())O+=E.width,E.grow!==void 0&&E.grow>0&&(R+=E.grow,M.push(_));if(O<r&&M.length>0){const _=[...S],E=r-O;let k=E;for(let F=0;F<M.length;F++){const D=M[F],C=(S[D].grow??0)/R,I=F===M.length-1?k:Math.min(k,Math.floor(E*C));_[D]={...S[D],growOffset:I,width:S[D].width+I},k-=I}S=_}return{sizedColumns:S,nonGrowWidth:O}},[r,e,h,w,s,i,o,a])}var Ka,du;function Xm(){if(du)return Ka;du=1;function e(t,n,r){return t===t&&(r!==void 0&&(t=t<=r?t:r),n!==void 0&&(t=t>=n?t:n)),t}return Ka=e,Ka}var Za,fu;function jm(){if(fu)return Za;fu=1;var e=Xm(),t=Mc();function n(r,i,o){return o===void 0&&(o=i,i=void 0),o!==void 0&&(o=t(o),o=o===o?o:0),i!==void 0&&(i=t(i),i=i===i?i:0),e(t(r),i,o)}return Za=n,Za}var Km=jm();const Fn=ar(Km);var Ja,hu;function Zm(){if(hu)return Ja;hu=1;function e(){}return Ja=e,Ja}var Qa,gu;function Jm(){if(gu)return Qa;gu=1;var e=uh(),t=Zm(),n=Rc(),r=1/0,i=e&&1/n(new e([,-0]))[1]==r?function(o){return new e(o)}:t;return Qa=i,Qa}var es,mu;function Qm(){if(mu)return es;mu=1;var e=ch(),t=dh(),n=hh(),r=fh(),i=Jm(),o=Rc(),s=200;function a(l,u,c){var f=-1,g=t,h=l.length,m=!0,p=[],w=p;if(c)m=!1,g=n;else if(h>=s){var b=u?null:i(l);if(b)return o(b);m=!1,g=r,w=new e}else w=u?[]:p;e:for(;++f<h;){var v=l[f],S=u?u(v):v;if(v=c||v!==0?v:0,m&&S===S){for(var O=w.length;O--;)if(w[O]===S)continue e;u&&w.push(S),p.push(v)}else g(w,S,c)||(w!==p&&w.push(S),p.push(v))}return p}return es=a,es}var ts,pu;function ep(){if(pu)return ts;pu=1;var e=Qm();function t(n){return n&&n.length?e(n):[]}return ts=t,ts}var tp=ep();const np=ar(tp);var rp=gh();const vu=ar(rp),$t='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',ip=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}<rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="${t}"/></svg>`},op=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}<rect x="2" y="2" width="16" height="16" rx="4" fill="${n}"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="${t}"/></svg>`},ap=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="${t}"/>
  </svg>`},sp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="${t}"/>
</svg>`},lp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
    <path
        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"
        fill="${n}"
    />
    <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"
        fill="${t}"
    />
</svg>`},wd=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="${t}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="${t}"/>
</svg>
  `},up=e=>{const t=e.bgColor;return`${$t}
    <path stroke="${t}" stroke-width="2" d="M12 3v14"/>
    <path stroke="${t}" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>
    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="${t}"/>
  </svg>
`},cp=wd,dp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="${t}"/>
</svg>`},fp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="${t}"/>
    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="${t}"/>
    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
  </svg>`},hp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="${t}"/>
</svg>`},gp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path fill="${t}" d="M3 3h14v14H3z"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="${n}"/>
  </svg>`},mp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="${t}"/>
  </svg>`},pp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="${t}"/>
</svg>`},vp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="${t}"/>
    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="${t}"/>
  </svg>`},bp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="${t}"/>
</svg>`},wp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="8" width="10" height="8" rx="2" fill="${n}"/>
    <rect x="8" y="4" width="10" height="8" rx="2" fill="${n}"/>
    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="${t}"/>
  </svg>`},yp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path fill="${t}" d="M4 3h12v14H4z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="${n}"/>
</svg>`},Cp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="${t}"/>
  </svg>`},Sp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="${t}"/>
  </svg>`},xp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="${t}"/>
</svg>`},kp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="${t}"/>
  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="${t}"/>
</svg>`},Mp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 1.4.7.7 0 0 1 0-1.4z" fill="${t}"/>
  </svg>`},Rp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="${t}"/>
</svg>`},Ep=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="${t}"/>
  </svg>`},Ip=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="${t}"/>
</svg>`},Tp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="${t}"/>
</svg>`},Dp=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>`},Op=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>
`},Pp={headerRowID:ip,headerNumber:ap,headerCode:op,headerString:sp,headerBoolean:lp,headerAudioUri:cp,headerVideoUri:dp,headerEmoji:fp,headerImage:hp,headerUri:wd,headerPhone:gp,headerMarkdown:mp,headerDate:pp,headerTime:vp,headerEmail:bp,headerReference:wp,headerIfThenElse:yp,headerSingleValue:Cp,headerLookup:Sp,headerTextTemplate:xp,headerMath:kp,headerRollup:Mp,headerJoinStrings:Rp,headerSplitString:Ep,headerGeoDistance:Ip,headerArray:Tp,rowOwnerOverlay:Dp,protectedColumnOverlay:Op,renameIcon:up};function _p(e,t){return e==="normal"?[t.bgIconHeader,t.fgIconHeader]:e==="selected"?["white",t.accentColor]:[t.accentColor,t.bgHeader]}class Fp{onSettled;spriteMap=new Map;headerIcons;inFlight=0;constructor(t,n){this.onSettled=n,this.headerIcons=t??{}}drawSprite(t,n,r,i,o,s,a,l=1){const[u,c]=_p(n,a),f=s*Math.ceil(window.devicePixelRatio),g=`${u}_${c}_${f}_${t}`;let h=this.spriteMap.get(g);if(h===void 0){const m=this.headerIcons[t];if(m===void 0)return;h=document.createElement("canvas");const p=h.getContext("2d");if(p===null)return;const w=new Image;w.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(m({fgColor:c,bgColor:u}))}`,this.spriteMap.set(g,h);const b=w.decode();if(b===void 0)return;this.inFlight++,b.then(()=>{p.drawImage(w,0,0,f,f)}).finally(()=>{this.inFlight--,this.inFlight===0&&this.onSettled()})}else l<1&&(r.globalAlpha=l),r.drawImage(h,0,0,f,f,i,o,s,s),l<1&&(r.globalAlpha=1)}}function yd(e){if(e.length===0)return;let t;for(const n of e)t=Math.min(t??n.y,n.y)}function ba(e,t,n,r,i,o,s,a,l){a=a??t;let u=t,c=e;const f=r-o;let g=!1;for(;u<n&&c<f;){const h=i(c);if(u+h>a&&l(u,c,h,!1,s&&c===r-1)===!0){g=!0;break}u+=h,c++}if(!g){u=n;for(let h=0;h<o;h++){c=r-1-h;const m=i(c);u-=m,l(u,c,m,!0,s&&c===r-1)}}}function kr(e,t,n,r,i,o){let s=0,a=0;const l=i+r;for(const u of e){const c=u.sticky?a:s+n;if(o(u,c,l,u.sticky?0:a,t)===!0)break;s+=u.width,a+=u.sticky?u.width:0}}function Cd(e,t,n,r,i){let o=0,s=0;for(let a=0;a<e.length;a++){const l=e[a];let u=a+1,c=l.width;for(l.sticky&&(s+=c);u<e.length&&to(e[u].group,l.group)&&e[u].sticky===e[a].sticky;){const p=e[u];c+=p.width,u++,a++,p.sticky&&(s+=p.width)}const f=l.sticky?0:n,g=o+f,h=l.sticky?0:Math.max(0,s-g),m=Math.min(c-h,t-(g+h));i([l.sourceIndex,e[u-1].sourceIndex],l.group??"",g+h,0,m,r),o+=c}}function Sd(e,t,n,r,i,o,s){const[a,l]=e;let u,c;const f=s.find(g=>!g.sticky)?.sourceIndex??0;if(l>f){const g=Math.max(a,f);let h=t,m=r;for(let p=o.sourceIndex-1;p>=g;p--)h-=s[p].width,m+=s[p].width;for(let p=o.sourceIndex+1;p<=l;p++)m+=s[p].width;c={x:h,y:n,width:m,height:i}}if(f>a){const g=Math.min(l,f-1);let h=t,m=r;for(let p=o.sourceIndex-1;p>=a;p--)h-=s[p].width,m+=s[p].width;for(let p=o.sourceIndex+1;p<=g;p++)m+=s[p].width;u={x:h,y:n,width:m,height:i}}return[u,c]}function Lp(e,t,n,r){if(r==="any")return xd(e,{x:t,y:n,width:1,height:1});if(r==="vertical"&&(t=e.x),r==="horizontal"&&(n=e.y),sd([t,n],e))return;const i=t-e.x,o=e.x+e.width-t,s=n-e.y+1,a=e.y+e.height-n,l=Math.min(r==="vertical"?Number.MAX_SAFE_INTEGER:i,r==="vertical"?Number.MAX_SAFE_INTEGER:o,r==="horizontal"?Number.MAX_SAFE_INTEGER:s,r==="horizontal"?Number.MAX_SAFE_INTEGER:a);return l===a?{x:e.x,y:e.y+e.height,width:e.width,height:n-e.y-e.height+1}:l===s?{x:e.x,y:n,width:e.width,height:e.y-n}:l===o?{x:e.x+e.width,y:e.y,width:t-e.x-e.width+1,height:e.height}:{x:t,y:e.y,width:e.x-t,height:e.height}}function no(e,t,n,r,i,o,s,a){return e<=i+s&&i<=e+n&&t<=o+a&&o<=t+r}function Vr(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function xd(e,t){const n=Math.min(e.x,t.x),r=Math.min(e.y,t.y),i=Math.max(e.x+e.width,t.x+t.width)-n,o=Math.max(e.y+e.height,t.y+t.height)-r;return{x:n,y:r,width:i,height:o}}function Ap(e,t){return e.x<=t.x&&e.y<=t.y&&e.x+e.width>=t.x+t.width&&e.y+e.height>=t.y+t.height}function Hp(e,t,n,r){if(e.x>t||e.y>n||e.x<0&&e.y<0&&e.x+e.width>t&&e.y+e.height>n)return;if(e.x>=0&&e.y>=0&&e.x+e.width<=t&&e.y+e.height<=n)return e;const i=-4,o=-4,s=t+4,a=n+4,l=i-e.x,u=e.x+e.width-s,c=o-e.y,f=e.y+e.height-a,g=l>0?e.x+Math.floor(l/r)*r:e.x,h=u>0?e.x+e.width-Math.floor(u/r)*r:e.x+e.width,m=c>0?e.y+Math.floor(c/r)*r:e.y,p=f>0?e.y+e.height-Math.floor(f/r)*r:e.y+e.height;return{x:g,y:m,width:h-g,height:p-m}}function zp(e,t,n,r,i){const[o,s,a,l]=t,[u,c,f,g]=i,{x:h,y:m,width:p,height:w}=e,b=[];if(p<=0||w<=0)return b;const v=h+p,S=m+w,O=h<o,R=m<s,M=h+p>a,_=m+w>l,E=h>=o&&h<a||v>o&&v<=a||h<o&&v>a,k=m>=s&&m<l||S>s&&S<=l||m<s&&S>l;if(E&&k){const D=Math.max(h,o),C=Math.max(m,s),I=Math.min(v,a),T=Math.min(S,l);b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:c,width:f-u+1,height:g-c+1}})}if(O&&R){const D=h,C=m,I=Math.min(v,o),T=Math.min(S,s);b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:0,width:u+1,height:c+1}})}if(R&&E){const D=Math.max(h,o),C=m,I=Math.min(v,a),T=Math.min(S,s);b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:0,width:f-u+1,height:c+1}})}if(R&&M){const D=Math.max(h,a),C=m,I=v,T=Math.min(S,s);b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:f,y:0,width:n-f+1,height:c+1}})}if(O&&k){const D=h,C=Math.max(m,s),I=Math.min(v,o),T=Math.min(S,l);b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:c,width:u+1,height:g-c+1}})}if(M&&k){const D=Math.max(h,a),C=Math.max(m,s),I=v,T=Math.min(S,l);b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:f,y:c,width:n-f+1,height:g-c+1}})}if(O&&_){const D=h,C=Math.max(m,l),I=Math.min(v,o),T=S;b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:g,width:u+1,height:r-g+1}})}if(_&&E){const D=Math.max(h,o),C=Math.max(m,l),I=Math.min(v,a),T=S;b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:g,width:f-u+1,height:r-g+1}})}if(M&&_){const D=Math.max(h,a),C=Math.max(m,l),I=v,T=S;b.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:f,y:g,width:n-f+1,height:r-g+1}})}return b}const Vp={kind:Z.Loading,allowOverlay:!1};function bu(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p,w,b,v,S,O,R,M,_,E,k,F,D,C,I,T,x,$,q,X){let oe=S?.size??Number.MAX_SAFE_INTEGER;const Q=performance.now();let J=I.baseFontFull;e.font=J;const te={ctx:e},ae=[0,0],le=w>0?Ur(l,w,u):0;let fe,re;const H=yd(v);return kr(t,a,o,s,i,(P,G,ue,he,ke)=>{const Se=Math.max(0,he-G),et=G+Se,Re=i+1,Xe=P.width-Se,yt=r-i-1;if(v.length>0){let ze=!1;for(let Ie=0;Ie<v.length;Ie++){const tt=v[Ie];if(no(et,Re,Xe,yt,tt.x,tt.y,tt.width,tt.height)){ze=!0;break}}if(!ze)return}const De=()=>{e.save(),e.beginPath(),e.rect(et,Re,Xe,yt),e.clip()},ne=O.columns.hasIndex(P.sourceIndex),Ee=f(P.group??"").overrideTheme,xe=P.themeOverride===void 0&&Ee===void 0?I:ir(I,Ee,P.themeOverride),ce=xe.baseFontFull;ce!==J&&(J=ce,e.font=ce),De();let pe;return ba(ke,ue,r,l,u,w,b,H,(ze,Ie,tt,Te,Oe)=>{if(Ie<0||(ae[0]=P.sourceIndex,ae[1]=Ie,S!==void 0&&!S.has(ae)))return;if(v.length>0){let je=!1;for(let Dt=0;Dt<v.length;Dt++){const Ot=v[Dt];if(no(G,ze,P.width,tt,Ot.x,Ot.y,Ot.width,Ot.height)){je=!0;break}}if(!je)return}const Je=O.rows.hasIndex(Ie),ye=h.hasIndex(Ie),qe=Ie<l?c(ae):Vp;let pt=G,ft=P.width,We=!1,xt=!1;if(qe.span!==void 0){const[je,Dt]=qe.span,Ot=`${Ie},${je},${Dt},${P.sticky}`;if(re===void 0&&(re=new Set),re.has(Ot)){oe--;return}else{const ln=Sd(qe.span,G,ze,P.width,tt,P,n),rn=P.sticky?ln[0]:ln[1];if(!P.sticky&&ln[0]!==void 0&&(xt=!0),rn!==void 0){pt=rn.x,ft=rn.width,re.add(Ot),e.restore(),pe=void 0,e.save(),e.beginPath();const hn=Math.max(0,he-rn.x);e.rect(rn.x+hn,ze,rn.width-hn,tt),fe===void 0&&(fe=[]),fe.push({x:rn.x+hn,y:ze,width:rn.width-hn,height:tt}),e.clip(),We=!0}}}const qt=g?.(Ie),Tt=Oe&&P.trailingRowOptions?.themeOverride!==void 0?P.trailingRowOptions?.themeOverride:void 0,_t=qe.themeOverride===void 0&&qt===void 0&&Tt===void 0?xe:ir(xe,qt,Tt,qe.themeOverride);e.beginPath();const tn=Om(ae,qe,O);let zt=Pm(ae,qe,O,p);const Sn=qe.span!==void 0&&O.columns.some(je=>qe.span!==void 0&&je>=qe.span[0]&&je<=qe.span[1]);tn&&!m&&p?zt=0:tn&&p&&(zt=Math.max(zt,1)),Sn&&zt++,tn||(Je&&zt++,ne&&!Oe&&zt++);const Vt=qe.kind===Z.Protected?_t.bgCellMedium:_t.bgCell;let ut;if((Te||Vt!==I.bgCell)&&(ut=Dn(Vt,ut)),zt>0||ye){ye&&(ut=Dn(_t.bgHeader,ut));for(let je=0;je<zt;je++)ut=Dn(_t.accentLight,ut)}else if(R!==void 0){for(const je of R)if(je[0]===P.sourceIndex&&je[1]===Ie){ut=Dn(_t.bgSearchResult,ut);break}}if(M!==void 0)for(let je=0;je<M.length;je++){const Dt=M[je],Ot=Dt.range;Dt.style!=="solid-outline"&&Ot.x<=P.sourceIndex&&P.sourceIndex<Ot.x+Ot.width&&Ot.y<=Ie&&Ie<Ot.y+Ot.height&&(ut=Dn(Dt.color,ut))}let nn=!1;if(S!==void 0){const je=ze+1,Ot=(Te?je+tt-1:Math.min(je+tt-1,r-le))-je;(Ot!==tt-1||pt+1<=he)&&(nn=!0,e.save(),e.beginPath(),e.rect(pt+1,je,ft-1,Ot),e.clip()),ut=ut===void 0?_t.bgCell:Dn(ut,_t.bgCell)}const Ct=P.sourceIndex===n.length-1,Ft=Ie===l-1;ut!==void 0&&(e.fillStyle=ut,pe!==void 0&&(pe.fillStyle=ut),S!==void 0?e.fillRect(pt+1,ze+1,ft-(Ct?2:1),tt-(Ft?2:1)):e.fillRect(pt,ze,ft,tt)),qe.style==="faded"&&(e.globalAlpha=.6);let Yt;for(let je=0;je<k.length;je++){const Dt=k[je];if(Dt.item[0]===P.sourceIndex&&Dt.item[1]===Ie){Yt=Dt;break}}if(ft>X&&!xt){const je=_t.baseFontFull;je!==J&&(e.font=je,J=je),pe=kd(e,qe,P.sourceIndex,Ie,Ct,Ft,pt,ze,ft,tt,zt>0,_t,ut??_t.bgCell,_,E,Yt?.hoverAmount??0,F,C,Q,D,pe,T,x,$,q)}return nn&&e.restore(),qe.style==="faded"&&(e.globalAlpha=1),oe--,We&&(e.restore(),pe?.deprep?.(te),pe=void 0,De(),J=ce,e.font=ce),oe<=0}),e.restore(),oe<=0}),fe}const Ni=[0,0],$i={x:0,y:0,width:0,height:0},ns=[void 0,()=>{}];let Ms=!1;function Np(){Ms=!0}function kd(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p,w,b,v,S,O,R,M,_,E){let k,F;w!==void 0&&w[0][0]===n&&w[0][1]===r&&(k=w[1][0],F=w[1][1]);let D;Ni[0]=n,Ni[1]=r,$i.x=s,$i.y=a,$i.width=l,$i.height=u,ns[0]=M.getValue(Ni),ns[1]=x=>M.setValue(Ni,x),Ms=!1;const C={ctx:e,theme:f,col:n,row:r,cell:t,rect:$i,highlighted:c,cellFillColor:g,hoverAmount:p,frameTime:v,hoverX:k,drawState:ns,hoverY:F,imageLoader:h,spriteManager:m,hyperWrapping:b,overrideCursor:k!==void 0?E:void 0,requestAnimationFrame:Np},I=zm(C,t.lastUpdated,v,O,i,o),T=_(t);if(T!==void 0){O?.renderer!==T&&(O?.deprep?.(C),O=void 0);const x=T.drawPrep?.(C,O);S!==void 0&&!ui(C.cell)?S(C,()=>T.draw(C,t)):T.draw(C,t),D=x===void 0?void 0:{deprep:x?.deprep,fillStyle:x?.fillStyle,font:x?.font,renderer:T}}return(I||Ms)&&R?.(Ni),D}function Xs(e,t,n,r,i,o,s,a,l=-20,u=-20,c=void 0,f="center",g="square"){const h=Math.floor(i+s/2),m=g==="circle"?1e4:t.roundingRadius??4;let p=Zc(c??t.checkboxMaxSize,s,t.cellVerticalPadding),w=p/2;const b=Kc(f,r,o,t.cellHorizontalPadding,p),v=jc(b,h,p),S=Jc(r+l,i+u,v);switch(n){case!0:{e.beginPath(),Kn(e,b-p/2,h-p/2,p,p,m),g==="circle"&&(w*=.8,p*=.8),e.fillStyle=a?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(b-w+p/4.23,h-w+p/1.97),e.lineTo(b-w+p/2.42,h-w+p/1.44),e.lineTo(b-w+p/1.29,h-w+p/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break}case ea:case!1:{e.beginPath(),Kn(e,b-p/2+.5,h-p/2+.5,p-1,p-1,m),e.lineWidth=1,e.strokeStyle=S?t.textDark:t.textMedium,e.stroke();break}case As:{e.beginPath(),Kn(e,b-p/2,h-p/2,p,p,m),e.fillStyle=S?t.textMedium:t.textLight,e.fill(),g==="circle"&&(w*=.8,p*=.8),e.beginPath(),e.moveTo(b-p/3,h),e.lineTo(b+p/3,h),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break}default:eo()}}function $p(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p,w,b,v){const S=s+a;if(S<=0)return;e.fillStyle=f.bgHeader,e.fillRect(0,0,i,S);const O=r?.[0]?.[0],R=r?.[0]?.[1],M=r?.[1]?.[0],_=r?.[1]?.[1],E=f.headerFontFull;e.font=E,kr(t,0,o,0,S,(k,F,D,C)=>{if(w!==void 0&&!w.has([k.sourceIndex,-1]))return;const I=Math.max(0,C-F);e.save(),e.beginPath(),e.rect(F+I,a,k.width-I,s),e.clip();const T=p(k.group??"").overrideTheme,x=k.themeOverride===void 0&&T===void 0?f:ir(f,T,k.themeOverride);x.bgHeader!==f.bgHeader&&(e.fillStyle=x.bgHeader,e.fill()),x!==f&&(e.font=x.headerFontFull);const $=c.columns.hasIndex(k.sourceIndex),q=l!==void 0||u||k.headerRowMarkerDisabled===!0,X=!q&&R===-1&&O===k.sourceIndex,oe=q?0:h.find(le=>le.item[0]===k.sourceIndex&&le.item[1]===-1)?.hoverAmount??0,Q=c?.current!==void 0&&c.current.cell[0]===k.sourceIndex,J=$?x.accentColor:Q?x.bgHeaderHasFocus:x.bgHeader,te=n?a:0,ae=k.sourceIndex===0?0:1;$?(e.fillStyle=J,e.fillRect(F+ae,te,k.width-ae,s)):(Q||oe>0)&&(e.beginPath(),e.rect(F+ae,te,k.width-ae,s),Q&&(e.fillStyle=x.bgHeaderHasFocus,e.fill()),oe>0&&(e.globalAlpha=oe,e.fillStyle=x.bgHeaderHovered,e.fill(),e.globalAlpha=1)),Ed(e,F,te,k.width,s,k,$,x,X,X?M:void 0,X?_:void 0,Q,oe,g,b,v),e.restore()}),n&&Bp(e,t,i,o,a,r,f,g,h,m,p,w)}function Bp(e,t,n,r,i,o,s,a,l,u,c,f){const[h,m]=o?.[0]??[];let p=0;Cd(t,n,r,i,(w,b,v,S,O,R)=>{if(f!==void 0&&!f.hasItemInRectangle({x:w[0],y:-2,width:w[1]-w[0]+1,height:1}))return;e.save(),e.beginPath(),e.rect(v,S,O,R),e.clip();const M=c(b),_=M?.overrideTheme===void 0?s:ir(s,M.overrideTheme),E=m===-2&&h!==void 0&&h>=w[0]&&h<=w[1],k=E?_.bgGroupHeaderHovered??_.bgHeaderHovered:_.bgGroupHeader??_.bgHeader;if(k!==s.bgHeader&&(e.fillStyle=k,e.fill()),e.fillStyle=_.textGroupHeader??_.textHeader,M!==void 0){let F=v;if(M.icon!==void 0&&(a.drawSprite(M.icon,"normal",e,F+8,(i-20)/2,20,_),F+=26),e.fillText(M.name,F+8,i/2+Zn(e,s.headerFontFull)),M.actions!==void 0&&E){const D=Md({x:v,y:S,width:O,height:R},M.actions);e.beginPath();const C=D[0].x-10,I=v+O-C;e.rect(C,0,I,i);const T=e.createLinearGradient(C,0,C+I,0),x=Nr(k,0);T.addColorStop(0,x),T.addColorStop(10/I,k),T.addColorStop(1,k),e.fillStyle=T,e.fill(),e.globalAlpha=.6;const[$,q]=o?.[1]??[-1,-1];for(let X=0;X<M.actions.length;X++){const oe=M.actions[X],Q=D[X],J=Vr(Q,$+v,q);J&&(e.globalAlpha=1),a.drawSprite(oe.icon,"normal",e,Q.x+Q.width/2-10,Q.y+Q.height/2-10,20,_),J&&(e.globalAlpha=.6)}e.globalAlpha=1}}v!==0&&u(w[0])&&(e.beginPath(),e.moveTo(v+.5,0),e.lineTo(v+.5,i),e.strokeStyle=s.borderColor,e.lineWidth=1,e.stroke()),e.restore(),p=v+O}),e.beginPath(),e.moveTo(p+.5,0),e.lineTo(p+.5,i),e.moveTo(0,i+.5),e.lineTo(n,i+.5),e.strokeStyle=s.borderColor,e.lineWidth=1,e.stroke()}const Ho=30;function Wp(e,t,n,r,i){return{x:e+n-Ho,y:Math.max(t,t+r/2-Ho/2),width:Ho,height:Math.min(Ho,r)}}function Md(e,t){const n=[];let r=e.x+e.width-26*t.length;const i=e.y+e.height/2-13,o=26,s=26;for(let a=0;a<t.length;a++)n.push({x:r,y:i,width:s,height:o}),r+=26;return n}function Bi(e,t,n){return!n||e===void 0||(e.x=t-(e.x-t)-e.width),e}function Rd(e,t,n,r,i,o,s,a){const l=s.cellHorizontalPadding,u=s.headerIconSize,c=Wp(n,r,i,o);let f=n+l;const g=t.icon===void 0?void 0:{x:f,y:r+(o-u)/2,width:u,height:u},h=g===void 0||t.overlayIcon===void 0?void 0:{x:g.x+9,y:g.y+6,width:18,height:18};g!==void 0&&(f+=Math.ceil(u*1.3));const m={x:f,y:r,width:i-f,height:o};let p;if(t.indicatorIcon!==void 0){const b=e===void 0?dd(t.title,s.headerFontFull)?.width??0:qr(t.title,e,s.headerFontFull).width;m.width=b,f+=b+l,p={x:f,y:r+(o-u)/2,width:u,height:u}}const w=n+i/2;return{menuBounds:Bi(c,w,a),iconBounds:Bi(g,w,a),iconOverlayBounds:Bi(h,w,a),textBounds:Bi(m,w,a),indicatorIconBounds:Bi(p,w,a)}}function wu(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p){if(o.rowMarker!==void 0&&o.headerRowMarkerDisabled!==!0){const v=o.rowMarkerChecked;v!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=f);const S=o.headerRowMarkerTheme!==void 0?ir(a,o.headerRowMarkerTheme):a;Xs(e,S,v,t,n,r,i,!1,void 0,void 0,a.checkboxMaxSize,"center",o.rowMarker),v!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=1);return}const w=s?a.textHeaderSelected:a.textHeader,b=o.hasMenu===!0&&(l||h&&s)&&p.menuBounds!==void 0;if(o.icon!==void 0&&p.iconBounds!==void 0){let v=s?"selected":"normal";o.style==="highlight"&&(v=s?"selected":"special"),g.drawSprite(o.icon,v,e,p.iconBounds.x,p.iconBounds.y,p.iconBounds.width,a),o.overlayIcon!==void 0&&p.iconOverlayBounds!==void 0&&g.drawSprite(o.overlayIcon,s?"selected":"special",e,p.iconOverlayBounds.x,p.iconOverlayBounds.y,p.iconOverlayBounds.width,a)}if(b&&r>35){const S=m?35:r-35,O=m?35*.7:r-35*.7,R=S/r,M=O/r,_=e.createLinearGradient(t,0,t+r,0),E=Nr(w,0);_.addColorStop(m?1:0,w),_.addColorStop(R,w),_.addColorStop(M,E),_.addColorStop(m?0:1,E),e.fillStyle=_}else e.fillStyle=w;if(m&&(e.textAlign="right"),p.textBounds!==void 0&&e.fillText(o.title,m?p.textBounds.x+p.textBounds.width:p.textBounds.x,n+i/2+Zn(e,a.headerFontFull)),m&&(e.textAlign="left"),o.indicatorIcon!==void 0&&p.indicatorIconBounds!==void 0&&(!b||!no(p.menuBounds.x,p.menuBounds.y,p.menuBounds.width,p.menuBounds.height,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,p.indicatorIconBounds.height))){let v=s?"selected":"normal";o.style==="highlight"&&(v=s?"selected":"special"),g.drawSprite(o.indicatorIcon,v,e,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,a)}if(b&&p.menuBounds!==void 0){const v=p.menuBounds,S=u!==void 0&&c!==void 0&&Vr(v,u+t,c+n);if(S||(e.globalAlpha=.7),o.menuIcon===void 0||o.menuIcon===ta.Triangle){e.beginPath();const O=v.x+v.width/2-5.5,R=v.y+v.height/2-3;Bm(e,[{x:O,y:R},{x:O+11,y:R},{x:O+5.5,y:R+6}],1),e.fillStyle=w,e.fill()}else if(o.menuIcon===ta.Dots){e.beginPath();const O=v.x+v.width/2,R=v.y+v.height/2;$m(e,O,R),e.fillStyle=w,e.fill()}else{const O=v.x+(v.width-a.headerIconSize)/2,R=v.y+(v.height-a.headerIconSize)/2;g.drawSprite(o.menuIcon,"normal",e,O,R,a.headerIconSize,a)}S||(e.globalAlpha=1)}}function Ed(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p){const w=Ns(o.title)==="rtl",b=Rd(e,o,t,n,r,i,a,w);m!==void 0?m({ctx:e,theme:a,rect:{x:t,y:n,width:r,height:i},column:o,columnIndex:o.sourceIndex,isSelected:s,hoverAmount:g,isHovered:l,hasSelectedCell:f,spriteManager:h,menuBounds:b?.menuBounds??{x:0,y:0,height:0,width:0},hoverX:u,hoverY:c},()=>wu(e,t,n,r,i,o,s,a,l,u,c,g,h,p,w,b)):wu(e,t,n,r,i,o,s,a,l,u,c,g,h,p,w,b)}function Up(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p,w,b,v){if(b!==void 0||t[t.length-1]!==n[t.length-1])return;const S=yd(w);kr(t,l,s,a,o,(O,R,M,_,E)=>{if(O!==t[t.length-1])return;R+=O.width;const k=Math.max(R,_);k>r||(e.save(),e.beginPath(),e.rect(k,o+1,1e4,i-o-1),e.clip(),ba(E,M,i,u,c,m,p,S,(F,D,C,I)=>{if(!I&&w.length>0&&!w.some(X=>no(R,F,1e4,C,X.x,X.y,X.width,X.height)))return;const T=g.hasIndex(D),x=h.hasIndex(D);e.beginPath();const $=f?.(D),q=$===void 0?v:ir(v,$);q.bgCell!==v.bgCell&&(e.fillStyle=q.bgCell,e.fillRect(R,F,1e4,C)),x&&(e.fillStyle=q.bgHeader,e.fillRect(R,F,1e4,C)),T&&(e.fillStyle=q.accentLight,e.fillRect(R,F,1e4,C))}),e.restore())})}function qp(e,t,n,r,i,o,s,a,l){let u=!1;for(const m of t)if(!m.sticky){u=s(m.sourceIndex);break}const c=l.horizontalBorderColor??l.borderColor,f=l.borderColor,g=u?gi(t):0;let h;if(g!==0&&(h=su(f,l.bgCell),e.beginPath(),e.moveTo(g+.5,0),e.lineTo(g+.5,r),e.strokeStyle=h,e.stroke()),i>0){const m=f===c&&h!==void 0?h:su(c,l.bgCell),p=Ur(o,i,a);e.beginPath(),e.moveTo(0,r-p+.5),e.lineTo(n,r-p+.5),e.strokeStyle=m,e.stroke()}}const Id=(e,t,n)=>{let r=0,i=t,o=0,s=n;if(e!==void 0&&e.length>0){r=Number.MAX_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;for(const a of e)r=Math.min(r,a.x-1),i=Math.max(i,a.x+a.width+1),o=Math.min(o,a.y-1),s=Math.max(s,a.y+a.height+1)}return{minX:r,maxX:i,minY:o,maxY:s}};function Gp(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m){const p=m.bgCell,{minX:w,maxX:b,minY:v,maxY:S}=Id(a,o,s),O=[],R=s-Ur(h,g,u);let M=l,_=n,E=0;for(;M+i<R;){const C=M+i,I=u(_);if(C>=v&&C<=S-1){const x=c?.(_)?.bgCell;x!==void 0&&x!==p&&_>=h-g&&O.push({x:w,y:C,w:b-w,h:I,color:x})}M+=I,_<h-g&&(E=M),_++}let k=0;const F=Math.min(R,S)-E;if(F>0)for(let C=0;C<t.length;C++){const I=t[C];if(I.width===0)continue;const T=I.sticky?k:k+r,x=I.themeOverride?.bgCell;x!==void 0&&x!==p&&T>=w&&T<=b&&f(C+1)&&O.push({x:T,y:E,w:I.width,h:F,color:x}),k+=I.width}if(O.length===0)return;let D;e.beginPath();for(let C=O.length-1;C>=0;C--){const I=O[C];D===void 0?D=I.color:I.color!==D&&(e.fillStyle=D,e.fill(),e.beginPath(),D=I.color),e.rect(I.x,I.y,I.w,I.h)}D!==void 0&&(e.fillStyle=D,e.fill()),e.beginPath()}function yu(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p,w,b=!1){if(l!==void 0){e.beginPath(),e.save(),e.rect(0,0,o,s);for(const C of l)e.rect(C.x+1,C.y+1,C.width-1,C.height-1);e.clip("evenodd")}const v=w.horizontalBorderColor??w.borderColor,S=w.borderColor,{minX:O,maxX:R,minY:M,maxY:_}=Id(a,o,s),E=[];e.beginPath();let k=.5;for(let C=0;C<t.length;C++){const I=t[C];if(I.width===0)continue;k+=I.width;const T=I.sticky?k:k+r;T>=O&&T<=R&&h(C+1)&&E.push({x1:T,y1:Math.max(u,M),x2:T,y2:Math.min(s,_),color:S})}let F=s+.5;for(let C=p-m;C<p;C++){const I=f(C);F-=I,E.push({x1:O,y1:F,x2:R,y2:F,color:v})}if(b!==!0){let C=c+.5,I=n;const T=F;for(;C+i<T;){const x=C+i;if(x>=M&&x<=_-1){const $=g?.(I);E.push({x1:O,y1:x,x2:R,y2:x,color:$?.horizontalBorderColor??$?.borderColor??v})}C+=f(I),I++}}const D=mh(E,C=>C.color);for(const C of Object.keys(D)){e.strokeStyle=C;for(const I of D[C])e.moveTo(I.x1,I.y1),e.lineTo(I.x2,I.y2);e.stroke(),e.beginPath()}l!==void 0&&e.restore()}function Yp(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p,w,b,v){const S=[];e.imageSmoothingEnabled=!1;const O=Math.min(i.cellYOffset,s),R=Math.max(i.cellYOffset,s);let M=0;if(typeof b=="number")M+=(R-O)*b;else for(let T=O;T<R;T++)M+=b(T);s>i.cellYOffset&&(M=-M),M+=l-i.translateY;const _=Math.min(i.cellXOffset,o),E=Math.max(i.cellXOffset,o);let k=0;for(let T=_;T<E;T++)k+=p[T].width;o>i.cellXOffset&&(k=-k),k+=a-i.translateX;const F=gi(w);if(k!==0&&M!==0)return{regions:[]};const D=u>0?Ur(g,u,b):0,C=c-F-Math.abs(k),I=f-h-D-Math.abs(M)-1;if(C>150&&I>150){const T={sx:0,sy:0,sw:c*m,sh:f*m,dx:0,dy:0,dw:c*m,dh:f*m};if(M>0?(T.sy=(h+1)*m,T.sh=I*m,T.dy=(M+h+1)*m,T.dh=I*m,S.push({x:0,y:h,width:c,height:M+1})):M<0&&(T.sy=(-M+h+1)*m,T.sh=I*m,T.dy=(h+1)*m,T.dh=I*m,S.push({x:0,y:f+M-D,width:c,height:-M+D})),k>0?(T.sx=F*m,T.sw=C*m,T.dx=(k+F)*m,T.dw=C*m,S.push({x:F-1,y:0,width:k+2,height:f})):k<0&&(T.sx=(F-k)*m,T.sw=C*m,T.dx=F*m,T.dw=C*m,S.push({x:c+k,y:0,width:-k,height:f})),e.setTransform(1,0,0,1,0,0),v){if(F>0&&k!==0&&M===0&&(r===void 0||n?.[1]!==!1)){const x=F*m,$=f*m;e.drawImage(t,0,0,x,$,0,0,x,$)}if(D>0&&k===0&&M!==0&&(r===void 0||n?.[0]!==!1)){const x=(f-D)*m,$=c*m,q=D*m;e.drawImage(t,0,x,$,q,0,x,$,q)}}e.drawImage(t,T.sx,T.sy,T.sw,T.sh,T.dx,T.dy,T.dw,T.dh),e.scale(m,m)}return e.imageSmoothingEnabled=!0,{regions:S}}function Xp(e,t,n,r,i,o,s,a,l,u){const c=[];return t!==e.cellXOffset||n!==e.cellYOffset||r!==e.translateX||i!==e.translateY||kr(l,n,r,i,a,(f,g,h,m)=>{if(f.sourceIndex===u){const p=Math.max(g,m)+1;return c.push({x:p,y:0,width:o-p,height:s}),!0}}),c}function jp(e,t){if(t===void 0||e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.freezeColumns!==t.freezeColumns||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.maxScaleFactor!==t.maxScaleFactor)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;let n;for(let r=0;r<e.mappedColumns.length;r++){const i=e.mappedColumns[r],o=t.mappedColumns[r];if(hi(i,o))continue;if(n!==void 0||i.width===o.width)return!1;const{width:s,...a}=i,{width:l,...u}=o;if(!hi(a,u))return!1;n=r}return n===void 0?!0:n}return!0}function Cu(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p){const w=m?.filter(_=>_.style!=="no-outline");if(w===void 0||w.length===0)return;const b=gi(a),v=Ur(h,g,f),S=[l,0,a.length,h-g],O=[b,0,t,n-v],R=w.map(_=>{const E=_.range,k=_.style??"dashed";return zp(E,S,t,n,O).map(F=>{const D=F.rect,C=xs(D.x,D.y,t,n,c,u+c,r,i,o,s,h,l,g,a,f),I=D.width===1&&D.height===1?C:xs(D.x+D.width-1,D.y+D.height-1,t,n,c,u+c,r,i,o,s,h,l,g,a,f);return D.x+D.width>=a.length&&(I.width-=1),D.y+D.height>=h&&(I.height-=1),{color:_.color,style:k,clip:F.clip,rect:Hp({x:C.x,y:C.y,width:I.x+I.width-C.x,height:I.y+I.height-C.y},t,n,8)}})}),M=()=>{e.lineWidth=1;let _=!1;for(const E of R)for(const k of E)if(k?.rect!==void 0&&no(0,0,t,n,k.rect.x,k.rect.y,k.rect.width,k.rect.height)){const F=_,D=!Ap(k.clip,k.rect);e.beginPath(),D&&(e.save(),e.rect(k.clip.x,k.clip.y,k.clip.width,k.clip.height),e.clip()),k.style==="dashed"&&!_?(e.setLineDash([5,3]),_=!0):(k.style==="solid"||k.style==="solid-outline")&&_&&(e.setLineDash([]),_=!1),e.strokeStyle=k.style==="solid-outline"?Dn(Dn(k.color,p.borderColor),p.bgCell):Nr(k.color,1),e.closePath(),e.strokeRect(k.rect.x+.5,k.rect.y+.5,k.rect.width-1,k.rect.height-1),D&&(e.restore(),_=F)}_&&e.setLineDash([])};return M(),M}function Su(e,t,n,r,i){e.beginPath(),e.moveTo(t,n),e.lineTo(t,r),e.lineWidth=2,e.strokeStyle=i,e.stroke(),e.globalAlpha=1}function rs(e,t,n,r,i,o,s,a,l,u,c,f,g,h,m,p,w){if(c.current===void 0)return;const b=c.current.range,v=c.current.cell,S=[b.x+b.width-1,b.y+b.height-1];if(v[1]>=w&&S[1]>=w||!s.some(T=>T.sourceIndex===v[0]||T.sourceIndex===S[0]))return;const[R,M]=c.current.cell,_=g(c.current.cell),E=_.span??[R,R],k=M>=w-h,F=h>0&&!k?Ur(w,h,f)-1:0,D=S[1];let C;if(kr(s,r,i,o,u,(T,x,$,q,X)=>{if(T.sticky&&R>T.sourceIndex)return;const oe=T.sourceIndex<E[0],Q=T.sourceIndex>E[1],J=T.sourceIndex===S[0];if(!(!J&&(oe||Q)))return ba(X,$,n,w,f,h,m,void 0,(te,ae,le)=>{if(ae!==M&&ae!==D)return;let fe=x,re=T.width;if(_.span!==void 0){const P=Sd(_.span,x,te,T.width,le,T,a),G=T.sticky?P[0]:P[1];G!==void 0&&(fe=G.x,re=G.width)}return ae===D&&J&&p&&(C=()=>{q>fe&&!T.sticky&&(e.beginPath(),e.rect(q,0,t-q,n),e.clip()),e.beginPath(),e.rect(fe+re-4,te+le-4,4,4),e.fillStyle=T.themeOverride?.accentColor??l.accentColor,e.fill()}),C!==void 0}),C!==void 0}),C===void 0)return;const I=()=>{e.save(),e.beginPath(),e.rect(0,u,t,n-u-F),e.clip(),C?.(),e.restore()};return I(),I}function Kp(e,t,n,r,i,o,s,a,l){l===void 0||l.size===0||(e.beginPath(),Cd(t,n,o,r,(u,c,f,g,h,m)=>{l.hasItemInRectangle({x:u[0],y:-2,width:u[1]-u[0]+1,height:1})&&e.rect(f,g,h,m)}),kr(t,a,o,s,i,(u,c,f,g)=>{const h=Math.max(0,g-c),m=c+h+1,p=u.width-h-1;l.has([u.sourceIndex,-1])&&e.rect(m,r,p,i-r)}),e.clip())}function Zp(e,t,n,r,i,o,s,a,l,u){let c=0;return kr(e,o,r,i,n,(f,g,h,m,p)=>(ba(p,h,t,s,a,l,u,void 0,(w,b,v,S)=>{S||(c=Math.max(b,c))}),!0)),c}function xu(e,t){const{canvasCtx:n,headerCanvasCtx:r,width:i,height:o,cellXOffset:s,cellYOffset:a,translateX:l,translateY:u,mappedColumns:c,enableGroups:f,freezeColumns:g,dragAndDropState:h,theme:m,drawFocus:p,headerHeight:w,groupHeaderHeight:b,disabledRows:v,rowHeight:S,verticalBorder:O,overrideCursor:R,isResizing:M,selection:_,fillHandle:E,freezeTrailingRows:k,rows:F,getCellContent:D,getGroupDetails:C,getRowThemeOverride:I,isFocused:T,drawHeaderCallback:x,prelightCells:$,drawCellCallback:q,highlightRegions:X,resizeCol:oe,imageLoader:Q,lastBlitData:J,hoverValues:te,hyperWrapping:ae,hoverInfo:le,spriteManager:fe,maxScaleFactor:re,hasAppendRow:H,touchMode:P,enqueue:G,renderStateProvider:ue,getCellRenderer:he,renderStrategy:ke,bufferACtx:Se,bufferBCtx:et,damage:Re,minimumCellWidth:Xe,resizeIndicator:yt}=e;if(i===0||o===0)return;const De=ke==="double-buffer",ne=Math.min(re,Math.ceil(window.devicePixelRatio??1)),Ee=ke!=="direct"&&jp(e,t),xe=n.canvas;(xe.width!==i*ne||xe.height!==o*ne)&&(xe.width=i*ne,xe.height=o*ne,xe.style.width=i+"px",xe.style.height=o+"px");const ce=r.canvas,pe=f?b+w:w,ze=pe+1;(ce.width!==i*ne||ce.height!==ze*ne)&&(ce.width=i*ne,ce.height=ze*ne,ce.style.width=i+"px",ce.style.height=ze+"px");const Ie=Se.canvas,tt=et.canvas;De&&(Ie.width!==i*ne||Ie.height!==o*ne)&&(Ie.width=i*ne,Ie.height=o*ne,J.current!==void 0&&(J.current.aBufferScroll=void 0)),De&&(tt.width!==i*ne||tt.height!==o*ne)&&(tt.width=i*ne,tt.height=o*ne,J.current!==void 0&&(J.current.bBufferScroll=void 0));const Te=J.current;if(Ee===!0&&s===Te?.cellXOffset&&a===Te?.cellYOffset&&l===Te?.translateX&&u===Te?.translateY)return;let Oe=null;De&&(Oe=n);const Je=r;let ye;De?Re!==void 0?ye=Te?.lastBuffer==="b"?et:Se:ye=Te?.lastBuffer==="b"?Se:et:ye=n;const qe=ye.canvas,pt=De?qe===Ie?tt:Ie:xe,ft=typeof S=="number"?()=>S:S;Je.save(),ye.save(),Je.beginPath(),ye.beginPath(),Je.textBaseline="middle",ye.textBaseline="middle",ne!==1&&(Je.scale(ne,ne),ye.scale(ne,ne));const We=Ss(c,s,i,h,l);let xt=[];const qt=p&&_.current?.cell[1]===a&&u===0;let Tt=!1;if(X!==void 0){for(const Ct of X)if(Ct.style!=="no-outline"&&Ct.range.y===a&&u===0){Tt=!0;break}}const _t=()=>{$p(Je,We,f,le,i,l,w,b,h,M,_,m,fe,te,O,C,Re,x,P),yu(Je,We,a,l,u,i,o,void 0,void 0,b,pe,ft,I,O,k,F,m,!0),Je.beginPath(),Je.moveTo(0,ze-.5),Je.lineTo(i,ze-.5),Je.strokeStyle=Dn(m.headerBottomBorderColor??m.horizontalBorderColor??m.borderColor,m.bgHeader),Je.stroke(),Tt&&Cu(Je,i,o,s,a,l,u,c,g,w,b,S,k,F,X,m),qt&&rs(Je,i,o,a,l,u,We,c,m,pe,_,ft,D,k,H,E,F)};if(Re!==void 0){const Ct=We[We.length-1].sourceIndex+1,Ft=Re.hasItemInRegion([{x:s,y:-2,width:Ct,height:2},{x:s,y:a,width:Ct,height:300},{x:0,y:a,width:g,height:300},{x:0,y:-2,width:g,height:2},{x:s,y:F-k,width:Ct,height:k,when:k>0}]),Yt=je=>{bu(je,We,c,o,pe,l,u,a,F,ft,D,C,I,v,T,p,k,H,xt,Re,_,$,X,Q,fe,te,le,q,ae,m,G,ue,he,R,Xe);const Dt=_.current;E&&p&&Dt!==void 0&&Re.has(ld(Dt.range))&&rs(je,i,o,a,l,u,We,c,m,pe,_,ft,D,k,H,E,F)};Ft&&(Yt(ye),Oe!==null&&(Oe.save(),Oe.scale(ne,ne),Oe.textBaseline="middle",Yt(Oe),Oe.restore()),Re.hasHeader()&&(Kp(Je,We,i,b,pe,l,u,a,Re),_t())),ye.restore(),Je.restore();return}if((Ee!==!0||s!==Te?.cellXOffset||l!==Te?.translateX||qt!==Te?.mustDrawFocusOnHeader||Tt!==Te?.mustDrawHighlightRingsOnHeader)&&_t(),Ee===!0){Tn(pt!==void 0&&Te!==void 0);const{regions:Ct}=Yp(ye,pt,pt===Ie?Te.aBufferScroll:Te.bBufferScroll,pt===Ie?Te.bBufferScroll:Te.aBufferScroll,Te,s,a,l,u,k,i,o,F,pe,ne,c,We,S,De);xt=Ct}else Ee!==!1&&(Tn(Te!==void 0),xt=Xp(Te,s,a,l,u,i,o,pe,We,Ee));qp(ye,We,i,o,k,F,O,ft,m);const tn=Cu(ye,i,o,s,a,l,u,c,g,w,b,S,k,F,X,m),zt=p?rs(ye,i,o,a,l,u,We,c,m,pe,_,ft,D,k,H,E,F):void 0;if(ye.fillStyle=m.bgCell,xt.length>0){ye.beginPath();for(const Ct of xt)ye.rect(Ct.x,Ct.y,Ct.width,Ct.height);ye.clip(),ye.fill(),ye.beginPath()}else ye.fillRect(0,0,i,o);const Sn=bu(ye,We,c,o,pe,l,u,a,F,ft,D,C,I,v,T,p,k,H,xt,Re,_,$,X,Q,fe,te,le,q,ae,m,G,ue,he,R,Xe);Up(ye,We,c,i,o,pe,l,u,a,F,ft,I,_.rows,v,k,H,xt,Re,m),Gp(ye,We,a,l,u,i,o,xt,pe,ft,I,O,k,F,m),yu(ye,We,a,l,u,i,o,xt,Sn,b,pe,ft,I,O,k,F,m),tn?.(),zt?.(),M&&yt!=="none"&&kr(We,0,l,0,pe,(Ct,Ft)=>Ct.sourceIndex===oe?(Su(Je,Ft+Ct.width,0,pe+1,Dn(m.resizeIndicatorColor??m.accentLight,m.bgHeader)),yt==="full"&&Su(ye,Ft+Ct.width,pe,o,Dn(m.resizeIndicatorColor??m.accentLight,m.bgCell)),!0):!1),Oe!==null&&(Oe.fillStyle=m.bgCell,Oe.fillRect(0,0,i,o),Oe.drawImage(ye.canvas,0,0));const Vt=Zp(We,o,pe,l,u,a,F,ft,k,H);Q?.setWindow({x:s,y:a,width:We.length,height:Vt-a},g,Array.from({length:k},(Ct,Ft)=>F-1-Ft));const ut=Te!==void 0&&(s!==Te.cellXOffset||l!==Te.translateX),nn=Te!==void 0&&(a!==Te.cellYOffset||u!==Te.translateY);J.current={cellXOffset:s,cellYOffset:a,translateX:l,translateY:u,mustDrawFocusOnHeader:qt,mustDrawHighlightRingsOnHeader:Tt,lastBuffer:De?qe===Ie?"a":"b":void 0,aBufferScroll:qe===Ie?[ut,nn]:Te?.aBufferScroll,bBufferScroll:qe===tt?[ut,nn]:Te?.bBufferScroll},ye.restore(),Je.restore()}const Jp=80;function Qp(e){const t=e-1;return t*t*t+1}class e0{callback;constructor(t){this.callback=t}currentHoveredItem=void 0;leavingItems=[];lastAnimationTime;addToLeavingItems=t=>{this.leavingItems.some(r=>Ki(r.item,t.item))||this.leavingItems.push(t)};removeFromLeavingItems=t=>{const n=this.leavingItems.find(r=>Ki(r.item,t));return this.leavingItems=this.leavingItems.filter(r=>r!==n),n?.hoverAmount??0};cleanUpLeavingElements=()=>{this.leavingItems=this.leavingItems.filter(t=>t.hoverAmount>0)};shouldStep=()=>{const t=this.leavingItems.length>0,n=this.currentHoveredItem!==void 0&&this.currentHoveredItem.hoverAmount<1;return t||n};getAnimatingItems=()=>this.currentHoveredItem!==void 0?[...this.leavingItems,this.currentHoveredItem]:this.leavingItems.map(t=>({...t,hoverAmount:Qp(t.hoverAmount)}));step=t=>{if(this.lastAnimationTime===void 0)this.lastAnimationTime=t;else{const r=(t-this.lastAnimationTime)/Jp;for(const o of this.leavingItems)o.hoverAmount=Fn(o.hoverAmount-r,0,1);this.currentHoveredItem!==void 0&&(this.currentHoveredItem.hoverAmount=Fn(this.currentHoveredItem.hoverAmount+r,0,1));const i=this.getAnimatingItems();this.callback(i),this.cleanUpLeavingElements()}this.shouldStep()?(this.lastAnimationTime=t,window.requestAnimationFrame(this.step)):this.lastAnimationTime=void 0};setHovered=t=>{if(!Ki(this.currentHoveredItem?.item,t)){if(this.currentHoveredItem!==void 0&&this.addToLeavingItems(this.currentHoveredItem),t!==void 0){const n=this.removeFromLeavingItems(t);this.currentHoveredItem={item:t,hoverAmount:n}}else this.currentHoveredItem=void 0;this.lastAnimationTime===void 0&&window.requestAnimationFrame(this.step)}}}class t0{fn;val;constructor(t){this.fn=t}get value(){return this.val??(this.val=this.fn())}}function js(e){return new t0(e)}const n0=js(()=>window.navigator.userAgent.includes("Firefox")),ia=js(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome")),oa=js(()=>window.navigator.platform.toLowerCase().startsWith("mac"));function r0(e){const t=d.useRef([]),n=d.useRef(0),r=d.useRef(e);r.current=e;const i=d.useCallback(()=>{const o=()=>window.requestAnimationFrame(s),s=()=>{const a=t.current.map(Ys);t.current=[],r.current(new Ji(a)),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?o:s)},[]);return d.useCallback(o=>{t.current.length===0&&i();const s=Wn(o[0],o[1]);t.current.includes(s)||t.current.push(s)},[i])}const wr="header",Ln="group-header",aa="out-of-bounds";var di;(function(e){e[e.Start=-2]="Start",e[e.StartPadding=-1]="StartPadding",e[e.Center=0]="Center",e[e.EndPadding=1]="EndPadding",e[e.End=2]="End"})(di||(di={}));function Td(e,t){return e===t?!0:e?.kind==="out-of-bounds"?e?.kind===t?.kind&&e?.location[0]===t?.location[0]&&e?.location[1]===t?.location[1]&&e?.region[0]===t?.region[0]&&e?.region[1]===t?.region[1]:e?.kind===t?.kind&&e?.location[0]===t?.location[0]&&e?.location[1]===t?.location[1]}const ku=6,i0=(e,t)=>e.kind===Z.Custom?e.copyData:t?.(e)?.getAccessibilityString(e)??"",o0=(e,t)=>{const{width:n,height:r,accessibilityHeight:i,columns:o,cellXOffset:s,cellYOffset:a,headerHeight:l,fillHandle:u=!1,groupHeaderHeight:c,rowHeight:f,rows:g,getCellContent:h,getRowThemeOverride:m,onHeaderMenuClick:p,onHeaderIndicatorClick:w,enableGroups:b,isFilling:v,onCanvasFocused:S,onCanvasBlur:O,isFocused:R,selection:M,freezeColumns:_,onContextMenu:E,freezeTrailingRows:k,fixedShadowX:F=!0,fixedShadowY:D=!0,drawFocusRing:C,onMouseDown:I,onMouseUp:T,onMouseMoveRaw:x,onMouseMove:$,onItemHovered:q,dragAndDropState:X,firstColAccessible:oe,onKeyDown:Q,onKeyUp:J,highlightRegions:te,canvasRef:ae,onDragStart:le,onDragEnd:fe,eventTargetRef:re,isResizing:H,resizeColumn:P,isDragging:G,isDraggable:ue=!1,allowResize:he,disabledRows:ke,hasAppendRow:Se,getGroupDetails:et,theme:Re,prelightCells:Xe,headerIcons:yt,verticalBorder:De,drawCell:ne,drawHeader:Ee,onCellFocused:xe,onDragOverCell:ce,onDrop:pe,onDragLeave:ze,imageWindowLoader:Ie,smoothScrollX:tt=!1,smoothScrollY:Te=!1,experimental:Oe,getCellRenderer:Je,resizeIndicator:ye="full"}=e,qe=e.translateX??0,pt=e.translateY??0,ft=Math.max(_,Math.min(o.length-1,s)),We=d.useRef(null),xt=d.useRef(Oe?.eventTarget??window),qt=xt.current,Tt=Ie,_t=d.useRef(),[tn,zt]=d.useState(!1),Sn=d.useRef([]),Vt=d.useRef(),[ut,nn]=d.useState(),[Ct,Ft]=d.useState(),Yt=d.useRef(null),[je,Dt]=d.useState(),[Ot,ln]=d.useState(!1),rn=d.useRef(Ot);rn.current=Ot;const hn=d.useMemo(()=>new Fp(yt,()=>{ge.current=void 0,Bt.current()}),[yt]),bn=b?c+l:l,Xt=d.useRef(-1),Jt=(Oe?.enableFirefoxRescaling??!1)&&n0.value,Ge=(Oe?.enableSafariRescaling??!1)&&ia.value;d.useLayoutEffect(()=>{window.devicePixelRatio===1||!Jt&&!Ge||(Xt.current!==-1&&zt(!0),window.clearTimeout(Xt.current),Xt.current=window.setTimeout(()=>{zt(!1),Xt.current=-1},200))},[a,ft,qe,pt,Jt,Ge]);const Et=Tm(o,_),Qt=d.useMemo(()=>F?gi(Et,X):0,[Et,X,F]),Lt=d.useCallback((V,ie,me)=>{const Me=V.getBoundingClientRect();if(ie>=Et.length||me>=g)return;const de=Me.width/n,be=xs(ie,me,n,r,c,bn,ft,a,qe,pt,g,_,k,Et,f);return de!==1&&(be.x*=de,be.y*=de,be.width*=de,be.height*=de),be.x+=Me.x,be.y+=Me.y,be},[n,r,c,bn,ft,a,qe,pt,g,_,k,Et,f]),At=d.useCallback((V,ie,me,Me)=>{const de=V.getBoundingClientRect(),be=de.width/n,Ye=(ie-de.left)/be,we=(me-de.top)/be,Ue=5,Fe=Ss(Et,ft,n,void 0,qe);let St=0,lt=0;Me instanceof MouseEvent&&(St=Me.button,lt=Me.buttons);const at=_m(Ye,Fe,qe),dt=Fm(we,r,b,l,c,g,f,a,pt,k),Gt=Me?.shiftKey===!0,gn=Me?.ctrlKey===!0,ct=Me?.metaKey===!0,sn=Me!==void 0&&!(Me instanceof MouseEvent)||Me?.pointerType==="touch",Tr=[Ye<0?-1:n<Ye?1:0,we<bn?-1:r<we?1:0];let tr;if(at===-1||we<0||Ye<0||dt===void 0||Ye>n||we>r){const Rt=Ye>n?1:Ye<0?-1:0,Vn=we>r?1:we<0?-1:0;let Mn=Rt*2,mt=Vn*2;Rt===0&&(Mn=at===-1?di.EndPadding:di.Center),Vn===0&&(mt=dt===void 0?di.EndPadding:di.Center);let jt=!1;if(at===-1&&dt===-1){const hr=Lt(V,Et.length-1,-1);Tn(hr!==void 0),jt=ie<hr.x+hr.width+Ue}const fr=Ye>n&&Ye<n+ws()||we>r&&we<r+ws();tr={kind:aa,location:[at!==-1?at:Ye<0?0:Et.length-1,dt??g-1],region:[Mn,mt],shiftKey:Gt,ctrlKey:gn,metaKey:ct,isEdge:jt,isTouch:sn,button:St,buttons:lt,scrollEdge:Tr,isMaybeScrollbar:fr}}else if(dt<=-1){let Rt=Lt(V,at,dt);Tn(Rt!==void 0);let Vn=Rt!==void 0&&Rt.x+Rt.width-ie<=Ue;const Mn=at-1;ie-Rt.x<=Ue&&Mn>=0?(Vn=!0,Rt=Lt(V,Mn,dt),Tn(Rt!==void 0),tr={kind:b&&dt===-2?Ln:wr,location:[Mn,dt],bounds:Rt,group:Et[Mn].group??"",isEdge:Vn,shiftKey:Gt,ctrlKey:gn,metaKey:ct,isTouch:sn,localEventX:ie-Rt.x,localEventY:me-Rt.y,button:St,buttons:lt,scrollEdge:Tr}):tr={kind:b&&dt===-2?Ln:wr,group:Et[at].group??"",location:[at,dt],bounds:Rt,isEdge:Vn,shiftKey:Gt,ctrlKey:gn,metaKey:ct,isTouch:sn,localEventX:ie-Rt.x,localEventY:me-Rt.y,button:St,buttons:lt,scrollEdge:Tr}}else{const Rt=Lt(V,at,dt);Tn(Rt!==void 0);const Vn=Rt!==void 0&&Rt.x+Rt.width-ie<Ue;let Mn=!1;if(u&&M.current!==void 0){const mt=ld(M.current.range),jt=Lt(V,mt[0],mt[1]);if(jt!==void 0){const fr=jt.x+jt.width-2,hr=jt.y+jt.height-2;Mn=Math.abs(fr-ie)<ku&&Math.abs(hr-me)<ku}}tr={kind:"cell",location:[at,dt],bounds:Rt,isEdge:Vn,shiftKey:Gt,ctrlKey:gn,isFillHandle:Mn,metaKey:ct,isTouch:sn,localEventX:ie-Rt.x,localEventY:me-Rt.y,button:St,buttons:lt,scrollEdge:Tr}}return tr},[n,Et,ft,qe,r,b,l,c,g,f,a,pt,k,Lt,u,M,bn]),[xn]=ut??[],Un=d.useRef(()=>{}),Hn=d.useRef(ut);Hn.current=ut;const[N,Be]=d.useMemo(()=>{const V=document.createElement("canvas"),ie=document.createElement("canvas");return V.style.display="none",V.style.opacity="0",V.style.position="fixed",ie.style.display="none",ie.style.opacity="0",ie.style.position="fixed",[V.getContext("2d",{alpha:!1}),ie.getContext("2d",{alpha:!1})]},[]);d.useLayoutEffect(()=>{if(!(N===null||Be===null))return document.documentElement.append(N.canvas),document.documentElement.append(Be.canvas),()=>{N.canvas.remove(),Be.canvas.remove()}},[N,Be]);const Pe=d.useMemo(()=>new Wm,[]),Pt=Jt&&tn?1:Ge&&tn?2:5,wn=Oe?.disableMinimumCellWidth===!0?1:10,ge=d.useRef(),bt=d.useRef(null),en=d.useRef(null),kt=d.useCallback(()=>{const V=We.current,ie=Yt.current;if(V===null||ie===null||(bt.current===null&&(bt.current=V.getContext("2d",{alpha:!1}),V.width=0,V.height=0),en.current===null&&(en.current=ie.getContext("2d",{alpha:!1}),ie.width=0,ie.height=0),bt.current===null||en.current===null||N===null||Be===null))return;let me=!1;const Me=Ye=>{me=!0,Dt(Ye)},de=ge.current,be={headerCanvasCtx:en.current,canvasCtx:bt.current,bufferACtx:N,bufferBCtx:Be,width:n,height:r,cellXOffset:ft,cellYOffset:a,translateX:Math.round(qe),translateY:Math.round(pt),mappedColumns:Et,enableGroups:b,freezeColumns:_,dragAndDropState:X,theme:Re,headerHeight:l,groupHeaderHeight:c,disabledRows:ke??rt.empty(),rowHeight:f,verticalBorder:De,isResizing:H,resizeCol:P,isFocused:R,selection:M,fillHandle:u,drawCellCallback:ne,hasAppendRow:Se,overrideCursor:Me,maxScaleFactor:Pt,freezeTrailingRows:k,rows:g,drawFocus:C,getCellContent:h,getGroupDetails:et??(Ye=>({name:Ye})),getRowThemeOverride:m,drawHeaderCallback:Ee,prelightCells:Xe,highlightRegions:te,imageLoader:Tt,lastBlitData:Vt,damage:_t.current,hoverValues:Sn.current,hoverInfo:Hn.current,spriteManager:hn,scrolling:tn,hyperWrapping:Oe?.hyperWrapping??!1,touchMode:Ot,enqueue:Un.current,renderStateProvider:Pe,renderStrategy:Oe?.renderStrategy??(ia.value?"double-buffer":"single-buffer"),getCellRenderer:Je,minimumCellWidth:wn,resizeIndicator:ye};be.damage===void 0?(ge.current=be,xu(be,de)):xu(be,void 0),!me&&(be.damage===void 0||be.damage.has(Hn?.current?.[0]))&&Dt(void 0)},[N,Be,n,r,ft,a,qe,pt,Et,b,_,X,Re,l,c,ke,f,De,H,Se,P,R,M,u,k,g,C,Pt,h,et,m,ne,Ee,Xe,te,Tt,hn,tn,Oe?.hyperWrapping,Oe?.renderStrategy,Ot,Pe,Je,wn,ye]),Bt=d.useRef(kt);d.useLayoutEffect(()=>{kt(),Bt.current=kt},[kt]),d.useLayoutEffect(()=>{(async()=>{document?.fonts?.ready!==void 0&&(await document.fonts.ready,ge.current=void 0,Bt.current())})()},[]);const an=d.useCallback(V=>{_t.current=V,Bt.current(),_t.current=void 0},[]),Sa=r0(an);Un.current=Sa;const co=d.useCallback(V=>{an(new Ji(V.map(ie=>ie.cell)))},[an]);Tt.setCallback(an);const[fo,xa]=d.useState(!1),[Mr,lr]=xn??[],pi=Mr!==void 0&&lr===-1&&o[Mr].headerRowMarkerDisabled!==!0,ka=Mr!==void 0&&lr===-2;let vi=!1,bi=!1,Rr=je;if(Rr===void 0&&Mr!==void 0&&lr!==void 0&&lr>-1&&lr<g){const V=h([Mr,lr],!0);vi=V.kind===An.NewRow||V.kind===An.Marker&&V.markerKind!=="number",bi=V.kind===Z.Boolean&&Hs(V),Rr=V.cursor}const kn=G?"grabbing":(Ct??!1)||H?"col-resize":fo||v?"crosshair":Rr!==void 0?Rr:pi||vi||bi||ka?"pointer":"default",wi=d.useMemo(()=>({contain:"strict",display:"block",cursor:kn}),[kn]),yi=d.useRef("default"),Gr=re?.current;Gr!=null&&yi.current!==wi.cursor&&(Gr.style.cursor=yi.current=wi.cursor);const Qn=d.useCallback((V,ie,me,Me)=>{if(et===void 0)return;const de=et(V);if(de.actions!==void 0){const be=Md(ie,de.actions);for(const[Ye,we]of be.entries())if(Vr(we,me+ie.x,Me+we.y))return de.actions[Ye]}},[et]),er=d.useCallback((V,ie,me,Me)=>{const de=Et[ie];if(!G&&!H&&!(Ct??!1)){const be=Lt(V,ie,-1);Tn(be!==void 0);const Ye=Rd(void 0,de,be.x,be.y,be.width,be.height,Re,Ns(de.title)==="rtl");if(de.hasMenu===!0&&Ye.menuBounds!==void 0&&Vr(Ye.menuBounds,me,Me))return{area:"menu",bounds:Ye.menuBounds};if(de.indicatorIcon!==void 0&&Ye.indicatorIconBounds!==void 0&&Vr(Ye.indicatorIconBounds,me,Me))return{area:"indicator",bounds:Ye.indicatorIconBounds}}},[Et,Lt,Ct,G,H,Re]),Er=d.useRef(0),qn=d.useRef(),Gn=d.useRef(!1),Ir=d.useCallback(V=>{const ie=We.current,me=re?.current;if(ie===null||V.target!==ie&&V.target!==me)return;Gn.current=!0;let Me,de;if(V instanceof MouseEvent?(Me=V.clientX,de=V.clientY):(Me=V.touches[0].clientX,de=V.touches[0].clientY),V.target===me&&me!==null){const Ye=me.getBoundingClientRect();if(Me>Ye.right||de>Ye.bottom)return}const be=At(ie,Me,de,V);qn.current=be.location,be.isTouch&&(Er.current=Date.now()),rn.current!==be.isTouch&&ln(be.isTouch),!(be.kind===wr&&er(ie,be.location[0],Me,de)!==void 0)&&(be.kind===Ln&&Qn(be.group,be.bounds,be.localEventX,be.localEventY)!==void 0||(I?.(be),!be.isTouch&&ue!==!0&&ue!==be.kind&&be.button<3&&be.button!==1&&V.preventDefault()))},[re,ue,At,Qn,er,I]);dn("touchstart",Ir,qt,!1),dn("mousedown",Ir,qt,!1);const ho=d.useRef(0),Ci=d.useCallback(V=>{const ie=ho.current;ho.current=Date.now();const me=We.current;if(Gn.current=!1,T===void 0||me===null)return;const Me=re?.current,de=V.target!==me&&V.target!==Me;let be,Ye,we=!0;if(V instanceof MouseEvent){if(be=V.clientX,Ye=V.clientY,we=V.button<3,V.pointerType==="touch")return}else be=V.changedTouches[0].clientX,Ye=V.changedTouches[0].clientY;let Ue=At(me,be,Ye,V);Ue.isTouch&&Er.current!==0&&Date.now()-Er.current>500&&(Ue={...Ue,isLongTouch:!0}),ie!==0&&Date.now()-ie<(Ue.isTouch?1e3:500)&&(Ue={...Ue,isDoubleClick:!0}),rn.current!==Ue.isTouch&&ln(Ue.isTouch),!de&&V.cancelable&&we&&V.preventDefault();const[Fe]=Ue.location,St=er(me,Fe,be,Ye);if(Ue.kind===wr&&St!==void 0){(Ue.button!==0||qn.current?.[0]!==Fe||qn.current?.[1]!==-1)&&T(Ue,!0);return}else if(Ue.kind===Ln){const lt=Qn(Ue.group,Ue.bounds,Ue.localEventX,Ue.localEventY);if(lt!==void 0){Ue.button===0&&lt.onClick(Ue);return}}T(Ue,de)},[T,re,At,er,Qn]);dn("mouseup",Ci,qt,!1),dn("touchend",Ci,qt,!1);const Ve=d.useCallback(V=>{const ie=We.current;if(ie===null)return;const me=re?.current,Me=V.target!==ie&&V.target!==me;let de,be,Ye=!0;V instanceof MouseEvent?(de=V.clientX,be=V.clientY,Ye=V.button<3):(de=V.changedTouches[0].clientX,be=V.changedTouches[0].clientY);const we=At(ie,de,be,V);rn.current!==we.isTouch&&ln(we.isTouch),!Me&&V.cancelable&&Ye&&V.preventDefault();const[Ue]=we.location;if(we.kind===wr){const Fe=er(ie,Ue,de,be);Fe!==void 0&&we.button===0&&qn.current?.[0]===Ue&&qn.current?.[1]===-1&&(Fe.area==="menu"?p?.(Ue,Fe.bounds):Fe.area==="indicator"&&w?.(Ue,Fe.bounds))}else if(we.kind===Ln){const Fe=Qn(we.group,we.bounds,we.localEventX,we.localEventY);Fe!==void 0&&we.button===0&&Fe.onClick(we)}},[re,At,er,p,w,Qn]);dn("click",Ve,qt,!1);const go=d.useCallback(V=>{const ie=We.current,me=re?.current;if(ie===null||V.target!==ie&&V.target!==me||E===void 0)return;const Me=At(ie,V.clientX,V.clientY,V);E(Me,()=>{V.cancelable&&V.preventDefault()})},[re,At,E]);dn("contextmenu",go,re?.current??null,!1);const mo=d.useCallback(V=>{_t.current=new Ji(V.map(ie=>ie.item)),Sn.current=V,Bt.current(),_t.current=void 0},[]),ur=d.useMemo(()=>new e0(mo),[mo]),po=d.useRef(ur);po.current=ur,d.useLayoutEffect(()=>{const V=po.current;if(xn===void 0||xn[1]<0){V.setHovered(xn);return}const ie=h(xn,!0),me=Je(ie),Me=me===void 0&&ie.kind===Z.Custom||me?.needsHover!==void 0&&(typeof me.needsHover=="boolean"?me.needsHover:me.needsHover(ie));V.setHovered(Me?xn:void 0)},[h,Je,xn]);const zn=d.useRef(),Si=d.useCallback(V=>{const ie=We.current;if(ie===null)return;const me=re?.current,Me=V.target!==ie&&V.target!==me,de=At(ie,V.clientX,V.clientY,V);if(de.kind!=="out-of-bounds"&&Me&&!Gn.current&&!de.isTouch)return;const be=(we,Ue)=>{nn(Fe=>Fe===we||Fe?.[0][0]===we?.[0][0]&&Fe?.[0][1]===we?.[0][1]&&(Fe?.[1][0]===we?.[1][0]&&Fe?.[1][1]===we?.[1][1]||!Ue)?Fe:we)};if(!Td(de,zn.current))Dt(void 0),q?.(de),be(de.kind===aa?void 0:[de.location,[de.localEventX,de.localEventY]],!0),zn.current=de;else if(de.kind==="cell"||de.kind===wr||de.kind===Ln){let we=!1,Ue=!0;if(de.kind==="cell"){const St=h(de.location);Ue=Je(St)?.needsHoverPosition??St.kind===Z.Custom,we=Ue}else we=!0;const Fe=[de.location,[de.localEventX,de.localEventY]];be(Fe,Ue),Hn.current=Fe,we&&an(new Ji([de.location]))}const Ye=de.location[0]>=(oe?0:1);Ft(de.kind===wr&&de.isEdge&&Ye&&he===!0),xa(de.kind==="cell"&&de.isFillHandle),x?.(V),$(de)},[re,At,oe,he,x,$,q,h,Je,an]);dn("mousemove",Si,qt,!0);const vo=d.useCallback(V=>{const ie=We.current;if(ie===null)return;let me,Me;M.current!==void 0&&(me=Lt(ie,M.current.cell[0],M.current.cell[1]),Me=M.current.cell),Q?.({bounds:me,stopPropagation:()=>V.stopPropagation(),preventDefault:()=>V.preventDefault(),cancel:()=>{},ctrlKey:V.ctrlKey,metaKey:V.metaKey,shiftKey:V.shiftKey,altKey:V.altKey,key:V.key,keyCode:V.keyCode,rawEvent:V,location:Me})},[Q,M,Lt]),bo=d.useCallback(V=>{const ie=We.current;if(ie===null)return;let me,Me;M.current!==void 0&&(me=Lt(ie,M.current.cell[0],M.current.cell[1]),Me=M.current.cell),J?.({bounds:me,stopPropagation:()=>V.stopPropagation(),preventDefault:()=>V.preventDefault(),cancel:()=>{},ctrlKey:V.ctrlKey,metaKey:V.metaKey,shiftKey:V.shiftKey,altKey:V.altKey,key:V.key,keyCode:V.keyCode,rawEvent:V,location:Me})},[J,M,Lt]),Ma=d.useCallback(V=>{if(We.current=V,ae!==void 0&&(ae.current=V),Oe?.eventTarget)xt.current=Oe.eventTarget;else if(V===null)xt.current=window;else{const ie=V.getRootNode();ie===document&&(xt.current=window),xt.current=ie}},[ae,Oe?.eventTarget]),Ra=d.useCallback(V=>{const ie=We.current;if(ie===null||ue===!1||H){V.preventDefault();return}let me,Me;const de=At(ie,V.clientX,V.clientY);if(ue!==!0&&de.kind!==ue){V.preventDefault();return}const be=(lt,at)=>{me=lt,Me=at};let Ye,we,Ue;const Fe=(lt,at,dt)=>{Ye=lt,we=at,Ue=dt};let St=!1;if(le?.({...de,setData:be,setDragImage:Fe,preventDefault:()=>St=!0,defaultPrevented:()=>St}),!St&&me!==void 0&&Me!==void 0&&V.dataTransfer!==null)if(V.dataTransfer.setData(me,Me),V.dataTransfer.effectAllowed="copyLink",Ye!==void 0&&we!==void 0&&Ue!==void 0)V.dataTransfer.setDragImage(Ye,we,Ue);else{const[lt,at]=de.location;if(at!==void 0){const dt=document.createElement("canvas"),Gt=Lt(ie,lt,at);Tn(Gt!==void 0);const gn=Math.ceil(window.devicePixelRatio??1);dt.width=Gt.width*gn,dt.height=Gt.height*gn;const ct=dt.getContext("2d");ct!==null&&(ct.scale(gn,gn),ct.textBaseline="middle",at===-1?(ct.font=Re.headerFontFull,ct.fillStyle=Re.bgHeader,ct.fillRect(0,0,dt.width,dt.height),Ed(ct,0,0,Gt.width,Gt.height,Et[lt],!1,Re,!1,void 0,void 0,!1,0,hn,Ee,!1)):(ct.font=Re.baseFontFull,ct.fillStyle=Re.bgCell,ct.fillRect(0,0,dt.width,dt.height),kd(ct,h([lt,at]),0,at,!1,!1,0,0,Gt.width,Gt.height,!1,Re,Re.bgCell,Tt,hn,1,void 0,!1,0,void 0,void 0,void 0,Pe,Je,()=>{}))),dt.style.left="-100%",dt.style.position="absolute",dt.style.width=`${Gt.width}px`,dt.style.height=`${Gt.height}px`,document.body.append(dt),V.dataTransfer.setDragImage(dt,Gt.width/2,Gt.height/2),window.setTimeout(()=>{dt.remove()},0)}}else V.preventDefault()},[ue,H,At,le,Lt,Re,Et,hn,Ee,h,Tt,Pe,Je]);dn("dragstart",Ra,re?.current??null,!1,!1);const On=d.useRef(),cr=d.useCallback(V=>{const ie=We.current;if(pe!==void 0&&V.preventDefault(),ie===null||ce===void 0)return;const me=At(ie,V.clientX,V.clientY),[Me,de]=me.location,be=Me-(oe?0:1),[Ye,we]=On.current??[];(Ye!==be||we!==de)&&(On.current=[be,de],ce([be,de],V.dataTransfer))},[oe,At,ce,pe]);dn("dragover",cr,re?.current??null,!1,!1);const Pn=d.useCallback(()=>{On.current=void 0,fe?.()},[fe]);dn("dragend",Pn,re?.current??null,!1,!1);const B=d.useCallback(V=>{const ie=We.current;if(ie===null||pe===void 0)return;V.preventDefault();const me=At(ie,V.clientX,V.clientY),[Me,de]=me.location,be=Me-(oe?0:1);pe([be,de],V.dataTransfer)},[oe,At,pe]);dn("drop",B,re?.current??null,!1,!1);const Wt=d.useCallback(()=>{ze?.()},[ze]);dn("dragleave",Wt,re?.current??null,!1,!1);const dr=d.useRef(M);dr.current=M;const Yr=d.useRef(null),Xr=d.useCallback(V=>{We.current===null||!We.current.contains(document.activeElement)||(V===null&&dr.current.current!==void 0?ae?.current?.focus({preventScroll:!0}):V!==null&&V.focus({preventScroll:!0}),Yr.current=V)},[ae]);d.useImperativeHandle(t,()=>({focus:()=>{const V=Yr.current;V===null||!document.contains(V)?ae?.current?.focus({preventScroll:!0}):V.focus({preventScroll:!0})},getBounds:(V,ie)=>{if(!(ae===void 0||ae.current===null))return Lt(ae.current,V??0,ie??-1)},damage:co,getMouseArgsForPosition:(V,ie,me)=>{if(!(ae===void 0||ae.current===null))return At(ae.current,V,ie,me)}}),[ae,co,Lt]);const xi=d.useRef(),Ea=Xg(()=>{if(n<50||Oe?.disableAccessibilityTree===!0)return null;let V=Ss(Et,ft,n,X,qe);const ie=oe?0:-1;!oe&&V[0]?.sourceIndex===0&&(V=V.slice(1));const[me,Me]=M.current?.cell??[],de=M.current?.range,be=V.map(we=>we.sourceIndex),Ye=Cr(a,Math.min(g,a+i));return me!==void 0&&Me!==void 0&&!(be.includes(me)&&Ye.includes(Me))&&Xr(null),d.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":g+1,"aria-multiselectable":"true","aria-colcount":Et.length+ie},d.createElement("thead",{role:"rowgroup"},d.createElement("tr",{role:"row","aria-rowindex":1},V.map(we=>d.createElement("th",{role:"columnheader","aria-selected":M.columns.hasIndex(we.sourceIndex),"aria-colindex":we.sourceIndex+1+ie,tabIndex:-1,onFocus:Ue=>{if(Ue.target!==Yr.current)return xe?.([we.sourceIndex,-1])},key:we.sourceIndex},we.title)))),d.createElement("tbody",{role:"rowgroup"},Ye.map(we=>d.createElement("tr",{role:"row","aria-selected":M.rows.hasIndex(we),key:we,"aria-rowindex":we+2},V.map(Ue=>{const Fe=Ue.sourceIndex,St=Wn(Fe,we),lt=me===Fe&&Me===we,at=de!==void 0&&Fe>=de.x&&Fe<de.x+de.width&&we>=de.y&&we<de.y+de.height,dt=`glide-cell-${Fe}-${we}`,Gt=[Fe,we],gn=h(Gt,!0);return d.createElement("td",{key:St,role:"gridcell","aria-colindex":Fe+1+ie,"aria-selected":at,"aria-readonly":ui(gn)||!Yi(gn),id:dt,"data-testid":dt,onClick:()=>{const ct=ae?.current;if(ct!=null)return Q?.({bounds:Lt(ct,Fe,we),cancel:()=>{},preventDefault:()=>{},stopPropagation:()=>{},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:Gt})},onFocusCapture:ct=>{if(!(ct.target===Yr.current||xi.current?.[0]===Fe&&xi.current?.[1]===we))return xi.current=Gt,xe?.(Gt)},ref:lt?Xr:void 0,tabIndex:-1},i0(gn,Je))})))))},[n,Et,ft,X,qe,g,a,i,M,Xr,h,ae,Q,Lt,xe],200),ki=_===0||!F?0:ft>_?1:Fn(-qe/100,0,1),K=-a*32+pt,un=D?Fn(-K/100,0,1):0,on=d.useMemo(()=>{if(!ki&&!un)return null;const V={position:"absolute",top:0,left:Qt,width:n-Qt,height:r,opacity:ki,pointerEvents:"none",transition:tt?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},ie={position:"absolute",top:bn,left:0,width:n,height:r,opacity:un,pointerEvents:"none",transition:Te?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return d.createElement(d.Fragment,null,ki>0&&d.createElement("div",{id:"shadow-x",style:V}),un>0&&d.createElement("div",{id:"shadow-y",style:ie}))},[ki,un,Qt,n,tt,bn,r,Te]),Ia=d.useMemo(()=>({position:"absolute",top:0,left:0}),[]);return d.createElement(d.Fragment,null,d.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:vo,onKeyUp:bo,onFocus:S,onBlur:O,ref:Ma,style:wi},Ea),d.createElement("canvas",{ref:Yt,style:Ia}),on)},a0=d.memo(d.forwardRef(o0));function Wi(e,t,n,r){return Fn(Math.round(t-(e.growOffset??0)),Math.ceil(n),Math.floor(r))}const s0=e=>{const[t,n]=d.useState(),[r,i]=d.useState(),[o,s]=d.useState(),[a,l]=d.useState(),[u,c]=d.useState(!1),[f,g]=d.useState(),[h,m]=d.useState(),[p,w]=d.useState(),[b,v]=d.useState(!1),[S,O]=d.useState(),{onHeaderMenuClick:R,onHeaderIndicatorClick:M,getCellContent:_,onColumnMoved:E,onColumnResize:k,onColumnResizeStart:F,onColumnResizeEnd:D,gridRef:C,maxColumnWidth:I,minColumnWidth:T,onRowMoved:x,lockColumns:$,onColumnProposeMove:q,onMouseDown:X,onMouseUp:oe,onItemHovered:Q,onDragStart:J,canvasRef:te}=e,ae=(k??D??F)!==void 0,{columns:le,selection:fe}=e,re=fe.columns,H=d.useCallback(ne=>{const[Ee,xe]=ne.location;o!==void 0&&a!==Ee&&Ee>=$?(c(!0),l(Ee)):h!==void 0&&xe!==void 0?(v(!0),w(Math.max(0,xe))):r===void 0&&!u&&!b&&Q?.(ne)},[o,h,a,Q,$,r,u,b]),P=E!==void 0,G=d.useCallback(ne=>{if(ne.button===0){const[Ee,xe]=ne.location;if(ne.kind==="out-of-bounds"&&ne.isEdge&&ae){const ce=C?.current?.getBounds(le.length-1,-1);ce!==void 0&&(n(ce.x),i(le.length-1))}else if(ne.kind==="header"&&Ee>=$){const ce=te?.current;if(ne.isEdge&&ae&&ce){n(ne.bounds.x),i(Ee);const ze=ce.getBoundingClientRect().width/ce.offsetWidth,Ie=ne.bounds.width/ze;F?.(le[Ee],Ie,Ee,Ie+(le[Ee].growOffset??0))}else ne.kind==="header"&&P&&(g(ne.bounds.x),s(Ee))}else ne.kind==="cell"&&$>0&&Ee===0&&xe!==void 0&&x!==void 0&&(O(ne.bounds.y),m(xe))}X?.(ne)},[X,ae,$,x,C,le,P,F,te]),ue=d.useCallback((ne,Ee)=>{u||b||R?.(ne,Ee)},[u,b,R]),he=d.useCallback((ne,Ee)=>{u||b||M?.(ne,Ee)},[u,b,M]),ke=d.useRef(-1),Se=d.useCallback(()=>{ke.current=-1,m(void 0),w(void 0),O(void 0),v(!1),s(void 0),l(void 0),g(void 0),c(!1),i(void 0),n(void 0)},[]),et=d.useCallback((ne,Ee)=>{if(ne.button===0){if(r!==void 0){if(re?.hasIndex(r)===!0)for(const ce of re){if(ce===r)continue;const pe=le[ce],ze=Wi(pe,ke.current,T,I);k?.(pe,ze,ce,ze+(pe.growOffset??0))}const xe=Wi(le[r],ke.current,T,I);if(D?.(le[r],xe,r,xe+(le[r].growOffset??0)),re.hasIndex(r))for(const ce of re){if(ce===r)continue;const pe=le[ce],ze=Wi(pe,ke.current,T,I);D?.(pe,ze,ce,ze+(pe.growOffset??0))}}Se(),o!==void 0&&a!==void 0&&q?.(o,a)!==!1&&E?.(o,a),h!==void 0&&p!==void 0&&x?.(h,p)}oe?.(ne,Ee)},[oe,r,o,a,h,p,re,D,le,T,I,k,E,x,Se,q]),Re=d.useMemo(()=>{if(!(o===void 0||a===void 0)&&o!==a&&q?.(o,a)!==!1)return{src:o,dest:a}},[o,a,q]),Xe=d.useCallback(ne=>{const Ee=te?.current;if(o!==void 0&&f!==void 0)Math.abs(ne.clientX-f)>20&&c(!0);else if(h!==void 0&&S!==void 0)Math.abs(ne.clientY-S)>20&&v(!0);else if(r!==void 0&&t!==void 0&&Ee){const ce=Ee.getBoundingClientRect().width/Ee.offsetWidth,pe=(ne.clientX-t)/ce,ze=le[r],Ie=Wi(ze,pe,T,I);if(k?.(ze,Ie,r,Ie+(ze.growOffset??0)),ke.current=pe,re?.first()===r)for(const tt of re){if(tt===r)continue;const Te=le[tt],Oe=Wi(Te,ke.current,T,I);k?.(Te,Oe,tt,Oe+(Te.growOffset??0))}}},[o,f,h,S,r,t,le,T,I,k,re,te]),yt=d.useCallback((ne,Ee)=>{if(h===void 0||p===void 0)return _(ne,Ee);let[xe,ce]=ne;return ce===p?ce=h:(ce>p&&(ce-=1),ce>=h&&(ce+=1)),_([xe,ce],Ee)},[h,p,_]),De=d.useCallback(ne=>{J?.(ne),ne.defaultPrevented()||Se()},[Se,J]);return d.createElement(a0,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,resizeColumn:r,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,resizeIndicator:e.resizeIndicator,verticalBorder:e.verticalBorder,width:e.width,getCellContent:yt,isResizing:r!==void 0,onHeaderMenuClick:ue,onHeaderIndicatorClick:he,isDragging:u,onItemHovered:H,onDragStart:De,onMouseDown:G,allowResize:ae,onMouseUp:et,dragAndDropState:Re,onMouseMoveRaw:Xe,ref:C})};function l0(e){const t=d.useRef(null),[n,r]=d.useState({width:e?.[0],height:e?.[1]});return d.useLayoutEffect(()=>{const i=s=>{for(const a of s){const{width:l,height:u}=a&&a.contentRect||{};r(c=>c.width===l&&c.height===u?c:{width:l,height:u})}},o=new window.ResizeObserver(i);return t.current&&o.observe(t.current,void 0),()=>{o.disconnect()}},[t.current]),{ref:t,...n}}const u0=(e,t,n)=>{const r=d.useRef(null),i=d.useRef(null),o=d.useRef(null),s=d.useRef(0),a=d.useRef(t);a.current=t;const l=n.current;d.useEffect(()=>{const u=()=>{if(i.current===!1&&l!==null){const g=[l.scrollLeft,l.scrollTop];if(o.current?.[0]===g[0]&&o.current?.[1]===g[1])if(s.current>10){o.current=null,i.current=null;return}else s.current++;else s.current=0,a.current(g[0],g[1]),o.current=g;r.current=window.setTimeout(u,8.333333333333334)}},c=()=>{i.current=!0,o.current=null,r.current!==null&&(window.clearTimeout(r.current),r.current=null)},f=g=>{g.touches.length===0&&(i.current=!1,s.current=0,r.current=window.setTimeout(u,8.333333333333334))};if(e&&l!==null){const g=l;return g.addEventListener("touchstart",c),g.addEventListener("touchend",f),()=>{g.removeEventListener("touchstart",c),g.removeEventListener("touchend",f),r.current!==null&&window.clearTimeout(r.current)}}},[e,l])},c0=()=>e=>e.isSafari?"scroll":"auto",d0=fn("div")({name:"ScrollRegionStyle",class:"gdg-s1dgczr6",propsAsIs:!1,vars:{"s1dgczr6-0":[c0()]}}),f0=33554400,h0=5e6;function g0(e){const[t,n]=d.useState(!1),r=typeof window>"u"?null:window,i=d.useRef(0);return dn("touchstart",d.useCallback(()=>{window.clearTimeout(i.current),n(!0)},[]),r,!0,!1),dn("touchend",d.useCallback(o=>{o.touches.length===0&&(i.current=window.setTimeout(()=>n(!1),e))},[e]),r,!0,!1),t}const m0=e=>{const{children:t,clientHeight:n,scrollHeight:r,scrollWidth:i,update:o,draggable:s,className:a,preventDiagonalScrolling:l=!1,paddingBottom:u=0,paddingRight:c=0,rightElement:f,rightElementProps:g,kineticScrollPerfHack:h=!1,scrollRef:m,initialSize:p}=e,w=[],b=g?.sticky??!1,v=g?.fill??!1,S=d.useRef(0),O=d.useRef(0),R=d.useRef(null),M=typeof window>"u"?1:window.devicePixelRatio,_=d.useRef(M);d.useEffect(()=>{if(_.current!==M){S.current=0,O.current=0,_.current=M;const fe=R.current;fe!==null&&x.current(fe.scrollLeft,fe.scrollTop)}},[M]);const E=d.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),k=d.useRef(null),F=g0(200),[D,C]=d.useState(!0),I=d.useRef(0);d.useLayoutEffect(()=>{if(!D||F||E.current.lockDirection===void 0)return;const fe=R.current;if(fe===null)return;const[re,H]=E.current.lockDirection;re!==void 0?fe.scrollLeft=re:H!==void 0&&(fe.scrollTop=H),E.current.lockDirection=void 0},[F,D]);const T=d.useCallback((fe,re)=>{const H=R.current;if(H===null)return;re=re??H.scrollTop,fe=fe??H.scrollLeft;const P=E.current.scrollTop,G=E.current.scrollLeft,ue=fe-G,he=re-P;F&&ue!==0&&he!==0&&(Math.abs(ue)>3||Math.abs(he)>3)&&l&&E.current.lockDirection===void 0&&(E.current.lockDirection=Math.abs(ue)<Math.abs(he)?[G,void 0]:[void 0,P]);const ke=E.current.lockDirection;fe=ke?.[0]??fe,re=ke?.[1]??re,E.current.scrollLeft=fe,E.current.scrollTop=re;const Se=H.clientWidth,et=H.clientHeight,Re=re,Xe=O.current-Re,yt=H.scrollHeight-et;O.current=Re;let De;if(yt>0&&r>H.scrollHeight+5)if(Math.abs(Xe)>2e3||Re===0||Re===yt){const ne=Math.max(0,Math.min(1,Re/yt)),Ee=r-et;De=ne*Ee,S.current=De}else S.current-=Xe,De=S.current;else De=Re,S.current=De;De=Math.max(0,Math.min(De,r-et)),S.current=De,ke!==void 0&&(window.clearTimeout(I.current),C(!1),I.current=window.setTimeout(()=>C(!0),200)),o({x:fe,y:De,width:Se-c,height:et-u,paddingRight:k.current?.clientWidth??0})},[u,c,r,o,l,F]);u0(h&&ia.value,T,R);const x=d.useRef(T);x.current=T;const $=d.useRef(),q=d.useRef(!1);d.useLayoutEffect(()=>{q.current?T():q.current=!0},[T,u,c]);const X=d.useCallback(fe=>{R.current=fe,m!==void 0&&(m.current=fe)},[m]);let oe=0,Q=0;const J=Math.min(r,f0);for(w.push(d.createElement("div",{key:oe++,style:{width:i,height:0}}));Q<J;){const fe=Math.min(h0,J-Q);w.push(d.createElement("div",{key:oe++,style:{width:0,height:fe}})),Q+=fe}const{ref:te,width:ae,height:le}=l0(p);return typeof window<"u"&&($.current?.height!==le||$.current?.width!==ae)&&(window.setTimeout(()=>x.current(),0),$.current={width:ae,height:le}),(ae??0)===0||(le??0)===0?d.createElement("div",{ref:te}):d.createElement("div",{ref:te},d.createElement(d0,{isSafari:ia.value},d.createElement("div",{className:"dvn-underlay"},t),d.createElement("div",{ref:X,style:$.current,draggable:s,onDragStart:fe=>{s||(fe.stopPropagation(),fe.preventDefault())},className:"dvn-scroller "+(a??""),onScroll:()=>T()},d.createElement("div",{className:"dvn-scroll-inner"+(f===void 0?" dvn-hidden":"")},d.createElement("div",{className:"dvn-stack"},w),f!==void 0&&d.createElement(d.Fragment,null,!v&&d.createElement("div",{className:"dvn-spacer"}),d.createElement("div",{ref:k,style:{height:le,maxHeight:n-Math.ceil(M%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:c,flexGrow:v?1:void 0,right:b?c??0:void 0,pointerEvents:"auto"}},f))))))},p0=e=>{const{columns:t,rows:n,rowHeight:r,headerHeight:i,groupHeaderHeight:o,enableGroups:s,freezeColumns:a,experimental:l,nonGrowWidth:u,clientSize:c,className:f,onVisibleRegionChanged:g,scrollRef:h,preventDiagonalScrolling:m,rightElement:p,rightElementProps:w,overscrollX:b,overscrollY:v,initialSize:S,smoothScrollX:O=!1,smoothScrollY:R=!1,isDraggable:M}=e,{paddingRight:_,paddingBottom:E}=l??{},[k,F]=c,D=d.useRef(),C=d.useRef(),I=d.useRef(),T=d.useRef(),x=u+Math.max(0,b??0);let $=s?i+o:i;if(typeof r=="number")$+=n*r;else for(let Q=0;Q<n;Q++)$+=r(Q);v!==void 0&&($+=v);const q=d.useRef(),X=d.useCallback(()=>{if(q.current===void 0)return;const Q={...q.current};let J=0,te=Q.x<0?-Q.x:0,ae=0,le=0;Q.x=Q.x<0?0:Q.x;let fe=0;for(let he=0;he<a;he++)fe+=t[he].width;for(const he of t){const ke=J-fe;if(Q.x>=ke+he.width)J+=he.width,le++,ae++;else if(Q.x>ke)J+=he.width,O?te+=ke-Q.x:le++,ae++;else if(Q.x+Q.width>ke)J+=he.width,ae++;else break}let re=0,H=0,P=0;if(typeof r=="number")R?(H=Math.floor(Q.y/r),re=H*r-Q.y):H=Math.ceil(Q.y/r),P=Math.ceil(Q.height/r)+H,re<0&&P++;else{let he=0;for(let ke=0;ke<n;ke++){const Se=r(ke),et=he+(R?0:Se/2);if(Q.y>=he+Se)he+=Se,H++,P++;else if(Q.y>et)he+=Se,R?re+=et-Q.y:H++,P++;else if(Q.y+Q.height>Se/2+he)he+=Se,P++;else break}}H=Math.max(0,Math.min(H,n-1)),P=Math.max(H,Math.min(P,n));const G={x:le,y:H,width:ae-le,height:P-H},ue=D.current;(ue===void 0||ue.y!==G.y||ue.x!==G.x||ue.height!==G.height||ue.width!==G.width||C.current!==te||I.current!==re||Q.width!==T.current?.[0]||Q.height!==T.current?.[1])&&(g?.({x:le,y:H,width:ae-le,height:P-H},Q.width,Q.height,Q.paddingRight??0,te,re),D.current=G,C.current=te,I.current=re,T.current=[Q.width,Q.height])},[t,r,n,g,a,O,R]),oe=d.useCallback(Q=>{q.current=Q,X()},[X]);return d.useEffect(()=>{X()},[X]),d.createElement(m0,{scrollRef:h,className:f,kineticScrollPerfHack:l?.kineticScrollPerfHack,preventDiagonalScrolling:m,draggable:M===!0||typeof M=="string",scrollWidth:x+(_??0),scrollHeight:$+(E??0),clientHeight:F,rightElement:p,paddingBottom:E,paddingRight:_,rightElementProps:w,update:oe,initialSize:S},d.createElement(s0,{eventTargetRef:h,width:k,height:F,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,onColumnProposeMove:e.onColumnProposeMove,verticalBorder:e.verticalBorder,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}))},v0=fn("div")({name:"SearchWrapper",class:"gdg-seveqep",propsAsIs:!1}),b0=d.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},d.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),w0=d.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},d.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),y0=d.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},d.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),C0=10,S0=e=>{const{canvasRef:t,cellYOffset:n,rows:r,columns:i,searchInputRef:o,searchValue:s,searchResults:a,onSearchValueChange:l,getCellsForSelection:u,onSearchResultsChanged:c,showSearch:f=!1,onSearchClose:g}=e,[h]=d.useState(()=>"search-box-"+Math.round(Math.random()*1e3)),[m,p]=d.useState(""),w=s??m,b=d.useCallback(J=>{p(J),l?.(J)},[l]),[v,S]=d.useState(),O=d.useRef(v);O.current=v,d.useEffect(()=>{a!==void 0&&(a.length>0?S(J=>({rowsSearched:r,results:a.length,selectedIndex:J?.selectedIndex??-1})):S(void 0))},[r,a]);const R=d.useRef();R.current===void 0&&(R.current=new AbortController);const M=d.useRef(),[_,E]=d.useState([]),k=a??_,F=d.useCallback(()=>{M.current!==void 0&&(window.cancelAnimationFrame(M.current),M.current=void 0,R.current.abort())},[]),D=d.useRef(n);D.current=n;const C=d.useCallback(J=>{const te=new RegExp(J.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i");let ae=D.current,le=Math.min(10,r),fe=0;S(void 0),E([]);const re=[],H=async()=>{if(u===void 0)return;const P=performance.now(),G=r-fe;let ue=u({x:0,y:ae,width:i.length,height:Math.min(le,G,r-ae)},R.current.signal);typeof ue=="function"&&(ue=await ue());let he=!1;for(const[yt,De]of ue.entries())for(const[ne,Ee]of De.entries()){let xe;switch(Ee.kind){case Z.Text:case Z.Number:xe=Ee.displayData;break;case Z.Uri:case Z.Markdown:xe=Ee.data;break;case Z.Boolean:xe=typeof Ee.data=="boolean"?Ee.data.toString():void 0;break;case Z.Image:case Z.Bubble:xe=Ee.data.join("🐳");break;case Z.Custom:xe=Ee.copyData;break}xe!==void 0&&te.test(xe)&&(re.push([ne,yt+ae]),he=!0)}const ke=performance.now();he&&E([...re]),fe+=ue.length,Tn(fe<=r);const Se=O.current?.selectedIndex??-1;S({results:re.length,rowsSearched:fe,selectedIndex:Se}),c?.(re,Se),ae+le>=r?ae=0:ae+=le;const et=ke-P,Re=Math.max(et,1),Xe=C0/Re;le=Math.ceil(le*Xe),fe<r&&re.length<1e3&&(M.current=window.requestAnimationFrame(H))};F(),M.current=window.requestAnimationFrame(H)},[F,i.length,u,c,r]),I=d.useCallback(()=>{g?.(),S(void 0),E([]),c?.([],-1),F(),t?.current?.focus()},[F,t,g,c]),T=d.useCallback(J=>{b(J.target.value),a===void 0&&(J.target.value===""?(S(void 0),E([]),F()):C(J.target.value))},[C,F,b,a]);d.useEffect(()=>{f&&o.current!==null&&(b(""),o.current.focus({preventScroll:!0}))},[f,o,b]);const x=d.useCallback(J=>{if(J?.stopPropagation?.(),v===void 0)return;const te=(v.selectedIndex+1)%v.results;S({...v,selectedIndex:te}),c?.(k,te)},[v,c,k]),$=d.useCallback(J=>{if(J?.stopPropagation?.(),v===void 0)return;let te=(v.selectedIndex-1)%v.results;te<0&&(te+=v.results),S({...v,selectedIndex:te}),c?.(k,te)},[c,k,v]),q=d.useCallback(J=>{(J.ctrlKey||J.metaKey)&&J.nativeEvent.code==="KeyF"||J.key==="Escape"?(I(),J.stopPropagation(),J.preventDefault()):J.key==="Enter"&&(J.shiftKey?$():x())},[I,x,$]);d.useEffect(()=>()=>{F()},[F]);const[X,oe]=d.useState(!1);d.useEffect(()=>{if(f)oe(!0);else{const J=setTimeout(()=>oe(!1),150);return()=>clearTimeout(J)}},[f]);const Q=d.useMemo(()=>{if(!f&&!X)return null;let J;v!==void 0&&(J=v.results>=1e3?"over 1000":`${v.results} result${v.results!==1?"s":""}`,v.selectedIndex>=0&&(J=`${v.selectedIndex+1} of ${J}`));const te=fe=>{fe.stopPropagation()},le={width:`${Math.floor((v?.rowsSearched??0)/r*100)}%`};return d.createElement(v0,{className:"gdg-search-bar"+(f?"":" out"),onMouseDown:te,onMouseMove:te,onMouseUp:te,onClick:te},d.createElement("div",{className:"gdg-search-bar-inner"},d.createElement("input",{id:h,"aria-hidden":!f,"data-testid":"search-input",ref:o,onChange:T,value:w,tabIndex:f?void 0:-1,onKeyDownCapture:q}),d.createElement("button",{type:"button","aria-label":"Previous Result","aria-hidden":!f,tabIndex:f?void 0:-1,onClick:$,disabled:(v?.results??0)===0},b0),d.createElement("button",{type:"button","aria-label":"Next Result","aria-hidden":!f,tabIndex:f?void 0:-1,onClick:x,disabled:(v?.results??0)===0},w0),g!==void 0&&d.createElement("button",{type:"button","aria-label":"Close Search","aria-hidden":!f,"data-testid":"search-close-button",tabIndex:f?void 0:-1,onClick:I},y0)),v!==void 0?d.createElement(d.Fragment,null,d.createElement("div",{className:"gdg-search-status"},d.createElement("div",{"data-testid":"search-result-area"},J)),d.createElement("div",{className:"gdg-search-progress",style:le})):d.createElement("div",{className:"gdg-search-status"},d.createElement("label",{htmlFor:h},"Type to search")))},[f,X,v,r,h,o,T,w,q,$,x,g,I]);return d.createElement(d.Fragment,null,d.createElement(p0,{prelightCells:k,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,nonGrowWidth:e.nonGrowWidth,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,onColumnProposeMove:e.onColumnProposeMove,drawFocusRing:e.drawFocusRing,drawCell:e.drawCell,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}),Q)};class x0 extends d.PureComponent{wrapperRef=d.createRef();componentDidMount(){const t=this.props.customEventTarget??document;t.addEventListener("touchend",this.clickOutside,!0),t.addEventListener("mousedown",this.clickOutside,!0),t.addEventListener("contextmenu",this.clickOutside,!0)}componentWillUnmount(){const t=this.props.customEventTarget??document;t.removeEventListener("touchend",this.clickOutside,!0),t.removeEventListener("mousedown",this.clickOutside,!0),t.removeEventListener("contextmenu",this.clickOutside,!0)}clickOutside=t=>{if(!(this.props.isOutsideClick&&!this.props.isOutsideClick(t))&&this.wrapperRef.current!==null&&!this.wrapperRef.current.contains(t.target)){let n=t.target;for(;n!==null;){if(n.classList.contains("click-outside-ignore"))return;n=n.parentElement}this.props.onClickOutside()}};render(){const{onClickOutside:t,isOutsideClick:n,customEventTarget:r,...i}=this.props;return d.createElement("div",{...i,ref:this.wrapperRef},this.props.children)}}const k0=()=>e=>Math.max(16,e.targetHeight-10),M0=fn("input")({name:"RenameInput",class:"gdg-r17m35ur",propsAsIs:!1,vars:{"r17m35ur-0":[k0(),"px"]}}),R0=e=>{const{bounds:t,group:n,onClose:r,canvasBounds:i,onFinish:o}=e,[s,a]=Nt.useState(n);return Nt.createElement(x0,{style:{position:"absolute",left:t.x-i.left+1,top:t.y-i.top,width:t.width-2,height:t.height},className:"gdg-c1tqibwd",onClickOutside:r},Nt.createElement(M0,{targetHeight:t.height,"data-testid":"group-rename-input",value:s,onBlur:r,onFocus:l=>l.target.setSelectionRange(0,s.length),onChange:l=>a(l.target.value),onKeyDown:l=>{l.key==="Enter"?o(s):l.key==="Escape"&&r()},autoFocus:!0}))};function E0(e,t){return e===void 0?!1:e.length>1&&e.startsWith("_")?Number.parseInt(e.slice(1))===t.keyCode:e.length===1&&e>="a"&&e<="z"?e.toUpperCase().codePointAt(0)===t.keyCode:e===t.key}function it(e,t,n){const r=Dd(e,t);return r&&(n.didMatch=!0),r}function Dd(e,t){if(e.length===0)return!1;if(e.includes("|")){const l=e.split("|");for(const u of l)if(Dd(u,t))return!0;return!1}let n=!1,r=!1,i=!1,o=!1;const s=e.split("+"),a=s.pop();if(!E0(a,t))return!1;if(s[0]==="any")return!0;for(const l of s)switch(l){case"ctrl":n=!0;break;case"shift":r=!0;break;case"alt":i=!0;break;case"meta":o=!0;break;case"primary":oa.value?o=!0:n=!0;break}return t.altKey===i&&t.ctrlKey===n&&t.shiftKey===r&&t.metaKey===o}function I0(e,t,n,r,i,o,s){const a=Nt.useCallback((c,f,g,h)=>{(o==="cell"||o==="multi-cell")&&c!==void 0&&(c={...c,range:{x:c.cell[0],y:c.cell[1],width:1,height:1}}),!s&&c!==void 0&&c.range.width>1&&(c={...c,range:{...c.range,width:1,x:c.cell[0]}});const m=n==="mixed"&&(g||h==="drag"),p=r==="mixed"&&m,w=i==="mixed"&&m;let b={current:c===void 0?void 0:{...c,rangeStack:h==="drag"?e.current?.rangeStack??[]:[]},columns:p?e.columns:rt.empty(),rows:w?e.rows:rt.empty()};g&&(o==="multi-rect"||o==="multi-cell")&&b.current!==void 0&&e.current!==void 0&&(b={...b,current:{...b.current,rangeStack:[...e.current.rangeStack,e.current.range]}}),t(b,f)},[r,e,n,o,s,i,t]),l=Nt.useCallback((c,f,g)=>{c=c??e.rows,f!==void 0&&(c=c.add(f));let h;if(i==="exclusive"&&c.length>0)h={current:void 0,columns:rt.empty(),rows:c};else{const m=g&&n==="mixed",p=g&&r==="mixed";h={current:m?e.current:void 0,columns:p?e.columns:rt.empty(),rows:c}}t(h,!1)},[r,e,n,i,t]),u=Nt.useCallback((c,f,g)=>{c=c??e.columns,f!==void 0&&(c=c.add(f));let h;if(r==="exclusive"&&c.length>0)h={current:void 0,rows:rt.empty(),columns:c};else{const m=g&&n==="mixed",p=g&&i==="mixed";h={current:m?e.current:void 0,rows:p?e.rows:rt.empty(),columns:c}}t(h,!1)},[r,e,n,i,t]);return[a,l,u]}function T0(e,t,n,r,i){const o=d.useCallback(u=>{if(e===!0){const c=[];for(let f=u.y;f<u.y+u.height;f++){const g=[];for(let h=u.x;h<u.x+u.width;h++)h<0||f>=i?g.push({kind:Z.Loading,allowOverlay:!1}):g.push(t([h,f]));c.push(g)}return c}return e?.(u,r.signal)??[]},[r.signal,t,e,i]),s=e!==void 0?o:void 0,a=d.useCallback(u=>{if(s===void 0)return[];const c={...u,x:u.x-n};if(c.x<0){c.x=0,c.width--;const f=s(c,r.signal);return typeof f=="function"?async()=>(await f()).map(g=>[{kind:Z.Loading,allowOverlay:!1},...g]):f.map(g=>[{kind:Z.Loading,allowOverlay:!1},...g])}return s(c,r.signal)},[r.signal,s,n]);return[e!==void 0?a:void 0,s]}function D0(e){if(e.copyData!==void 0)return{formatted:e.copyData,rawValue:e.copyData,format:"string",doNotEscape:!0};switch(e.kind){case Z.Boolean:return{formatted:e.data===!0?"TRUE":e.data===!1?"FALSE":e.data===As?"INDETERMINATE":"",rawValue:e.data,format:"boolean"};case Z.Custom:return{formatted:e.copyData,rawValue:e.copyData,format:"string"};case Z.Image:case Z.Bubble:return{formatted:e.data,rawValue:e.data,format:"string-array"};case Z.Drilldown:return{formatted:e.data.map(t=>t.text),rawValue:e.data.map(t=>t.text),format:"string-array"};case Z.Text:return{formatted:e.displayData??e.data,rawValue:e.data,format:"string"};case Z.Uri:return{formatted:e.displayData??e.data,rawValue:e.data,format:"url"};case Z.Markdown:case Z.RowID:return{formatted:e.data,rawValue:e.data,format:"string"};case Z.Number:return{formatted:e.displayData,rawValue:e.data,format:"number"};case Z.Loading:return{formatted:"#LOADING",rawValue:"",format:"string"};case Z.Protected:return{formatted:"************",rawValue:"",format:"string"};default:eo()}}function O0(e,t){return e.map((r,i)=>{const o=t[i];return r.map(s=>s.span!==void 0&&s.span[0]!==o?{formatted:"",rawValue:"",format:"string"}:D0(s))})}function Mu(e,t){return(t?/[\t\n",]/:/[\t\n"]/).test(e)&&(e=`"${e.replace(/"/g,'""')}"`),e}function P0(e){const t=[];for(const n of e){const r=[];for(const i of n)i.format==="url"?r.push(i.rawValue?.toString()??""):i.format==="string-array"?r.push(i.formatted.map(o=>Mu(o,!0)).join(",")):r.push(i.doNotEscape===!0?i.formatted:Mu(i.formatted,!1));t.push(r.join("	"))}return t.join(`
`)}function is(e){return e.replace(/\t/g,"    ").replace(/ {2,}/g,t=>"<span> </span>".repeat(t.length))}function Ru(e){return'"'+e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")+'"'}function _0(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")}function F0(e){const t=[];t.push('<style type="text/css"><!--br {mso-data-placement:same-cell;}--></style>',"<table><tbody>");for(const n of e){t.push("<tr>");for(const r of n){const i=`gdg-format="${r.format}"`;r.format==="url"?t.push(`<td ${i}><a href="${r.rawValue}">${is(r.formatted)}</a></td>`):r.format==="string-array"?t.push(`<td ${i}><ol>${r.formatted.map((o,s)=>`<li gdg-raw-value=${Ru(r.rawValue[s])}>`+is(o)+"</li>").join("")}</ol></td>`):t.push(`<td gdg-raw-value=${Ru(r.rawValue?.toString()??"")} ${i}>${is(r.formatted)}</td>`)}t.push("</tr>")}return t.push("</tbody></table>"),t.join("")}function L0(e,t){const n=O0(e,t),r=P0(n),i=F0(n);return{textPlain:r,textHtml:i}}function Eu(e){const t=document.createElement("html");t.innerHTML=e.replace(/&nbsp;/g," ");const n=t.querySelector("table");if(n===null)return;const r=[n],i=[];let o;for(;r.length>0;){const s=r.pop();if(s===void 0)break;if(s instanceof HTMLTableElement||s.nodeName==="TBODY")r.push(...[...s.children].reverse());else if(s instanceof HTMLTableRowElement)o!==void 0&&i.push(o),o=[],r.push(...[...s.children].reverse());else if(s instanceof HTMLTableCellElement){const a=s.cloneNode(!0),u=a.children.length===1&&a.children[0].nodeName==="P"?a.children[0]:null,c=u?.children.length===1&&u.children[0].nodeName==="FONT",f=a.querySelectorAll("br");for(const m of f)m.replaceWith(`
`);const g=a.getAttribute("gdg-raw-value"),h=a.getAttribute("gdg-format")??"string";if(a.querySelector("a")!==null)o?.push({rawValue:a.querySelector("a")?.getAttribute("href")??"",formatted:a.textContent??"",format:h});else if(a.querySelector("ol")!==null){const m=a.querySelectorAll("li");o?.push({rawValue:[...m].map(p=>p.getAttribute("gdg-raw-value")??""),formatted:[...m].map(p=>p.textContent??""),format:"string-array"})}else if(g!==null)o?.push({rawValue:_0(g),formatted:a.textContent??"",format:h});else{let m=a.textContent??"";c&&(m=m.replace(/\n(?!\n)/g,"")),o?.push({rawValue:m??"",formatted:m??"",format:h})}}}return o!==void 0&&i.push(o),i}function A0(e,t,n,r,i){const o=e;if(r==="allowPartial"||e.current===void 0||t===void 0)return e;let s=!1;do{if(e?.current===void 0)break;const a=e.current?.range,l=[];if(a.width>2){const f=t({x:a.x,y:a.y,width:1,height:a.height},i.signal);if(typeof f=="function")return o;l.push(...f);const g=t({x:a.x+a.width-1,y:a.y,width:1,height:a.height},i.signal);if(typeof g=="function")return o;l.push(...g)}else{const f=t({x:a.x,y:a.y,width:a.width,height:a.height},i.signal);if(typeof f=="function")return o;l.push(...f)}let u=a.x-n,c=a.x+a.width-1-n;for(const f of l)for(const g of f)g.span!==void 0&&(u=Math.min(g.span[0],u),c=Math.max(g.span[1],c));u===a.x-n&&c===a.x+a.width-1-n?s=!0:e={current:{cell:e.current.cell??[0,0],range:{x:u+n,y:a.y,width:c-u+1,height:a.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!s);return e}function Iu(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function H0(e){let t;(function(a){a[a.None=0]="None",a[a.inString=1]="inString",a[a.inStringPostQuote=2]="inStringPostQuote"})(t||(t={}));const n=[];let r=[],i=0,o=t.None;e=e.replace(/\r\n/g,`
`);let s=0;for(const a of e){switch(o){case t.None:a==="	"||a===`
`?(r.push(e.slice(i,s)),i=s+1,a===`
`&&(n.push(r),r=[])):a==='"'&&(o=t.inString);break;case t.inString:a==='"'&&(o=t.inStringPostQuote);break;case t.inStringPostQuote:a==='"'?o=t.inString:((a==="	"||a===`
`)&&(r.push(Iu(e.slice(i,s))),i=s+1,a===`
`&&(n.push(r),r=[])),o=t.None);break}s++}return i<e.length&&r.push(Iu(e.slice(i,e.length))),n.push(r),n.map(a=>a.map(l=>({rawValue:l,formatted:l,format:"string"})))}function Tu(e,t,n){const r=L0(e,t),i=a=>{window.navigator.clipboard?.writeText(a)},o=(a,l)=>window.navigator.clipboard?.write===void 0?!1:(window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([a],{type:"text/plain"}),"text/html":new Blob([l],{type:"text/html"})})]),!0),s=(a,l)=>{try{if(n===void 0||n.clipboardData===null)throw new Error("No clipboard data");n?.clipboardData?.setData("text/plain",a),n?.clipboardData?.setData("text/html",l)}catch{o(a,l)||i(a)}};window.navigator.clipboard?.write!==void 0||n?.clipboardData!==void 0?s(r.textPlain,r.textHtml):i(r.textPlain),n?.preventDefault()}function Od(e){return e!==!0}function Du(e){return typeof e=="string"?e:`${e}px`}const z0=()=>e=>e.innerWidth,V0=()=>e=>e.innerHeight,N0=fn("div")({name:"Wrapper",class:"gdg-wmyidgi",propsAsIs:!1,vars:{"wmyidgi-0":[z0()],"wmyidgi-1":[V0()]}}),$0=e=>{const{inWidth:t,inHeight:n,children:r,...i}=e;return d.createElement(N0,{innerHeight:Du(n),innerWidth:Du(t),...i},r)},B0=2,W0=1300;function U0(e,t,n){const r=Nt.useRef(0),[i,o]=e??[0,0];Nt.useEffect(()=>{if(i===0&&o===0){r.current=0;return}let s=!1,a=0;const l=u=>{if(!s){if(a===0)a=u;else{const c=u-a;r.current=Math.min(1,r.current+c/W0);const f=r.current**1.618*c*B0;t.current?.scrollBy(i*f,o*f),a=u,n?.()}window.requestAnimationFrame(l)}};return window.requestAnimationFrame(l),()=>{s=!0}},[t,i,o,n])}function q0({rowHeight:e,headerHeight:t,groupHeaderHeight:n,theme:r,overscrollX:i,overscrollY:o,scaleToRem:s,remSize:a}){const[l,u,c,f,g,h]=Nt.useMemo(()=>{if(!s||a===16)return[e,t,n,r,i,o];const m=a/16,p=e,w=pd();return[typeof p=="number"?p*m:b=>Math.ceil(p(b)*m),Math.ceil(t*m),Math.ceil(n*m),{...r,headerIconSize:(r?.headerIconSize??w.headerIconSize)*m,cellHorizontalPadding:(r?.cellHorizontalPadding??w.cellHorizontalPadding)*m,cellVerticalPadding:(r?.cellVerticalPadding??w.cellVerticalPadding)*m},Math.ceil((i??0)*m),Math.ceil((o??0)*m)]},[n,t,i,o,a,e,s,r]);return{rowHeight:l,headerHeight:u,groupHeaderHeight:c,theme:f,overscrollX:g,overscrollY:h}}const vr={downFill:!1,rightFill:!1,clear:!0,closeOverlay:!0,acceptOverlayDown:!0,acceptOverlayUp:!0,acceptOverlayLeft:!0,acceptOverlayRight:!0,copy:!0,paste:!0,cut:!0,search:!1,delete:!0,activateCell:!0,scrollToSelectedCell:!0,goToFirstCell:!0,goToFirstColumn:!0,goToFirstRow:!0,goToLastCell:!0,goToLastColumn:!0,goToLastRow:!0,goToNextPage:!0,goToPreviousPage:!0,selectToFirstCell:!0,selectToFirstColumn:!0,selectToFirstRow:!0,selectToLastCell:!0,selectToLastColumn:!0,selectToLastRow:!0,selectAll:!0,selectRow:!0,selectColumn:!0,goUpCell:!0,goRightCell:!0,goDownCell:!0,goLeftCell:!0,goUpCellRetainSelection:!0,goRightCellRetainSelection:!0,goDownCellRetainSelection:!0,goLeftCellRetainSelection:!0,selectGrowUp:!0,selectGrowRight:!0,selectGrowDown:!0,selectGrowLeft:!0};function ot(e,t){return e===!0?t:e===!1?"":e}function Ou(e){const t=oa.value;return{activateCell:ot(e.activateCell," |Enter|shift+Enter"),clear:ot(e.clear,"any+Escape"),closeOverlay:ot(e.closeOverlay,"any+Escape"),acceptOverlayDown:ot(e.acceptOverlayDown,"Enter"),acceptOverlayUp:ot(e.acceptOverlayUp,"shift+Enter"),acceptOverlayLeft:ot(e.acceptOverlayLeft,"shift+Tab"),acceptOverlayRight:ot(e.acceptOverlayRight,"Tab"),copy:e.copy,cut:e.cut,delete:ot(e.delete,t?"Backspace|Delete":"Delete"),downFill:ot(e.downFill,"primary+_68"),scrollToSelectedCell:ot(e.scrollToSelectedCell,"primary+Enter"),goDownCell:ot(e.goDownCell,"ArrowDown"),goDownCellRetainSelection:ot(e.goDownCellRetainSelection,"alt+ArrowDown"),goLeftCell:ot(e.goLeftCell,"ArrowLeft|shift+Tab"),goLeftCellRetainSelection:ot(e.goLeftCellRetainSelection,"alt+ArrowLeft"),goRightCell:ot(e.goRightCell,"ArrowRight|Tab"),goRightCellRetainSelection:ot(e.goRightCellRetainSelection,"alt+ArrowRight"),goUpCell:ot(e.goUpCell,"ArrowUp"),goUpCellRetainSelection:ot(e.goUpCellRetainSelection,"alt+ArrowUp"),goToFirstCell:ot(e.goToFirstCell,"primary+Home"),goToFirstColumn:ot(e.goToFirstColumn,"Home|primary+ArrowLeft"),goToFirstRow:ot(e.goToFirstRow,"primary+ArrowUp"),goToLastCell:ot(e.goToLastCell,"primary+End"),goToLastColumn:ot(e.goToLastColumn,"End|primary+ArrowRight"),goToLastRow:ot(e.goToLastRow,"primary+ArrowDown"),goToNextPage:ot(e.goToNextPage,"PageDown"),goToPreviousPage:ot(e.goToPreviousPage,"PageUp"),paste:e.paste,rightFill:ot(e.rightFill,"primary+_82"),search:ot(e.search,"primary+f"),selectAll:ot(e.selectAll,"primary+a"),selectColumn:ot(e.selectColumn,"ctrl+ "),selectGrowDown:ot(e.selectGrowDown,"shift+ArrowDown"),selectGrowLeft:ot(e.selectGrowLeft,"shift+ArrowLeft"),selectGrowRight:ot(e.selectGrowRight,"shift+ArrowRight"),selectGrowUp:ot(e.selectGrowUp,"shift+ArrowUp"),selectRow:ot(e.selectRow,"shift+ "),selectToFirstCell:ot(e.selectToFirstCell,"primary+shift+Home"),selectToFirstColumn:ot(e.selectToFirstColumn,"primary+shift+ArrowLeft"),selectToFirstRow:ot(e.selectToFirstRow,"primary+shift+ArrowUp"),selectToLastCell:ot(e.selectToLastCell,"primary+shift+End"),selectToLastColumn:ot(e.selectToLastColumn,"primary+shift+ArrowRight"),selectToLastRow:ot(e.selectToLastRow,"primary+shift+ArrowDown")}}function G0(e){const t=Qg(e);return Nt.useMemo(()=>{if(t===void 0)return Ou(vr);const n={...t,goToNextPage:t?.goToNextPage??t?.pageDown??vr.goToNextPage,goToPreviousPage:t?.goToPreviousPage??t?.pageUp??vr.goToPreviousPage,goToFirstCell:t?.goToFirstCell??t?.first??vr.goToFirstCell,goToLastCell:t?.goToLastCell??t?.last??vr.goToLastCell,selectToFirstCell:t?.selectToFirstCell??t?.first??vr.selectToFirstCell,selectToLastCell:t?.selectToLastCell??t?.last??vr.selectToLastCell};return Ou({...vr,...n})},[t])}function Y0(e){function t(r,i,o){if(typeof r=="number")return{headerIndex:r,isCollapsed:!1,depth:i,path:o};const s={headerIndex:r.headerIndex,isCollapsed:r.isCollapsed,depth:i,path:o};return r.subGroups!==void 0&&(s.subGroups=r.subGroups.map((a,l)=>t(a,i+1,[...o,l])).sort((a,l)=>a.headerIndex-l.headerIndex)),s}return e.map((r,i)=>t(r,0,[i])).sort((r,i)=>r.headerIndex-i.headerIndex)}function Ks(e,t){const n=[];function r(a,l,u=!1){let c=l!==null?l-a.headerIndex:t-a.headerIndex;if(a.subGroups!==void 0&&(c=a.subGroups[0].headerIndex-a.headerIndex),c--,n.push({rowIndex:-1,headerIndex:a.headerIndex,contentIndex:-1,skip:u,isCollapsed:a.isCollapsed,depth:a.depth,path:a.path,rows:c}),a.subGroups)for(let f=0;f<a.subGroups.length;f++){const g=f<a.subGroups.length-1?a.subGroups[f+1].headerIndex:l;r(a.subGroups[f],g,u||a.isCollapsed)}}const i=Y0(e.groups);for(let a=0;a<i.length;a++){const l=a<i.length-1?i[a+1].headerIndex:null;r(i[a],l)}let o=0,s=0;for(const a of n)a.contentIndex=s,s+=a.rows,a.rowIndex=o,o+=a.isCollapsed?1:a.rows+1;return n.filter(a=>a.skip===!1).map(a=>{const{skip:l,...u}=a;return u})}function Rs(e,t){if(t===void 0||Ks.length===0)return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1};let n=e;for(const r of t){if(n===0)return{path:[...r.path,-1],originalIndex:r.headerIndex,isGroupHeader:!0,groupIndex:-1,contentIndex:-1,groupRows:r.rows};if(r.isCollapsed)n--;else{if(n<=r.rows)return{path:[...r.path,n-1],originalIndex:r.headerIndex+n,isGroupHeader:!1,groupIndex:n-1,contentIndex:r.contentIndex+n-1,groupRows:r.rows};n=n-r.rows-1}}return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1}}function X0(e,t,n,r){const i=Nt.useMemo(()=>e===void 0?void 0:Ks(e,t),[e,t]),o=Nt.useMemo(()=>i?.reduce((c,f)=>(c[f.rowIndex]=f,c),{}),[i]),s=Nt.useMemo(()=>i===void 0?t:i.reduce((c,f)=>c+(f.isCollapsed?1:f.rows+1),0),[i,t]),a=Nt.useMemo(()=>e===void 0||typeof n=="number"&&e.height===n?n:c=>o?.[c]?e.height:typeof n=="number"?n:n(c),[o,e,n]),l=Nt.useCallback(c=>{if(i===void 0)return c;let f=c;for(const g of i){if(f===0)return;if(f--,!g.isCollapsed){if(f<g.rows)return g.contentIndex+f;f-=g.rows}}return c},[i]),u=br(r??e?.themeOverride,Nt.useCallback(c=>{if(e===void 0)return r?.(c,c,c);if(r===void 0&&e?.themeOverride===void 0)return;const{isGroupHeader:f,contentIndex:g,groupIndex:h}=Rs(c,i);return f?e.themeOverride:r?.(c,h,g)},[i,r,e]));return e===void 0?{rowHeight:a,rows:t,rowNumberMapper:l,getRowThemeOverride:u}:{rowHeight:a,rows:s,rowNumberMapper:l,getRowThemeOverride:u}}function j0(e,t){const n=Nt.useMemo(()=>e===void 0?void 0:Ks(e,t),[e,t]);return{getRowGroupingForPath:_d,updateRowGroupingByPath:Pd,mapper:Nt.useCallback(r=>{if(typeof r=="number")return Rs(r,n);const i=Rs(r[1],n);return{...i,originalIndex:[r[0],i.originalIndex]}},[n])}}function Pd(e,t,n){const[r,...i]=t;return i[0]===-1?e.map((o,s)=>s===r?{...o,...n}:o):e.map((o,s)=>s===r?{...o,subGroups:Pd(o.subGroups??[],i,n)}:o)}function _d(e,t){const[n,...r]=t;return r[0]===-1?e[n]:_d(e[n].subGroups??[],r)}function K0(e,t){const[n]=d.useState(()=>({value:e,callback:t,facade:{get current(){return n.value},set current(r){const i=n.value;i!==r&&(n.value=r,n.callback(r,i))}}}));return n.callback=t,n.facade}function Z0(e,t,n,r,i){const[o,s]=d.useMemo(()=>[t!==void 0&&typeof n=="number"?Math.floor(t/n):0,t!==void 0&&typeof n=="number"?-(t%n):0],[t,n]),a=d.useMemo(()=>({x:r.current.x,y:o,width:r.current.width??1,height:r.current.height??1,ty:s}),[r,s,o]),[l,u,c]=Jg(a),f=d.useRef(i);f.current=i;const g=K0(null,p=>{p!==null&&t!==void 0?p.scrollTop=t:p!==null&&e!==void 0&&(p.scrollLeft=e)}),h=(l.height??1)>1;d.useLayoutEffect(()=>{if(t!==void 0&&g.current!==null&&h){if(g.current.scrollTop===t)return;g.current.scrollTop=t,g.current.scrollTop!==t&&c(),f.current()}},[t,h,c,g]);const m=(l.width??1)>1;return d.useLayoutEffect(()=>{if(e!==void 0&&g.current!==null&&m){if(g.current.scrollLeft===e)return;g.current.scrollLeft=e,g.current.scrollLeft!==e&&c(),f.current()}},[e,m,c,g]),{visibleRegion:l,setVisibleRegion:u,scrollRef:g}}const J0=d.lazy(async()=>await _s(()=>import("./data-grid-overlay-editor.CoquyZNK.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url));let Q0=0;function ev(e){return np(vu(vu(e).filter(t=>t.span!==void 0).map(t=>Cr((t.span?.[0]??0)+1,(t.span?.[1]??0)+1))))}function zo(e,t){return e===void 0||t===0||e.columns.length===0&&e.current===void 0?e:{current:e.current===void 0?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:{...e.current.range,x:e.current.range.x+t},rangeStack:e.current.rangeStack.map(n=>({...n,x:n.x+t}))},rows:e.rows,columns:e.columns.offset(t)}}const Vo={kind:Z.Loading,allowOverlay:!1},No={columns:rt.empty(),rows:rt.empty(),current:void 0},tv=(e,t)=>{const[n,r]=d.useState(No),[i,o]=d.useState(),s=d.useRef(null),a=d.useRef(null),[l,u]=d.useState(),c=d.useRef(),f=typeof window>"u"?null:window,{imageEditorOverride:g,getRowThemeOverride:h,markdownDivCreateNode:m,width:p,height:w,columns:b,rows:v,getCellContent:S,onCellClicked:O,onCellActivated:R,onFillPattern:M,onFinishedEditing:_,coercePasteValue:E,drawHeader:k,drawCell:F,editorBloom:D,onHeaderClicked:C,onColumnProposeMove:I,rangeSelectionColumnSpanning:T=!0,spanRangeBehavior:x="default",onGroupHeaderClicked:$,onCellContextMenu:q,className:X,onHeaderContextMenu:oe,getCellsForSelection:Q,onGroupHeaderContextMenu:J,onGroupHeaderRenamed:te,onCellEdited:ae,onCellsEdited:le,onSearchResultsChanged:fe,searchResults:re,onSearchValueChange:H,searchValue:P,onKeyDown:G,onKeyUp:ue,keybindings:he,editOnType:ke=!0,onRowAppended:Se,onColumnMoved:et,validateCell:Re,highlightRegions:Xe,rangeSelect:yt="rect",columnSelect:De="multi",rowSelect:ne="multi",rangeSelectionBlending:Ee="exclusive",columnSelectionBlending:xe="exclusive",rowSelectionBlending:ce="exclusive",onDelete:pe,onDragStart:ze,onMouseMove:Ie,onPaste:tt,copyHeaders:Te=!1,freezeColumns:Oe=0,cellActivationBehavior:Je="second-click",rowSelectionMode:ye="auto",onHeaderMenuClick:qe,onHeaderIndicatorClick:pt,getGroupDetails:ft,rowGrouping:We,onSearchClose:xt,onItemHovered:qt,onSelectionCleared:Tt,showSearch:_t,onVisibleRegionChanged:tn,gridSelection:zt,onGridSelectionChange:Sn,minColumnWidth:Vt=50,maxColumnWidth:ut=500,maxColumnAutoWidth:nn,provideEditor:Ct,trailingRowOptions:Ft,freezeTrailingRows:Yt=0,allowedFillDirections:je="orthogonal",scrollOffsetX:Dt,scrollOffsetY:Ot,verticalBorder:ln,onDragOverCell:rn,onDrop:hn,onColumnResize:bn,onColumnResizeEnd:Xt,onColumnResizeStart:Jt,customRenderers:Ge,fillHandle:Et,experimental:Qt,fixedShadowX:Lt,fixedShadowY:At,headerIcons:xn,imageWindowLoader:Un,initialSize:Hn,isDraggable:N,onDragLeave:Be,onRowMoved:Pe,overscrollX:Pt,overscrollY:wn,preventDiagonalScrolling:ge,rightElement:bt,rightElementProps:en,trapFocus:kt=!1,smoothScrollX:Bt,smoothScrollY:an,scaleToRem:Sa=!1,rowHeight:co=34,headerHeight:fo=36,groupHeaderHeight:xa=fo,theme:Mr,isOutsideClick:lr,renderers:pi,resizeIndicator:ka,scrollToActiveCell:vi=!0,drawFocusRing:bi=!0}=e,Rr=bi==="no-editor"?i===void 0:bi,Jn=typeof e.rowMarkers=="string"?void 0:e.rowMarkers,kn=Jn?.kind??e.rowMarkers??"none",wi=Jn?.width??e.rowMarkerWidth,yi=Jn?.startIndex??e.rowMarkerStartIndex??1,Gr=Jn?.theme??e.rowMarkerTheme,Qn=Jn?.headerTheme,er=Jn?.headerAlwaysVisible,Er=ne!=="multi"||Jn?.headerDisabled===!0,qn=Jn?.checkboxStyle??"square",Gn=Math.max(Vt,20),Ir=Math.max(ut,Gn),ho=Math.max(nn??Ir,Gn),Ci=d.useMemo(()=>typeof window>"u"?{fontSize:"16px"}:window.getComputedStyle(document.documentElement),[]),{rows:Ve,rowNumberMapper:go,rowHeight:mo,getRowThemeOverride:ur}=X0(We,v,co,h),po=d.useMemo(()=>Number.parseFloat(Ci.fontSize),[Ci]),{rowHeight:zn,headerHeight:Si,groupHeaderHeight:vo,theme:bo,overscrollX:Ma,overscrollY:Ra}=q0({groupHeaderHeight:xa,headerHeight:fo,overscrollX:Pt,overscrollY:wn,remSize:po,rowHeight:mo,scaleToRem:Sa,theme:Mr}),On=G0(he),cr=wi??(v>1e4?48:v>1e3?44:v>100?36:32),Pn=kn!=="none",B=Pn?1:0,Wt=Se!==void 0,dr=Ft?.sticky===!0,[Yr,Xr]=d.useState(!1),xi=_t??Yr,Ea=d.useCallback(()=>{xt!==void 0?xt():Xr(!1)},[xt]),K=d.useMemo(()=>zt===void 0?void 0:zo(zt,B),[zt,B])??n,un=d.useRef();un.current===void 0&&(un.current=new AbortController),d.useEffect(()=>()=>un?.current.abort(),[]);const[on,Ia]=T0(Q,S,B,un.current,Ve),V=d.useCallback((y,A,L)=>{if(Re===void 0)return!0;const U=[y[0]-B,y[1]];return Re?.(U,A,L)},[B,Re]),ie=d.useRef(zt),me=d.useCallback((y,A)=>{A&&(y=A0(y,on,B,x,un.current)),Sn!==void 0?(ie.current=zo(y,-B),Sn(ie.current)):r(y)},[Sn,on,B,x]),Me=br(bn,d.useCallback((y,A,L,U)=>{bn?.(b[L-B],A,L-B,U)},[bn,B,b])),de=br(Xt,d.useCallback((y,A,L,U)=>{Xt?.(b[L-B],A,L-B,U)},[Xt,B,b])),be=br(Jt,d.useCallback((y,A,L,U)=>{Jt?.(b[L-B],A,L-B,U)},[Jt,B,b])),Ye=br(k,d.useCallback((y,A)=>k?.({...y,columnIndex:y.columnIndex-B},A)??!1,[k,B])),we=br(F,d.useCallback((y,A)=>F?.({...y,col:y.col-B},A)??!1,[F,B])),Ue=d.useCallback(y=>{if(pe!==void 0){const A=pe(zo(y,-B));return typeof A=="boolean"?A:zo(A,B)}return!0},[pe,B]),[Fe,St,lt]=I0(K,me,Ee,xe,ce,yt,T),at=d.useMemo(()=>ir(pd(),bo),[bo]),[dt,Gt]=d.useState([0,0,0]),gn=d.useMemo(()=>{if(pi===void 0)return{};const y={};for(const A of pi)y[A.kind]=A;return y},[pi]),ct=d.useCallback(y=>y.kind!==Z.Custom?gn[y.kind]:Ge?.find(A=>A.isMatch(y)),[Ge,gn]);let{sizedColumns:sn,nonGrowWidth:Tr}=Ym(b,Ve,Ia,dt[0]-(B===0?0:cr)-dt[2],Gn,ho,at,ct,un.current);kn!=="none"&&(Tr+=cr);const tr=d.useMemo(()=>sn.some(y=>y.group!==void 0),[sn]),Rt=tr?Si+vo:Si,Vn=K.rows.length,Mn=kn==="none"?void 0:Vn===0?!1:Vn===Ve?!0:void 0,mt=d.useMemo(()=>kn==="none"?sn:[{title:"",width:cr,icon:void 0,hasMenu:!1,style:"normal",themeOverride:Gr,rowMarker:qn,rowMarkerChecked:Mn,headerRowMarkerTheme:Qn,headerRowMarkerAlwaysVisible:er,headerRowMarkerDisabled:Er},...sn],[kn,sn,cr,Gr,qn,Mn,Qn,er,Er]),jt=d.useRef({height:1,width:1,x:0,y:0}),fr=d.useRef(!1),{setVisibleRegion:hr,visibleRegion:Dr,scrollRef:Ut}=Z0(Dt,Ot,zn,jt,()=>fr.current=!0);jt.current=Dr;const If=Dr.x+B,wo=Dr.y,mn=d.useRef(null),yn=d.useCallback(y=>{y===!0?mn.current?.focus():window.requestAnimationFrame(()=>{mn.current?.focus()})},[]),pn=Wt?Ve+1:Ve,_n=d.useCallback(y=>{const A=B===0?y:y.map(U=>({...U,location:[U.location[0]-B,U.location[1]]})),L=le?.(A);if(L!==!0)for(const U of A)ae?.(U.location,U.value);return L},[ae,le,B]),[Or,Ta]=d.useState(),yo=K.current!==void 0&&K.current.range.width*K.current.range.height>1?K.current.range:void 0,hl=Rr?K.current?.cell:void 0,Co=hl?.[0],So=hl?.[1],Tf=d.useMemo(()=>{if((Xe===void 0||Xe.length===0)&&(yo??Co??So??Or)===void 0)return;const y=[];if(Xe!==void 0)for(const A of Xe){const L=mt.length-A.range.x-B;L>0&&y.push({color:A.color,range:{...A.range,x:A.range.x+B,width:Math.min(L,A.range.width)},style:A.style})}return Or!==void 0&&y.push({color:Nr(at.accentColor,0),range:Or,style:"dashed"}),yo!==void 0&&y.push({color:Nr(at.accentColor,.5),range:yo,style:"solid-outline"}),Co!==void 0&&So!==void 0&&y.push({color:at.accentColor,range:{x:Co,y:So,width:1,height:1},style:"solid-outline"}),y.length>0?y:void 0},[Or,yo,Co,So,Xe,mt.length,at.accentColor,B]),gl=d.useRef(mt);gl.current=mt;const Rn=d.useCallback(([y,A],L=!1)=>{const U=Wt&&A===pn-1;if(y===0&&Pn){if(U)return Vo;const j=go(A);return j===void 0?Vo:{kind:An.Marker,allowOverlay:!1,checkboxStyle:qn,checked:K?.rows.hasIndex(A)===!0,markerKind:kn==="clickable-number"?"number":kn,row:yi+j,drawHandle:Pe!==void 0,cursor:kn==="clickable-number"?"pointer":void 0}}else if(U){const z=y===B?Ft?.hint??"":"",W=gl.current[y];if(W?.trailingRowOptions?.disabled===!0)return Vo;{const ee=W?.trailingRowOptions?.hint??z,se=W?.trailingRowOptions?.addIcon??Ft?.addIcon;return{kind:An.NewRow,hint:ee,allowOverlay:!1,icon:se}}}else{const j=y-B;if(L||Qt?.strict===!0){const W=jt.current,ee=W.x>j||j>W.x+W.width||W.y>A||A>W.y+W.height||A>=Oa.current,se=j===W.extras?.selected?.[0]&&A===W.extras?.selected[1];let ve=!1;if(W.extras?.freezeRegions!==void 0){for(const wt of W.extras.freezeRegions)if(Vr(wt,j,A)){ve=!0;break}}if(ee&&!se&&!ve)return Vo}let z=S([j,A]);return B!==0&&z.span!==void 0&&(z={...z,span:[z.span[0]+B,z.span[1]+B]}),z}},[Wt,pn,Pn,go,qn,K?.rows,kn,yi,Pe,B,Ft?.hint,Ft?.addIcon,Qt?.strict,S]),Da=d.useCallback(y=>{let A=ft?.(y)??{name:y};return te!==void 0&&y!==""&&(A={icon:A.icon,name:A.name,overrideTheme:A.overrideTheme,actions:[...A.actions??[],{title:"Rename",icon:"renameIcon",onClick:L=>_a({group:A.name,bounds:L.bounds})}]}),A},[ft,te]),xo=d.useCallback(y=>{const[A,L]=y.cell,U=mt[A],Y=U?.group!==void 0?Da(U.group)?.overrideTheme:void 0,j=U?.themeOverride,z=ur?.(L);o({...y,theme:ir(at,Y,j,z,y.content.themeOverride)})},[ur,mt,Da,at]),jr=d.useCallback((y,A,L)=>{if(K.current===void 0)return;const[U,Y]=K.current.cell,j=Rn([U,Y]);if(j.kind!==Z.Boolean&&j.allowOverlay){let z=j;if(L!==void 0)switch(z.kind){case Z.Number:{const W=Rg(()=>L==="-"?-0:Number.parseFloat(L),0);z={...z,data:Number.isNaN(W)?0:W};break}case Z.Text:case Z.Markdown:case Z.Uri:z={...z,data:L};break}xo({target:y,content:z,initialValue:L,cell:[U,Y],highlight:L===void 0,forceEditMode:L!==void 0})}else j.kind===Z.Boolean&&A&&j.readonly!==!0&&(_n([{location:K.current.cell,value:{...j,data:Od(j.data)}}]),mn.current?.damage([{cell:K.current.cell}]))},[Rn,K,_n,xo]),ml=d.useCallback((y,A)=>{const L=mn.current?.getBounds(y,A);if(L===void 0||Ut.current===null)return;const U=Rn([y,A]);U.allowOverlay&&xo({target:L,content:U,initialValue:void 0,highlight:!0,cell:[y,A],forceEditMode:!0})},[Rn,Ut,xo]),Zt=d.useCallback((y,A,L="both",U=0,Y=0,j=void 0)=>{if(Ut.current!==null){const z=mn.current,W=a.current,ee=typeof y!="number"?y.unit==="cell"?y.amount:void 0:y,se=typeof A!="number"?A.unit==="cell"?A.amount:void 0:A,ve=typeof y!="number"&&y.unit==="px"?y.amount:void 0,wt=typeof A!="number"&&A.unit==="px"?A.amount:void 0;if(z!==null&&W!==null){let nt={x:0,y:0,width:0,height:0},Ke=0,Ze=0;if((ee!==void 0||se!==void 0)&&(nt=z.getBounds((ee??0)+B,se??0)??nt,nt.width===0||nt.height===0))return;const ht=W.getBoundingClientRect(),Mt=ht.width/W.offsetWidth;if(ve!==void 0&&(nt={...nt,x:ve-ht.left-Ut.current.scrollLeft,width:1}),wt!==void 0&&(nt={...nt,y:wt+ht.top-Ut.current.scrollTop,height:1}),nt!==void 0){const vt={x:nt.x-U,y:nt.y-Y,width:nt.width+2*U,height:nt.height+2*Y};let ti=0;for(let Na=0;Na<Oe;Na++)ti+=sn[Na].width;let Oi=0;const Pi=Yt+(dr?1:0);Pi>0&&(Oi=Ur(pn,Pi,zn));let Xn=ti*Mt+ht.left+B*cr*Mt,ni=ht.right,Lr=ht.top+Rt*Mt,ri=ht.bottom-Oi*Mt;const Do=nt.width+U*2;switch(j?.hAlign){case"start":ni=Xn+Do;break;case"end":Xn=ni-Do;break;case"center":Xn=Math.floor((Xn+ni)/2)-Do/2,ni=Xn+Do;break}const Oo=nt.height+Y*2;switch(j?.vAlign){case"start":ri=Lr+Oo;break;case"end":Lr=ri-Oo;break;case"center":Lr=Math.floor((Lr+ri)/2)-Oo/2,ri=Lr+Oo;break}Xn>vt.x?Ke=vt.x-Xn:ni<vt.x+vt.width&&(Ke=vt.x+vt.width-ni),Lr>vt.y?Ze=vt.y-Lr:ri<vt.y+vt.height&&(Ze=vt.y+vt.height-ri),L==="vertical"||typeof y=="number"&&y<Oe?Ke=0:(L==="horizontal"||typeof A=="number"&&A>=pn-Pi)&&(Ze=0),(Ke!==0||Ze!==0)&&(Mt!==1&&(Ke/=Mt,Ze/=Mt),Ut.current.scrollTo({left:Ke+Ut.current.scrollLeft,top:Ze+Ut.current.scrollTop,behavior:j?.behavior??"auto"}))}}}},[B,Yt,cr,Ut,Rt,Oe,sn,pn,dr,zn]),pl=d.useRef(ml),vl=d.useRef(S),Oa=d.useRef(Ve);pl.current=ml,vl.current=S,Oa.current=Ve;const Kr=d.useCallback(async(y,A=!0,L)=>{if(mt[y]?.trailingRowOptions?.disabled===!0)return;const Y=Se?.();let j,z=!0;Y!==void 0&&(j=await Y,j==="top"&&(z=!1),typeof j=="number"&&(z=!1));let W=0;const ee=()=>{if(Oa.current<=Ve){W<500&&window.setTimeout(ee,W),W=50+W*2;return}const se=typeof j=="number"?j:z?Ve:0;To.current(y-B,se,"both",0,0,L?{behavior:L}:void 0),Fe({cell:[y,se],range:{x:y,y:se,width:1,height:1}},!1,!1,"edit");const ve=vl.current([y-B,se]);ve.allowOverlay&&Yi(ve)&&ve.readonly!==!0&&A&&window.setTimeout(()=>{pl.current(y,se)},0)};ee()},[mt,Se,B,Ve,Fe]),ko=d.useCallback(y=>{const A=sn[y]?.trailingRowOptions?.targetColumn??Ft?.targetColumn;if(typeof A=="number")return A+(Pn?1:0);if(typeof A=="object"){const L=b.indexOf(A);if(L>=0)return L+(Pn?1:0)}},[sn,b,Pn,Ft?.targetColumn]),gr=d.useRef(),Zr=d.useRef(),Mi=d.useCallback((y,A)=>{const[L,U]=A;return ir(at,mt[L]?.themeOverride,ur?.(U),y.themeOverride)},[ur,mt,at]),{mapper:Pr}=j0(We,v),Nn=We?.navigationBehavior,Ri=d.useCallback(y=>{const A=oa.value?y.metaKey:y.ctrlKey,L=A&&ne==="multi",U=A&&De==="multi",[Y,j]=y.location,z=K.columns,W=K.rows,[ee,se]=K.current?.cell??[];if(y.kind==="cell"){if(Zr.current=void 0,_r.current=[Y,j],Y===0&&Pn){if(Wt===!0&&j===Ve||kn==="number"||ne==="none")return;const ve=Rn(y.location);if(ve.kind!==An.Marker)return;if(Pe!==void 0){const Ke=ct(ve);Tn(Ke?.kind===An.Marker);const Ze=Ke?.onClick?.({...y,cell:ve,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,theme:Mi(ve,y.location),preventDefault:()=>{}});if(Ze===void 0||Ze.checked===ve.checked)return}o(void 0),yn();const wt=W.hasIndex(j),nt=gr.current;if(ne==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&nt!==void 0&&W.hasIndex(nt)){const Ke=[Math.min(nt,j),Math.max(nt,j)+1];L||ye==="multi"?St(void 0,Ke,!0):St(rt.fromSingleSelection(Ke),void 0,L)}else ne==="multi"&&(L||y.isTouch||ye==="multi")?wt?St(W.remove(j),void 0,!0):(St(void 0,j,!0),gr.current=j):wt&&W.length===1?St(rt.empty(),void 0,A):(St(rt.fromSingleSelection(j),void 0,A),gr.current=j)}else if(Y>=B&&Wt&&j===Ve){const ve=ko(Y);Kr(ve??Y)}else if(ee!==Y||se!==j){const ve=Rn(y.location),wt=ct(ve);if(wt?.onSelect!==void 0){let Ze=!1;if(wt.onSelect({...y,cell:ve,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,preventDefault:()=>Ze=!0,theme:Mi(ve,y.location)}),Ze)return}if(Nn==="block"&&Pr(j).isGroupHeader)return;const nt=dr&&j===Ve,Ke=dr&&K!==void 0&&K.current?.cell[1]===Ve;if((y.shiftKey||y.isLongTouch===!0)&&ee!==void 0&&se!==void 0&&K.current!==void 0&&!Ke){if(nt)return;const Ze=Math.min(Y,ee),ht=Math.max(Y,ee),Mt=Math.min(j,se),vt=Math.max(j,se);Fe({...K.current,range:{x:Ze,y:Mt,width:ht-Ze+1,height:vt-Mt+1}},!0,A,"click"),gr.current=void 0,yn()}else Fe({cell:[Y,j],range:{x:Y,y:j,width:1,height:1}},!0,A,"click"),gr.current=void 0,o(void 0),yn()}}else if(y.kind==="header")if(_r.current=[Y,j],o(void 0),Pn&&Y===0)gr.current=void 0,Zr.current=void 0,!Er&&ne==="multi"&&(W.length!==Ve?St(rt.fromSingleSelection([0,Ve]),void 0,A):St(rt.empty(),void 0,A),yn());else{const ve=Zr.current;if(De==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&ve!==void 0&&z.hasIndex(ve)){const wt=[Math.min(ve,Y),Math.max(ve,Y)+1];U?lt(void 0,wt,A):lt(rt.fromSingleSelection(wt),void 0,A)}else U?(z.hasIndex(Y)?lt(z.remove(Y),void 0,A):lt(void 0,Y,A),Zr.current=Y):De!=="none"&&(z.hasIndex(Y)?lt(z.remove(Y),void 0,A):lt(rt.fromSingleSelection(Y),void 0,A),Zr.current=Y);gr.current=void 0,yn()}else y.kind===Ln?_r.current=[Y,j]:y.kind===aa&&!y.isMaybeScrollbar&&(me(No,!1),o(void 0),yn(),Tt?.(),gr.current=void 0,Zr.current=void 0)},[ne,De,K,Pn,B,Wt,Ve,kn,Rn,Pe,yn,ye,ct,Mi,St,ko,Kr,Nn,Pr,dr,Fe,lt,me,Tt]),Ei=d.useRef(!1),_r=d.useRef(),bl=d.useRef(Dr),Yn=d.useRef(),Df=d.useCallback(y=>{if(Jr.current=!1,bl.current=jt.current,y.button!==0&&y.button!==1){Yn.current=void 0;return}const A=performance.now();Yn.current={button:y.button,time:A,location:y.location},y?.kind==="header"&&(Ei.current=!0);const L=y.kind==="cell"&&y.isFillHandle;!L&&y.kind!=="cell"&&y.isEdge||(u({previousSelection:K,fillHandle:L}),_r.current=void 0,!y.isTouch&&y.button===0&&!L?Ri(y):!y.isTouch&&y.button===1&&(_r.current=y.location))},[K,Ri]),[Pa,_a]=d.useState(),wl=d.useCallback(y=>{if(y.kind!==Ln||De!=="multi")return;const A=oa.value?y.metaKey:y.ctrlKey,[L]=y.location,U=K.columns;if(L<B)return;const Y=mt[L];let j=L,z=L;for(let W=L-1;W>=B&&to(Y.group,mt[W].group);W--)j--;for(let W=L+1;W<mt.length&&to(Y.group,mt[W].group);W++)z++;if(yn(),A)if(U.hasAll([j,z+1])){let W=U;for(let ee=j;ee<=z;ee++)W=W.remove(ee);lt(W,void 0,A)}else lt(void 0,[j,z+1],A);else lt(rt.fromSingleSelection([j,z+1]),void 0,A)},[De,yn,K.columns,mt,B,lt]),Jr=d.useRef(!1),Mo=d.useCallback(async y=>{if(on!==void 0&&Me!==void 0){const A=jt.current.y,L=jt.current.height;let U=on({x:y,y:A,width:1,height:Math.min(L,Ve-A)},un.current.signal);typeof U!="object"&&(U=await U());const Y=sn[y-B],z=document.createElement("canvas").getContext("2d",{alpha:!1});if(z!==null){z.font=at.baseFontFull;const W=bd(z,at,Y,0,U,Gn,Ir,!1,ct);Me?.(Y,W.width,y,W.width)}}},[sn,on,Ir,at,Gn,Me,B,Ve,ct]),[Of,Fa]=d.useState(),Qr=d.useCallback(async(y,A)=>{const L=y.current?.range;if(L===void 0||on===void 0||A.current===void 0)return;const U=A.current.range;if(M!==void 0){let W=!1;if(M({fillDestination:{...U,x:U.x-B},patternSource:{...L,x:L.x-B},preventDefault:()=>W=!0}),W)return}let Y=on(L,un.current.signal);typeof Y!="object"&&(Y=await Y());const j=Y,z=[];for(let W=0;W<U.width;W++)for(let ee=0;ee<U.height;ee++){const se=[U.x+W,U.y+ee];if(sd(se,L))continue;const ve=j[ee%L.height][W%L.width];ui(ve)||!Yi(ve)||z.push({location:se,value:{...ve}})}_n(z),mn.current?.damage(z.map(W=>({cell:W.location})))},[on,_n,M,B]),yl=d.useCallback(()=>{if(K.current===void 0||K.current.range.width<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,width:1}}};Qr(y,K)},[Qr,K]),Cl=d.useCallback(()=>{if(K.current===void 0||K.current.range.height<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,height:1}}};Qr(y,K)},[Qr,K]),Pf=d.useCallback((y,A)=>{const L=l;if(u(void 0),Ta(void 0),Fa(void 0),Ei.current=!1,A)return;if(L?.fillHandle===!0&&K.current!==void 0&&L.previousSelection?.current!==void 0){if(Or===void 0)return;const ve={...K,current:{...K.current,range:xd(L.previousSelection.current.range,Or)}};Qr(L.previousSelection,ve),me(ve,!0);return}const[U,Y]=y.location,[j,z]=_r.current??[],W=()=>{Jr.current=!0},ee=ve=>{const wt=ve.isTouch||j===U&&z===Y;if(wt&&O?.([U-B,Y],{...ve,preventDefault:W}),ve.button===1)return!Jr.current;if(!Jr.current){const nt=Rn(y.location),Ke=ct(nt);if(Ke!==void 0&&Ke.onClick!==void 0&&wt){const ht=Ke.onClick({...ve,cell:nt,posX:ve.localEventX,posY:ve.localEventY,bounds:ve.bounds,theme:Mi(nt,y.location),preventDefault:W});ht!==void 0&&!ui(ht)&&si(ht)&&(_n([{location:ve.location,value:ht}]),mn.current?.damage([{cell:ve.location}]))}if(Jr.current||K.current===void 0)return!1;let Ze=!1;switch(nt.activationBehaviorOverride??Je){case"double-click":case"second-click":{if(L?.previousSelection?.current?.cell===void 0)break;const[ht,Mt]=K.current.cell,[vt,ti]=L.previousSelection.current.cell;Ze=U===ht&&U===vt&&Y===Mt&&Y===ti&&(ve.isDoubleClick===!0||Je==="second-click");break}case"single-click":{Ze=!0;break}}if(Ze)return R?.([U-B,Y]),jr(ve.bounds,!1),!0}return!1},se=y.location[0]-B;if(y.isTouch){const ve=jt.current,wt=bl.current;if(ve.x!==wt.x||ve.y!==wt.y)return;if(y.isLongTouch===!0){if(y.kind==="cell"&&Ki(K.current?.cell,y.location)){q?.([se,y.location[1]],{...y,preventDefault:W});return}else if(y.kind==="header"&&K.columns.hasIndex(U)){oe?.(se,{...y,preventDefault:W});return}else if(y.kind===Ln){if(se<0)return;J?.(se,{...y,preventDefault:W});return}}y.kind==="cell"?ee(y)||Ri(y):y.kind===Ln?$?.(se,{...y,preventDefault:W}):(y.kind===wr&&C?.(se,{...y,preventDefault:W}),Ri(y));return}if(y.kind==="header"){if(se<0)return;y.isEdge?y.isDoubleClick===!0&&Mo(U):y.button===0&&U===j&&Y===z&&C?.(se,{...y,preventDefault:W})}if(y.kind===Ln){if(se<0)return;y.button===0&&U===j&&Y===z&&($?.(se,{...y,preventDefault:W}),Jr.current||wl(y))}y.kind==="cell"&&(y.button===0||y.button===1)&&ee(y),_r.current=void 0},[l,K,B,Or,Qr,me,O,Rn,ct,Je,Mi,_n,R,jr,q,oe,J,Ri,$,C,Mo,wl]),_f=d.useCallback(y=>{const A={...y,location:[y.location[0]-B,y.location[1]]};Ie?.(A),l!==void 0&&y.buttons===0&&(u(void 0),Ta(void 0),Fa(void 0),Ei.current=!1),Fa(L=>Ei.current?[y.scrollEdge[0],0]:y.scrollEdge[0]===L?.[0]&&y.scrollEdge[1]===L[1]?L:l===void 0||(Yn.current?.location[0]??0)<B?void 0:y.scrollEdge)},[l,Ie,B]),Ff=d.useCallback((y,A)=>{qe?.(y-B,A)},[qe,B]),Lf=d.useCallback((y,A)=>{pt?.(y-B,A)},[pt,B]),ei=K?.current?.cell,Af=d.useCallback((y,A,L,U,Y,j)=>{fr.current=!1;let z=ei;z!==void 0&&(z=[z[0]-B,z[1]]);const W=Oe===0?void 0:{x:0,y:y.y,width:Oe,height:y.height},ee=[];W!==void 0&&ee.push(W),Yt>0&&(ee.push({x:y.x-B,y:Ve-Yt,width:y.width,height:Yt}),Oe>0&&ee.push({x:0,y:Ve-Yt,width:Oe,height:Yt}));const se={x:y.x-B,y:y.y,width:y.width,height:Wt&&y.y+y.height>=Ve?y.height-1:y.height,tx:Y,ty:j,extras:{selected:z,freezeRegion:W,freezeRegions:ee}};jt.current=se,hr(se),Gt([A,L,U]),tn?.(se,se.tx,se.ty,se.extras)},[ei,B,Wt,Ve,Oe,Yt,hr,tn]),Hf=br(I,d.useCallback((y,A)=>I?.(y-B,A-B)!==!1,[I,B])),zf=br(et,d.useCallback((y,A)=>{et?.(y-B,A-B),De!=="none"&&lt(rt.fromSingleSelection(A),void 0,!0)},[De,et,B,lt])),La=d.useRef(!1),Vf=d.useCallback(y=>{if(y.location[0]===0&&B>0){y.preventDefault();return}ze?.({...y,location:[y.location[0]-B,y.location[1]]}),y.defaultPrevented()||(La.current=!0),u(void 0)},[ze,B]),Nf=d.useCallback(()=>{La.current=!1},[]),Sl=We?.selectionBehavior,Ro=d.useCallback(y=>{if(Sl!=="block-spanning")return;const{isGroupHeader:A,path:L,groupRows:U}=Pr(y);if(A)return[y,y];const Y=L[L.length-1],j=y-Y,z=y+U-Y-1;return[j,z]},[Pr,Sl]),Aa=d.useRef(),Ha=d.useCallback(y=>{if(!Td(y,Aa.current)&&(Aa.current=y,!(Yn?.current?.button!==void 0&&Yn.current.button>=1))){if(y.buttons!==0&&l!==void 0&&Yn.current?.location[0]===0&&y.location[0]===0&&B===1&&ne==="multi"&&l.previousSelection&&!l.previousSelection.rows.hasIndex(Yn.current.location[1])&&K.rows.hasIndex(Yn.current.location[1])){const A=Math.min(Yn.current.location[1],y.location[1]),L=Math.max(Yn.current.location[1],y.location[1])+1;St(rt.fromSingleSelection([A,L]),void 0,!1)}if(y.buttons!==0&&l!==void 0&&K.current!==void 0&&!La.current&&!Ei.current&&(yt==="rect"||yt==="multi-rect")){const[A,L]=K.current.cell;let[U,Y]=y.location;if(Y<0&&(Y=jt.current.y),l.fillHandle===!0&&l.previousSelection?.current!==void 0){const j=l.previousSelection.current.range;Y=Math.min(Y,Wt?Ve-1:Ve);const z=Lp(j,U,Y,je);Ta(z)}else{if(Wt&&L===Ve)return;if(Wt&&Y===Ve)if(y.kind===aa)Y--;else return;U=Math.max(U,B);const W=Ro(L);Y=W===void 0?Y:Fn(Y,W[0],W[1]);const ee=U-A,se=Y-L,ve={x:ee>=0?A:U,y:se>=0?L:Y,width:Math.abs(ee)+1,height:Math.abs(se)+1};Fe({...K.current,range:ve},!0,!1,"drag")}}qt?.({...y,location:[y.location[0]-B,y.location[1]]})}},[l,B,ne,K,yt,qt,St,Wt,Ve,je,Ro,Fe]),$f=d.useCallback(()=>{const y=Aa.current;if(y===void 0)return;const[A,L]=y.scrollEdge;let[U,Y]=y.location;const j=jt.current;A===-1?U=j.extras?.freezeRegion?.x??j.x:A===1&&(U=j.x+j.width),L===-1?Y=Math.max(0,j.y):L===1&&(Y=Math.min(Ve-1,j.y+j.height)),U=Fn(U,0,mt.length-1),Y=Fn(Y,0,Ve-1),Ha({...y,location:[U,Y]})},[mt.length,Ha,Ve]);U0(Of,Ut,$f);const $n=d.useCallback(y=>{if(K.current===void 0)return;const[A,L]=y,[U,Y]=K.current.cell,j=K.current.range;let z=j.x,W=j.x+j.width,ee=j.y,se=j.y+j.height;const[ve,wt]=Ro(Y)??[0,Ve-1],nt=wt+1;if(L!==0)switch(L){case 2:{se=nt,ee=Y,Zt(0,se,"vertical");break}case-2:{ee=ve,se=Y+1,Zt(0,ee,"vertical");break}case 1:{ee<Y?(ee++,Zt(0,ee,"vertical")):(se=Math.min(nt,se+1),Zt(0,se,"vertical"));break}case-1:{se>Y+1?(se--,Zt(0,se,"vertical")):(ee=Math.max(ve,ee-1),Zt(0,ee,"vertical"));break}default:eo()}if(A!==0)if(A===2)W=mt.length,z=U,Zt(W-1-B,0,"horizontal");else if(A===-2)z=B,W=U+1,Zt(z-B,0,"horizontal");else{let Ke=[];if(on!==void 0){const Ze=on({x:z,y:ee,width:W-z-B,height:se-ee},un.current.signal);typeof Ze=="object"&&(Ke=ev(Ze))}if(A===1){let Ze=!1;if(z<U){if(Ke.length>0){const ht=Cr(z+1,U+1).find(Mt=>!Ke.includes(Mt-B));ht!==void 0&&(z=ht,Ze=!0)}else z++,Ze=!0;Ze&&Zt(z,0,"horizontal")}Ze||(W=Math.min(mt.length,W+1),Zt(W-1-B,0,"horizontal"))}else if(A===-1){let Ze=!1;if(W>U+1){if(Ke.length>0){const ht=Cr(W-1,U,-1).find(Mt=>!Ke.includes(Mt-B));ht!==void 0&&(W=ht,Ze=!0)}else W--,Ze=!0;Ze&&Zt(W-B,0,"horizontal")}Ze||(z=Math.max(B,z-1),Zt(z-B,0,"horizontal"))}else eo()}Fe({cell:K.current.cell,range:{x:z,y:ee,width:W-z,height:se-ee}},!0,!1,"keyboard-select")},[on,Ro,K,mt.length,B,Ve,Zt,Fe]),za=d.useRef(vi);za.current=vi;const mr=d.useCallback((y,A,L,U)=>{const Y=pn-(L?0:1);y=Fn(y,B,sn.length-1+B),A=Fn(A,0,Y);const j=ei?.[0],z=ei?.[1];if(y===j&&A===z)return!1;if(U&&K.current!==void 0){const W=[...K.current.rangeStack];(K.current.range.width>1||K.current.range.height>1)&&W.push(K.current.range),me({...K,current:{cell:[y,A],range:{x:y,y:A,width:1,height:1},rangeStack:W}},!0)}else Fe({cell:[y,A],range:{x:y,y:A,width:1,height:1}},!0,!1,"keyboard-nav");return c.current!==void 0&&c.current[0]===y&&c.current[1]===A&&(c.current=void 0),za.current&&Zt(y-B,A),!0},[pn,B,sn.length,ei,K,Zt,me,Fe]),Bf=d.useCallback((y,A)=>{i?.cell!==void 0&&y!==void 0&&si(y)&&(_n([{location:i.cell,value:y}]),window.requestAnimationFrame(()=>{mn.current?.damage([{cell:i.cell}])})),yn(!0),o(void 0);const[L,U]=A;if(K.current!==void 0&&(L!==0||U!==0)){const Y=K.current.cell[1]===pn-1&&y!==void 0;mr(Fn(K.current.cell[0]+L,0,mt.length-1),Fn(K.current.cell[1]+U,0,pn-1),Y,!1)}_?.(y,A)},[i?.cell,yn,K,_,_n,pn,mr,mt.length]),Wf=d.useMemo(()=>`gdg-overlay-${Q0++}`,[]),Fr=d.useCallback(y=>{yn();const A=[];for(let L=y.x;L<y.x+y.width;L++)for(let U=y.y;U<y.y+y.height;U++){const Y=S([L-B,U]);if(!Y.allowOverlay&&Y.kind!==Z.Boolean)continue;let j;if(Y.kind===Z.Custom){const z=ct(Y),W=z?.provideEditor?.({...Y,location:[L-B,U]});z?.onDelete!==void 0?j=z.onDelete(Y):Eg(W)&&(j=W?.deletedValue?.(Y))}else(si(Y)&&Y.allowOverlay||Y.kind===Z.Boolean)&&(j=ct(Y)?.onDelete?.(Y));j!==void 0&&!ui(j)&&si(j)&&A.push({location:[L,U],value:j})}_n(A),mn.current?.damage(A.map(L=>({cell:L.location})))},[yn,S,ct,_n,B]),Ii=i!==void 0,xl=d.useCallback(y=>{const A=()=>{y.stopPropagation(),y.preventDefault()},L={didMatch:!1},{bounds:U}=y,Y=K.columns,j=K.rows,z=On;if(!Ii&&it(z.clear,y,L))me(No,!1),Tt?.();else if(!Ii&&it(z.selectAll,y,L))me({columns:rt.empty(),rows:rt.empty(),current:{cell:K.current?.cell??[B,0],range:{x:B,y:0,width:b.length,height:Ve},rangeStack:[]}},!1);else if(it(z.search,y,L))s?.current?.focus({preventScroll:!0}),Xr(!0);else if(it(z.delete,y,L)){const ht=Ue?.(K)??!0;if(ht!==!1){const Mt=ht===!0?K:ht;if(Mt.current!==void 0){Fr(Mt.current.range);for(const vt of Mt.current.rangeStack)Fr(vt)}for(const vt of Mt.rows)Fr({x:B,y:vt,width:b.length,height:1});for(const vt of Mt.columns)Fr({x:vt,y:0,width:1,height:Ve})}}if(L.didMatch)return A(),!0;if(K.current===void 0)return!1;let[W,ee]=K.current.cell;const[,se]=K.current.cell;let ve=!1,wt=!1;if(it(z.scrollToSelectedCell,y,L)?To.current(W-B,ee):De!=="none"&&it(z.selectColumn,y,L)?Y.hasIndex(W)?lt(Y.remove(W),void 0,!0):De==="single"?lt(rt.fromSingleSelection(W),void 0,!0):lt(void 0,W,!0):ne!=="none"&&it(z.selectRow,y,L)?j.hasIndex(ee)?St(j.remove(ee),void 0,!0):ne==="single"?St(rt.fromSingleSelection(ee),void 0,!0):St(void 0,ee,!0):!Ii&&U!==void 0&&it(z.activateCell,y,L)?ee===Ve&&Wt?window.setTimeout(()=>{const ht=ko(W);Kr(ht??W)},0):(R?.([W-B,ee]),jr(U,!0)):K.current.range.height>1&&it(z.downFill,y,L)?Cl():K.current.range.width>1&&it(z.rightFill,y,L)?yl():it(z.goToNextPage,y,L)?ee+=Math.max(1,jt.current.height-4):it(z.goToPreviousPage,y,L)?ee-=Math.max(1,jt.current.height-4):it(z.goToFirstCell,y,L)?(o(void 0),ee=0,W=0):it(z.goToLastCell,y,L)?(o(void 0),ee=Number.MAX_SAFE_INTEGER,W=Number.MAX_SAFE_INTEGER):it(z.selectToFirstCell,y,L)?(o(void 0),$n([-2,-2])):it(z.selectToLastCell,y,L)?(o(void 0),$n([2,2])):Ii?(it(z.closeOverlay,y,L)&&o(void 0),it(z.acceptOverlayDown,y,L)&&(o(void 0),ee++),it(z.acceptOverlayUp,y,L)&&(o(void 0),ee--),it(z.acceptOverlayLeft,y,L)&&(o(void 0),W--),it(z.acceptOverlayRight,y,L)&&(o(void 0),W++)):(it(z.goDownCell,y,L)?ee+=1:it(z.goUpCell,y,L)?ee-=1:it(z.goRightCell,y,L)?W+=1:it(z.goLeftCell,y,L)?W-=1:it(z.goDownCellRetainSelection,y,L)?(ee+=1,ve=!0):it(z.goUpCellRetainSelection,y,L)?(ee-=1,ve=!0):it(z.goRightCellRetainSelection,y,L)?(W+=1,ve=!0):it(z.goLeftCellRetainSelection,y,L)?(W-=1,ve=!0):it(z.goToLastRow,y,L)?ee=Ve-1:it(z.goToFirstRow,y,L)?ee=Number.MIN_SAFE_INTEGER:it(z.goToLastColumn,y,L)?W=Number.MAX_SAFE_INTEGER:it(z.goToFirstColumn,y,L)?W=Number.MIN_SAFE_INTEGER:(yt==="rect"||yt==="multi-rect")&&(it(z.selectGrowDown,y,L)?$n([0,1]):it(z.selectGrowUp,y,L)?$n([0,-1]):it(z.selectGrowRight,y,L)?$n([1,0]):it(z.selectGrowLeft,y,L)?$n([-1,0]):it(z.selectToLastRow,y,L)?$n([0,2]):it(z.selectToFirstRow,y,L)?$n([0,-2]):it(z.selectToLastColumn,y,L)?$n([2,0]):it(z.selectToFirstColumn,y,L)&&$n([-2,0])),wt=L.didMatch),Nn!==void 0&&Nn!=="normal"&&ee!==se){const ht=Nn==="skip-up"||Nn==="skip"||Nn==="block",Mt=Nn==="skip-down"||Nn==="skip"||Nn==="block",vt=ee<se;if(vt&&ht){for(;ee>=0&&Pr(ee).isGroupHeader;)ee--;ee<0&&(ee=se)}else if(!vt&&Mt){for(;ee<Ve&&Pr(ee).isGroupHeader;)ee++;ee>=Ve&&(ee=se)}}const Ke=mr(W,ee,!1,ve),Ze=L.didMatch;return Ze&&(Ke||!wt||kt)&&A(),Ze},[Nn,Ii,K,On,De,ne,yt,B,Pr,Ve,mr,me,Tt,b.length,Ue,kt,Fr,lt,St,Wt,ko,Kr,R,jr,Cl,yl,$n]),Ti=d.useCallback(y=>{let A=!1;if(G!==void 0&&G({...y,...y.location&&{location:[y.location[0]-B,y.location[1]]},cancel:()=>{A=!0}}),A||xl(y)||K.current===void 0)return;const[L,U]=K.current.cell,Y=jt.current;if(ke&&!y.metaKey&&!y.ctrlKey&&K.current!==void 0&&y.key.length===1&&/[\p{L}\p{M}\p{N}\p{S}\p{P}]/u.test(y.key)&&y.bounds!==void 0&&Yi(S([L-B,Math.max(0,Math.min(U,Ve-1))]))){if((!Wt||U!==Ve)&&(Y.y>U||U>Y.y+Y.height||Y.x>L||L>Y.x+Y.width))return;R?.([L-B,U]),jr(y.bounds,!0,y.key),y.stopPropagation(),y.preventDefault()}},[ke,G,xl,K,S,B,Ve,Wt,R,jr]),Uf=d.useCallback((y,A)=>{const L=y.location[0]-B;if(y.kind==="header"&&oe?.(L,{...y,preventDefault:A}),y.kind===Ln){if(L<0)return;J?.(L,{...y,preventDefault:A})}if(y.kind==="cell"){const[U,Y]=y.location;q?.([L,Y],{...y,preventDefault:A}),Dm(K,y.location)||mr(U,Y,!1,!1)}},[K,q,J,oe,B,mr]),Va=d.useCallback(async y=>{if(!On.paste)return;function A(z,W,ee,se){const ve=typeof ee=="object"?ee?.join(`
`)??"":ee?.toString()??"";if(!ui(z)&&Yi(z)&&z.readonly!==!0){const wt=E?.(ve,z);if(wt!==void 0&&si(wt))return{location:W,value:wt};const nt=ct(z);if(nt===void 0)return;if(nt.kind===Z.Custom){Tn(z.kind===Z.Custom);const Ke=nt.onPaste?.(ve,z.data);return Ke===void 0?void 0:{location:W,value:{...z,data:Ke}}}else{const Ke=nt.onPaste?.(ve,z,{formatted:se,formattedString:typeof se=="string"?se:se?.join(`
`),rawValue:ee});return Ke===void 0?void 0:(Tn(Ke.kind===z.kind),{location:W,value:Ke})}}}const L=K.columns,U=K.rows,Y=Ut.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0;let j;if(K.current!==void 0?j=[K.current.range.x,K.current.range.y]:L.length===1?j=[L.first()??0,0]:U.length===1&&(j=[B,U.first()??0]),Y&&j!==void 0){let z,W;const ee="text/plain",se="text/html";if(navigator.clipboard.read!==void 0){const Ke=await navigator.clipboard.read();for(const Ze of Ke){if(Ze.types.includes(se)){const Mt=await(await Ze.getType(se)).text(),vt=Eu(Mt);if(vt!==void 0){z=vt;break}}Ze.types.includes(ee)&&(W=await(await Ze.getType(ee)).text())}}else if(navigator.clipboard.readText!==void 0)W=await navigator.clipboard.readText();else if(y!==void 0&&y?.clipboardData!==null){if(y.clipboardData.types.includes(se)){const Ke=y.clipboardData.getData(se);z=Eu(Ke)}z===void 0&&y.clipboardData.types.includes(ee)&&(W=y.clipboardData.getData(ee))}else return;const[ve,wt]=j,nt=[];do{if(tt===void 0){const Ke=Rn(j),Ze=W??z?.map(Mt=>Mt.map(vt=>vt.rawValue).join("	")).join("	")??"",ht=A(Ke,j,Ze,void 0);ht!==void 0&&nt.push(ht);break}if(z===void 0){if(W===void 0)return;z=H0(W)}if(tt===!1||typeof tt=="function"&&tt?.([j[0]-B,j[1]],z.map(Ke=>Ke.map(Ze=>Ze.rawValue?.toString()??"")))!==!0)return;for(const[Ke,Ze]of z.entries()){if(Ke+wt>=Ve)break;for(const[ht,Mt]of Ze.entries()){const vt=[ht+ve,Ke+wt],[ti,Oi]=vt;if(ti>=mt.length||Oi>=pn)continue;const Pi=Rn(vt),Xn=A(Pi,vt,Mt.rawValue,Mt.formatted);Xn!==void 0&&nt.push(Xn)}}}while(!1);_n(nt),mn.current?.damage(nt.map(Ke=>({cell:Ke.location})))}},[E,ct,Rn,K,On.paste,Ut,mt.length,_n,pn,tt,B,Ve]);dn("paste",Va,f,!1,!0);const Di=d.useCallback(async(y,A)=>{if(!On.copy)return;const L=A===!0||Ut.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0,U=K.columns,Y=K.rows,j=(z,W)=>{if(!Te)Tu(z,W,y);else{const ee=W.map(se=>({kind:Z.Text,data:b[se].title,displayData:b[se].title,allowOverlay:!1}));Tu([ee,...z],W,y)}};if(L&&on!==void 0){if(K.current!==void 0){let z=on(K.current.range,un.current.signal);typeof z!="object"&&(z=await z()),j(z,Cr(K.current.range.x-B,K.current.range.x+K.current.range.width-B))}else if(Y!==void 0&&Y.length>0){const W=[...Y].map(ee=>{const se=on({x:B,y:ee,width:b.length,height:1},un.current.signal);return typeof se=="object"?se[0]:se().then(ve=>ve[0])});if(W.some(ee=>ee instanceof Promise)){const ee=await Promise.all(W);j(ee,Cr(b.length))}else j(W,Cr(b.length))}else if(U.length>0){const z=[],W=[];for(const ee of U){let se=on({x:ee,y:0,width:1,height:Ve},un.current.signal);typeof se!="object"&&(se=await se()),z.push(se),W.push(ee-B)}if(z.length===1)j(z[0],W);else{const ee=z.reduce((se,ve)=>se.map((wt,nt)=>[...wt,...ve[nt]]));j(ee,W)}}}},[b,on,K,On.copy,B,Ut,Ve,Te]);dn("copy",Di,f,!1,!1);const qf=d.useCallback(async y=>{if(!(!On.cut||!(Ut.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0))&&(await Di(y),K.current!==void 0)){let L={current:{cell:K.current.cell,range:K.current.range,rangeStack:[]},rows:rt.empty(),columns:rt.empty()};const U=Ue?.(L);if(U===!1||(L=U===!0?L:U,L.current===void 0))return;Fr(L.current.range)}},[Fr,K,On.cut,Di,Ut,Ue]);dn("cut",qf,f,!1,!1);const Gf=d.useCallback((y,A)=>{if(fe!==void 0){B!==0&&(y=y.map(Y=>[Y[0]-B,Y[1]])),fe(y,A);return}if(y.length===0||A===-1)return;const[L,U]=y[A];c.current!==void 0&&c.current[0]===L&&c.current[1]===U||(c.current=[L,U],mr(L,U,!1,!1))},[fe,B,mr]),[Eo,Io]=zt?.current?.cell??[],To=d.useRef(Zt);To.current=Zt,d.useLayoutEffect(()=>{za.current&&!fr.current&&Eo!==void 0&&Io!==void 0&&(Eo!==ie.current?.current?.cell[0]||Io!==ie.current?.current?.cell[1])&&To.current(Eo,Io),fr.current=!1},[Eo,Io]);const kl=K.current!==void 0&&(K.current.cell[0]>=mt.length||K.current.cell[1]>=pn);d.useLayoutEffect(()=>{kl&&me(No,!1)},[kl,me]);const Yf=d.useMemo(()=>Wt===!0&&Ft?.tint===!0?rt.fromSingleSelection(pn-1):rt.empty(),[pn,Wt,Ft?.tint]),Xf=d.useCallback(y=>typeof ln=="boolean"?ln:ln?.(y-B)??!0,[B,ln]),jf=d.useMemo(()=>{if(Pa===void 0||a.current===null)return null;const{bounds:y,group:A}=Pa,L=a.current.getBoundingClientRect();return d.createElement(R0,{bounds:y,group:A,canvasBounds:L,onClose:()=>_a(void 0),onFinish:U=>{_a(void 0),te?.(A,U)}})},[te,Pa]),Kf=Math.min(mt.length,Oe+(Pn?1:0));d.useImperativeHandle(t,()=>({appendRow:(y,A)=>Kr(y+B,A),updateCells:y=>(B!==0&&(y=y.map(A=>({cell:[A.cell[0]+B,A.cell[1]]}))),mn.current?.damage(y)),getBounds:(y,A)=>{if(!(a?.current===null||Ut?.current===null)){if(y===void 0&&A===void 0){const L=a.current.getBoundingClientRect(),U=L.width/Ut.current.clientWidth;return{x:L.x-Ut.current.scrollLeft*U,y:L.y-Ut.current.scrollTop*U,width:Ut.current.scrollWidth*U,height:Ut.current.scrollHeight*U}}return mn.current?.getBounds((y??0)+B,A)}},focus:()=>mn.current?.focus(),emit:async y=>{switch(y){case"delete":Ti({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-right":Ti({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-down":Ti({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"copy":await Di(void 0,!0);break;case"paste":await Va();break}},scrollTo:Zt,remeasureColumns:y=>{for(const A of y)Mo(A+B)},getMouseArgsForPosition:(y,A,L)=>{if(mn?.current===null)return;const U=mn.current.getMouseArgsForPosition(y,A,L);if(U!==void 0)return{...U,location:[U.location[0]-B,U.location[1]]}}}),[Kr,Mo,Ut,Di,Ti,Va,B,Zt]);const[Ml,Rl]=ei??[],Zf=d.useCallback(y=>{const[A,L]=y;if(L===-1){De!=="none"&&(lt(rt.fromSingleSelection(A),void 0,!1),yn());return}Ml===A&&Rl===L||(Fe({cell:y,range:{x:A,y:L,width:1,height:1}},!0,!1,"keyboard-nav"),Zt(A,L))},[De,yn,Zt,Ml,Rl,Fe,lt]),[Jf,Qf]=d.useState(!1),El=d.useRef(Xc(y=>{Qf(y)},5)),eh=d.useCallback(()=>{El.current(!0),K.current===void 0&&K.columns.length===0&&K.rows.length===0&&l===void 0&&Fe({cell:[B,wo],range:{x:B,y:wo,width:1,height:1}},!0,!1,"keyboard-select")},[wo,K,l,B,Fe]),th=d.useCallback(()=>{El.current(!1)},[]),[nh,rh]=d.useMemo(()=>{let y;const A=Qt?.scrollbarWidthOverride??ws(),L=Ve+(Wt?1:0);if(typeof zn=="number")y=Rt+L*zn;else{let Y=0;const j=Math.min(L,10);for(let z=0;z<j;z++)Y+=zn(z);Y=Math.floor(Y/j),y=Rt+L*Y}y+=A;const U=mt.reduce((Y,j)=>j.width+Y,0)+A;return[`${Math.min(1e5,U)}px`,`${Math.min(1e5,y)}px`]},[mt,Qt?.scrollbarWidthOverride,zn,Ve,Wt,Rt]),ih=d.useMemo(()=>Um(at),[at]);return d.createElement(vd.Provider,{value:at},d.createElement($0,{style:ih,className:X,inWidth:p??nh,inHeight:w??rh},d.createElement(S0,{fillHandle:Et,drawFocusRing:Rr,experimental:Qt,fixedShadowX:Lt,fixedShadowY:At,getRowThemeOverride:ur,headerIcons:xn,imageWindowLoader:Un,initialSize:Hn,isDraggable:N,onDragLeave:Be,onRowMoved:Pe,overscrollX:Ma,overscrollY:Ra,preventDiagonalScrolling:ge,rightElement:bt,rightElementProps:en,smoothScrollX:Bt,smoothScrollY:an,className:X,enableGroups:tr,onCanvasFocused:eh,onCanvasBlur:th,canvasRef:a,onContextMenu:Uf,theme:at,cellXOffset:If,cellYOffset:wo,accessibilityHeight:Dr.height,onDragEnd:Nf,columns:mt,nonGrowWidth:Tr,drawHeader:Ye,onColumnProposeMove:Hf,drawCell:we,disabledRows:Yf,freezeColumns:Kf,lockColumns:B,firstColAccessible:B===0,getCellContent:Rn,minColumnWidth:Gn,maxColumnWidth:Ir,searchInputRef:s,showSearch:xi,onSearchClose:Ea,highlightRegions:Tf,getCellsForSelection:on,getGroupDetails:Da,headerHeight:Si,isFocused:Jf,groupHeaderHeight:tr?vo:0,freezeTrailingRows:Yt+(Wt&&Ft?.sticky===!0?1:0),hasAppendRow:Wt,onColumnResize:Me,onColumnResizeEnd:de,onColumnResizeStart:be,onCellFocused:Zf,onColumnMoved:zf,onDragStart:Vf,onHeaderMenuClick:Ff,onHeaderIndicatorClick:Lf,onItemHovered:Ha,isFilling:l?.fillHandle===!0,onMouseMove:_f,onKeyDown:Ti,onKeyUp:ue,onMouseDown:Df,onMouseUp:Pf,onDragOverCell:rn,onDrop:hn,onSearchResultsChanged:Gf,onVisibleRegionChanged:Af,clientSize:dt,rowHeight:zn,searchResults:re,searchValue:P,onSearchValueChange:H,rows:pn,scrollRef:Ut,selection:K,translateX:Dr.tx,translateY:Dr.ty,verticalBorder:Xf,gridRef:mn,getCellRenderer:ct,resizeIndicator:ka}),jf,i!==void 0&&d.createElement(d.Suspense,{fallback:null},d.createElement(J0,{...i,validateCell:V,bloom:D,id:Wf,getCellRenderer:ct,className:Qt?.isSubGrid===!0?"click-outside-ignore":void 0,provideEditor:Ct,imageEditorOverride:g,onFinishEditing:Bf,markdownDivCreateNode:m,isOutsideClick:lr,customEventTarget:Qt?.eventTarget}))))},nv=d.forwardRef(tv);function Pu(e){const{cell:t,posX:n,posY:r,bounds:i,theme:o}=e,{width:s,height:a,x:l,y:u}=i,c=t.maxSize??o.checkboxMaxSize,f=Math.floor(i.y+a/2),g=Zc(c,a,o.cellVerticalPadding),h=Kc(t.contentAlign??"center",l,s,o.cellHorizontalPadding,g),m=jc(h,f,g),p=Jc(l+n,u+r,m);return Hs(t)&&p}const rv={getAccessibilityString:e=>e.data?.toString()??"false",kind:Z.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:()=>50,draw:e=>iv(e,e.cell.data,Hs(e.cell),e.cell.maxSize??e.theme.checkboxMaxSize,e.cell.hoverEffectIntensity??.35),onDelete:e=>({...e,data:!1}),onSelect:e=>{Pu(e)&&e.preventDefault()},onClick:e=>{if(Pu(e))return{...e.cell,data:Od(e.cell.data)}},onPaste:(e,t)=>{let n=ea;return e.toLowerCase()==="true"?n=!0:e.toLowerCase()==="false"?n=!1:e.toLowerCase()==="indeterminate"&&(n=As),n===t.data?void 0:{...t,data:n}}};function iv(e,t,n,r,i){if(!n&&t===ea)return;const{ctx:o,hoverAmount:s,theme:a,rect:l,highlighted:u,hoverX:c,hoverY:f,cell:{contentAlign:g}}=e,{x:h,y:m,width:p,height:w}=l;let b=!1;if(i>0){let v=n?1-i+i*s:.4;if(t===ea&&(v*=s),v===0)return;v<1&&(b=!0,o.globalAlpha=v)}Xs(o,a,t,h,m,p,w,u,c,f,r,g),b&&(o.globalAlpha=1)}const ov=fn("div")({name:"BubblesOverlayEditorStyle",class:"gdg-b1ygi5by",propsAsIs:!1}),av=e=>{const{bubbles:t}=e;return d.createElement(ov,null,t.map((n,r)=>d.createElement("div",{key:r,className:"boe-bubble"},n)),d.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},sv={getAccessibilityString:e=>Qc(e.data),kind:Z.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>{const r=t.data.reduce((i,o)=>e.measureText(o).width+i+n.bubblePadding*2+n.bubbleMargin,0);return t.data.length===0?n.cellHorizontalPadding*2:r+2*n.cellHorizontalPadding-n.bubbleMargin},draw:e=>lv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return d.createElement(av,{bubbles:t.data})},onPaste:()=>{}};function lv(e,t){const{rect:n,theme:r,ctx:i,highlighted:o}=e,{x:s,y:a,width:l,height:u}=n;let c=s+r.cellHorizontalPadding;const f=[];for(const g of t){if(c>s+l)break;const h=qr(g,i,r.baseFontFull).width;f.push({x:c,width:h}),c+=h+r.bubblePadding*2+r.bubbleMargin}i.beginPath();for(const g of f)Kn(i,g.x,a+(u-r.bubbleHeight)/2,g.width+r.bubblePadding*2,r.bubbleHeight,r.roundingRadius??r.bubbleHeight/2);i.fillStyle=o?r.bgBubbleSelected:r.bgBubble,i.fill();for(const[g,h]of f.entries())i.beginPath(),i.fillStyle=r.textBubble,i.fillText(t[g],h.x+r.bubblePadding,a+u/2+Zn(i,r))}const uv=fn("div")({name:"DrilldownOverlayEditorStyle",class:"gdg-d4zsq0x",propsAsIs:!1}),cv=e=>{const{drilldowns:t}=e;return d.createElement(uv,null,t.map((n,r)=>d.createElement("div",{key:r,className:"doe-bubble"},n.img!==void 0&&d.createElement("img",{src:n.img}),d.createElement("div",null,n.text))))},dv={getAccessibilityString:e=>Qc(e.data.map(t=>t.text)),kind:Z.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce((r,i)=>e.measureText(i.text).width+r+n.bubblePadding*2+n.bubbleMargin+(i.img!==void 0?18:0),0)+2*n.cellHorizontalPadding-4,draw:e=>hv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return d.createElement(cv,{drilldowns:t.data})},onPaste:()=>{}},os={};function fv(e,t,n,r){const i=Math.ceil(window.devicePixelRatio),o=5,s=n-o*2,a=4,l=n*i,u=r+o,c=r*3,f=(c+o*2)*i,g=`${e},${t},${i},${n}`;if(os[g]!==void 0)return{el:os[g],height:l,width:f,middleWidth:a*i,sideWidth:u*i,padding:o*i,dpr:i};const h=document.createElement("canvas"),m=h.getContext("2d");return m===null?null:(h.width=f,h.height=l,m.scale(i,i),os[g]=h,m.beginPath(),Kn(m,o,o,c,s,r),m.shadowColor="rgba(24, 25, 34, 0.4)",m.shadowBlur=1,m.fillStyle=e,m.fill(),m.shadowColor="rgba(24, 25, 34, 0.3)",m.shadowOffsetY=1,m.shadowBlur=5,m.fillStyle=e,m.fill(),m.shadowOffsetY=0,m.shadowBlur=0,m.shadowBlur=0,m.beginPath(),Kn(m,o+.5,o+.5,c,s,r),m.strokeStyle=t,m.lineWidth=1,m.stroke(),{el:h,height:l,width:f,sideWidth:u*i,middleWidth:r*i,padding:o*i,dpr:i})}function hv(e,t){const{rect:n,theme:r,ctx:i,imageLoader:o,col:s,row:a}=e,{x:l,width:u}=n,c=r.baseFontFull,f=Us(i,c),g=Math.min(n.height,Math.max(16,Math.ceil(f*r.lineHeight)*2)),h=Math.floor(n.y+(n.height-g)/2),m=g-10,p=r.bubblePadding,w=r.bubbleMargin;let b=l+r.cellHorizontalPadding;const v=r.roundingRadius??6,S=fv(r.bgCell,r.drilldownBorder,g,v),O=[];for(const R of t){if(b>l+u)break;const _=qr(R.text,i,c).width;let E=0;R.img!==void 0&&o.loadOrGetImage(R.img,s,a)!==void 0&&(E=m-8+4);const k=_+E+p*2;O.push({x:b,width:k}),b+=k+w}if(S!==null){const{el:R,height:M,middleWidth:_,sideWidth:E,width:k,dpr:F,padding:D}=S,C=E/F,I=D/F;for(const T of O){const x=Math.floor(T.x),$=Math.floor(T.width),q=$-(C-I)*2;i.imageSmoothingEnabled=!1,i.drawImage(R,0,0,E,M,x-I,h,C,g),q>0&&i.drawImage(R,E,0,_,M,x+(C-I),h,q,g),i.drawImage(R,k-E,0,E,M,x+$-(C-I),h,C,g),i.imageSmoothingEnabled=!0}}i.beginPath();for(const[R,M]of O.entries()){const _=t[R];let E=M.x+p;if(_.img!==void 0){const k=o.loadOrGetImage(_.img,s,a);if(k!==void 0){const F=m-8;let D=0,C=0,I=k.width,T=k.height;I>T?(D+=(I-T)/2,I=T):T>I&&(C+=(T-I)/2,T=I),i.beginPath(),Kn(i,E,h+g/2-F/2,F,F,r.roundingRadius??3),i.save(),i.clip(),i.drawImage(k,D,C,I,T,E,h+g/2-F/2,F,F),i.restore(),E+=F+4}}i.beginPath(),i.fillStyle=r.textBubble,i.fillText(_.text,E,h+g/2+Zn(i,r))}}const gv={getAccessibilityString:e=>e.data.join(", "),kind:Z.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:e=>mv(e,e.cell.displayData??e.cell.data,e.cell.rounding??e.theme.roundingRadius??4,e.cell.contentAlign),measure:(e,t)=>t.data.length*50,onDelete:e=>({...e,data:[]}),provideEditor:()=>e=>{const{value:t,onFinishedEditing:n,imageEditorOverride:r}=e,i=r??em;return d.createElement(i,{urls:t.data,canWrite:t.readonly!==!0,onCancel:n,onChange:o=>{n({...t,data:[o]})}})},onPaste:(e,t)=>{e=e.trim();const r=e.split(",").map(i=>{try{return new URL(i),i}catch{return}}).filter(i=>i!==void 0);if(!(r.length===t.data.length&&r.every((i,o)=>i===t.data[o])))return{...t,data:r}}},as=4;function mv(e,t,n,r){const{rect:i,col:o,row:s,theme:a,ctx:l,imageLoader:u}=e,{x:c,y:f,height:g,width:h}=i,m=g-a.cellVerticalPadding*2,p=[];let w=0;for(let v=0;v<t.length;v++){const S=t[v];if(S.length===0)continue;const O=u.loadOrGetImage(S,o,s);if(O!==void 0){p[v]=O;const R=O.width*(m/O.height);w+=R+as}}if(w===0)return;w-=as;let b=c+a.cellHorizontalPadding;r==="right"?b=Math.floor(c+h-a.cellHorizontalPadding-w):r==="center"&&(b=Math.floor(c+h/2-w/2));for(const v of p){if(v===void 0)continue;const S=v.width*(m/v.height);n>0&&(l.beginPath(),Kn(l,b,f+a.cellVerticalPadding,S,m,n),l.save(),l.clip()),l.drawImage(v,b,f+a.cellVerticalPadding,S,m),n>0&&l.restore(),b+=S+as}}function pv(e,t){let n=e*49632+t*325176;return n^=n<<13,n^=n>>17,n^=n<<5,n/4294967295*2}const vv={getAccessibilityString:()=>"",kind:Z.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:()=>120,draw:e=>{const{cell:t,col:n,row:r,ctx:i,rect:o,theme:s}=e;if(t.skeletonWidth===void 0||t.skeletonWidth===0)return;let a=t.skeletonWidth;t.skeletonWidthVariability!==void 0&&t.skeletonWidthVariability>0&&(a+=Math.round(pv(n,r)*t.skeletonWidthVariability));const l=s.cellHorizontalPadding;a+l*2>=o.width&&(a=o.width-l*2-1);const u=t.skeletonHeight??Math.min(18,o.height-2*s.cellVerticalPadding);Kn(i,o.x+l,o.y+(o.height-u)/2,a,u,s.roundingRadius??3),i.fillStyle=Nr(s.textDark,.1),i.fill()},onPaste:()=>{}},bv=()=>e=>e.targetWidth,_u=fn("div")({name:"MarkdownOverlayEditorStyle",class:"gdg-m1pnx84e",propsAsIs:!1,vars:{"m1pnx84e-0":[bv(),"px"]}}),wv=e=>{const{value:t,onChange:n,forceEditMode:r,createNode:i,targetRect:o,onFinish:s,validatedSelection:a}=e,l=t.data,u=t.readonly===!0,[c,f]=d.useState(l===""||r),g=d.useCallback(()=>{f(m=>!m)},[]),h=l?"gdg-ml-6":"";return c?d.createElement(_u,{targetWidth:o.width-20},d.createElement(Wr,{autoFocus:!0,highlight:!1,validatedSelection:a,value:l,onKeyDown:m=>{m.key==="Enter"&&m.stopPropagation()},onChange:n}),d.createElement("div",{className:`gdg-edit-icon gdg-checkmark-hover ${h}`,onClick:()=>s(t)},d.createElement(Yg,null))):d.createElement(_u,{targetWidth:o.width},d.createElement(wm,{contents:l,createNode:i}),!u&&d.createElement(d.Fragment,null,d.createElement("div",{className:"spacer"}),d.createElement("div",{className:`gdg-edit-icon gdg-edit-hover ${h}`,onClick:g},d.createElement(Vs,null))),d.createElement("textarea",{className:"gdg-md-edit-textarea gdg-input",autoFocus:!0}))},yv={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:lo,measure:(e,t,n)=>{const r=t.data.split(`
`)[0];return e.measureText(r).width+2*n.cellHorizontalPadding},draw:e=>jn(e,e.cell.data,e.cell.contentAlign),onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{const{onChange:t,value:n,target:r,onFinishedEditing:i,markdownDivCreateNode:o,forceEditMode:s,validatedSelection:a}=e;return d.createElement(wv,{onFinish:i,targetRect:r,value:n,validatedSelection:a,onChange:l=>t({...n,data:l.target.value}),forceEditMode:s,createNode:o})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},Cv={getAccessibilityString:e=>e.row.toString(),kind:An.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:Sv,measure:()=>44,draw:e=>kv(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle,e.cell.checkboxStyle),onClick:e=>{const{bounds:t,cell:n,posX:r,posY:i}=e,{width:o,height:s}=t,a=n.drawHandle?7+(o-7)/2:o/2,l=s/2;if(Math.abs(r-a)<=10&&Math.abs(i-l)<=10)return{...n,checked:!n.checked}},onPaste:()=>{}};function Sv(e,t){const{ctx:n,theme:r}=e,i=r.markerFontFull,o=t??{};return o?.font!==i&&(n.font=i,o.font=i),o.deprep=xv,n.textAlign="center",o}function xv(e){const{ctx:t}=e;t.textAlign="start"}function kv(e,t,n,r,i,o){const{ctx:s,rect:a,hoverAmount:l,theme:u}=e,{x:c,y:f,width:g,height:h}=a,m=n?1:r==="checkbox-visible"?.6+.4*l:l;if(r!=="number"&&m>0){s.globalAlpha=m;const p=7*(n?l:1);if(Xs(s,u,n,i?c+p:c,f,i?g-p:g,h,!0,void 0,void 0,u.checkboxMaxSize,"center",o),i){s.globalAlpha=l,s.beginPath();for(const w of[3,6])for(const b of[-5,-1,3])s.rect(c+w,f+h/2+b,2,2);s.fillStyle=u.textLight,s.fill(),s.beginPath()}s.globalAlpha=1}if(r==="number"||r==="both"&&!n){const p=t.toString(),w=u.markerFontFull,b=c+g/2;r==="both"&&l!==0&&(s.globalAlpha=1-l),s.fillStyle=u.textLight,s.font=w,s.fillText(p,b,f+h/2+Zn(s,w)),l!==0&&(s.globalAlpha=1)}}const Mv={getAccessibilityString:()=>"",kind:An.NewRow,needsHover:!0,needsHoverPosition:!1,measure:()=>200,draw:e=>Rv(e,e.cell.hint,e.cell.icon),onPaste:()=>{}};function Rv(e,t,n){const{ctx:r,rect:i,hoverAmount:o,theme:s,spriteManager:a}=e,{x:l,y:u,width:c,height:f}=i;r.beginPath(),r.globalAlpha=o,r.rect(l+1,u+1,c,f-2),r.fillStyle=s.bgHeaderHovered,r.fill(),r.globalAlpha=1,r.beginPath();const g=t!=="";let h=0;if(n!==void 0){const p=f-8,w=l+8/2,b=u+8/2;a.drawSprite(n,"normal",r,w,b,p,s,g?1:o),h=p}else{h=24;const m=12,p=g?m:o*m,w=g?0:(1-o)*m*.5,b=s.cellHorizontalPadding+4;p>0&&(r.moveTo(l+b+w,u+f/2),r.lineTo(l+b+w+p,u+f/2),r.moveTo(l+b+w+p*.5,u+f/2-p*.5),r.lineTo(l+b+w+p*.5,u+f/2+p*.5),r.lineWidth=2,r.strokeStyle=s.bgIconHeader,r.lineCap="round",r.stroke())}r.fillStyle=s.textMedium,r.fillText(t,h+l+s.cellHorizontalPadding+.5,u+f/2+Zn(r,s)),r.beginPath()}function Fd(e,t,n,r,i,o,s){e.textBaseline="alphabetic";const a=Ev(e,i,r,t,n?.fullSize??!1);e.beginPath(),Kn(e,a.x,a.y,a.width,a.height,t.roundingRadius??4),e.globalAlpha=o,e.fillStyle=n?.bgColor??Nr(t.textDark,.1),e.fill(),e.globalAlpha=1,e.fillStyle=t.textDark,e.textBaseline="middle",s?.("text")}function Ev(e,t,n,r,i){const o=r.cellHorizontalPadding,s=r.cellVerticalPadding;if(i)return{x:t.x+o/2,y:t.y+s/2+1,width:t.width-o,height:t.height-s-1};const a=qr(n,e,r.baseFontFull,"alphabetic"),l=t.height-s,u=Math.min(l,a.actualBoundingBoxAscent*2.5);return{x:t.x+o/2,y:t.y+(t.height-u)/2+1,width:a.width+o*3,height:u-1}}const Iv=d.lazy(async()=>await _s(()=>import("./number-overlay-editor.BRNxOzEZ.js"),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),Tv={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Number,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,useLabel:!0,drawPrep:lo,draw:e=>{const{hoverAmount:t,cell:n,ctx:r,theme:i,rect:o,overrideCursor:s}=e,{hoverEffect:a,displayData:l,hoverEffectTheme:u}=n;a===!0&&t>0&&Fd(r,i,u,l,o,t,s),jn(e,e.cell.displayData,e.cell.contentAlign)},measure:(e,t,n)=>e.measureText(t.displayData).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:void 0}),provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return d.createElement(d.Suspense,{fallback:null},d.createElement(Iv,{highlight:t,disabled:r.readonly===!0,value:r.data,fixedDecimals:r.fixedDecimals,allowNegative:r.allowNegative,thousandSeparator:r.thousandSeparator,decimalSeparator:r.decimalSeparator,validatedSelection:i,onChange:o=>n({...r,data:Number.isNaN(o.floatValue??0)?0:o.floatValue})}))},onPaste:(e,t,n)=>{const r=typeof n.rawValue=="number"?n.rawValue:Number.parseFloat(typeof n.rawValue=="string"?n.rawValue:e);if(!(Number.isNaN(r)||t.data===r))return{...t,data:r,displayData:n.formattedString??t.displayData}}},Dv={getAccessibilityString:()=>"",measure:()=>108,kind:Z.Protected,needsHover:!1,needsHoverPosition:!1,draw:Ov,onPaste:()=>{}};function Ov(e){const{ctx:t,theme:n,rect:r}=e,{x:i,y:o,height:s}=r;t.beginPath();const a=2.5;let l=i+n.cellHorizontalPadding+a;const u=o+s/2,c=Math.cos(Jl(30))*a,f=Math.sin(Jl(30))*a;for(let g=0;g<12;g++)t.moveTo(l,u-a),t.lineTo(l,u+a),t.moveTo(l+c,u-f),t.lineTo(l-c,u+f),t.moveTo(l-c,u-f),t.lineTo(l+c,u+f),l+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()}const Pv={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:(e,t)=>lo(e,t,e.theme.textLight),draw:e=>jn(e,e.cell.data,e.cell.contentAlign),measure:(e,t,n)=>e.measureText(t.data).width+n.cellHorizontalPadding*2,provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return Nt.createElement(Wr,{highlight:t,autoFocus:r.readonly!==!0,disabled:r.readonly!==!1,value:r.data,validatedSelection:i,onChange:o=>n({...r,data:o.target.value})})},onPaste:()=>{}},_v={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Text,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,drawPrep:lo,useLabel:!0,draw:e=>{const{cell:t,hoverAmount:n,hyperWrapping:r,ctx:i,rect:o,theme:s,overrideCursor:a}=e,{displayData:l,contentAlign:u,hoverEffect:c,allowWrapping:f,hoverEffectTheme:g}=t;c===!0&&n>0&&Fd(i,s,g,l,o,n,a),jn(e,l,u,f,r)},measure:(e,t,n)=>{const r=t.displayData.split(`
`,t.allowWrapping===!0?void 0:1);let i=0;for(const o of r)i=Math.max(i,e.measureText(o).width);return i+2*n.cellHorizontalPadding},onDelete:e=>({...e,data:""}),provideEditor:e=>({disablePadding:e.allowWrapping===!0,editor:t=>{const{isHighlighted:n,onChange:r,value:i,validatedSelection:o}=t;return d.createElement(Wr,{style:e.allowWrapping===!0?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:i.readonly!==!0,disabled:i.readonly===!0,altNewline:!0,value:i.data,validatedSelection:o,onChange:s=>r({...i,data:s.target.value})})}}),onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},Fv=fn("div")({name:"UriOverlayEditorStyle",class:"gdg-u1rrojo",propsAsIs:!1}),Lv=e=>{const{uri:t,onChange:n,forceEditMode:r,readonly:i,validatedSelection:o,preview:s}=e,[a,l]=d.useState(!i&&(t===""||r)),u=d.useCallback(()=>{l(!0)},[]);return a?d.createElement(Wr,{validatedSelection:o,highlight:!0,autoFocus:!0,value:t,onChange:n}):d.createElement(Fv,null,d.createElement("a",{className:"gdg-link-area",href:t,target:"_blank",rel:"noopener noreferrer"},s),!i&&d.createElement("div",{className:"gdg-edit-icon",onClick:u},d.createElement(Vs,null)),d.createElement("textarea",{className:"gdg-input",autoFocus:!0}))};function Ld(e,t,n,r){let i=n.cellHorizontalPadding;const o=t.height/2-e.actualBoundingBoxAscent/2,s=e.width,a=e.actualBoundingBoxAscent;return r==="right"?i=t.width-s-n.cellHorizontalPadding:r==="center"&&(i=t.width/2-s/2),{x:i,y:o,width:s,height:a}}function Fu(e){const{cell:t,bounds:n,posX:r,posY:i,theme:o}=e,s=t.displayData??t.data;if(t.hoverEffect!==!0||t.onClickUri===void 0)return!1;const a=dd(s,o.baseFontFull);if(a===void 0)return!1;const l=Ld(a,n,o,t.contentAlign);return Vr({x:l.x-4,y:l.y-4,width:l.width+8,height:l.height+8},r,i)}const Av={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Uri,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!0,useLabel:!0,drawPrep:lo,draw:e=>{const{cell:t,theme:n,overrideCursor:r,hoverX:i,hoverY:o,rect:s,ctx:a}=e,l=t.displayData??t.data,u=t.hoverEffect===!0;if(r!==void 0&&u&&i!==void 0&&o!==void 0){const c=qr(l,a,n.baseFontFull),f=Ld(c,s,n,t.contentAlign),{x:g,y:h,width:m,height:p}=f;if(i>=g-4&&i<=g-4+m+8&&o>=h-4&&o<=h-4+p+8){const w=Zn(a,n.baseFontFull);r("pointer");const b=5,v=h-w;a.beginPath(),a.moveTo(s.x+g,Math.floor(s.y+v+p+b)+.5),a.lineTo(s.x+g+m,Math.floor(s.y+v+p+b)+.5),a.strokeStyle=n.linkColor,a.stroke(),a.save(),a.fillStyle=e.cellFillColor,jn({...e,rect:{...s,x:s.x-1}},l,t.contentAlign),jn({...e,rect:{...s,x:s.x-2}},l,t.contentAlign),jn({...e,rect:{...s,x:s.x+1}},l,t.contentAlign),jn({...e,rect:{...s,x:s.x+2}},l,t.contentAlign),a.restore()}}a.fillStyle=u?n.linkColor:n.textDark,jn(e,l,t.contentAlign)},onSelect:e=>{Fu(e)&&e.preventDefault()},onClick:e=>{const{cell:t}=e;Fu(e)&&t.onClickUri?.(e)},measure:(e,t,n)=>e.measureText(t.displayData??t.data).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:""}),provideEditor:e=>t=>{const{onChange:n,value:r,forceEditMode:i,validatedSelection:o}=t;return d.createElement(Lv,{forceEditMode:r.readonly!==!0&&(i||e.hoverEffect===!0&&e.onClickUri!==void 0),uri:r.data,preview:r.displayData??r.data,validatedSelection:o,readonly:r.readonly===!0,onChange:s=>n({...r,data:s.target.value})})},onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},Hv=[Cv,Mv,rv,sv,dv,gv,vv,yv,Tv,Dv,Pv,_v,Av];var ss,Lu;function zv(){if(Lu)return ss;Lu=1;var e=Yc(),t=kc(),n="Expected a function";function r(i,o,s){var a=!0,l=!0;if(typeof i!="function")throw new TypeError(n);return t(s)&&(a="leading"in s?!!s.leading:a,l="trailing"in s?!!s.trailing:l),e(i,o,{leading:a,maxWait:o,trailing:l})}return ss=r,ss}var Vv=zv();const Nv=ar(Vv),ls=[];class $v extends gd{imageLoaded=()=>{};loadedLocations=[];cache={};setCallback(t){this.imageLoaded=t}sendLoaded=Nv(()=>{this.imageLoaded(new Ji(this.loadedLocations)),this.loadedLocations=[]},20);clearOutOfWindow=()=>{const t=Object.keys(this.cache);for(const n of t){const r=this.cache[n];let i=!1;for(let o=0;o<r.cells.length;o++){const s=r.cells[o];if(this.isInWindow(s)){i=!0;break}}i?r.cells=r.cells.filter(this.isInWindow):(r.cancel(),delete this.cache[n])}};loadImage(t,n,r,i){let o=!1;const s=ls.pop()??new Image;let a=!1;const l={img:void 0,cells:[Wn(n,r)],url:t,cancel:()=>{a||(a=!0,ls.length<12?ls.unshift(s):o||(s.src=""))}},u=new Promise(c=>s.addEventListener("load",()=>c(null)));requestAnimationFrame(async()=>{try{s.src=t,await u,await s.decode();const c=this.cache[i];if(c!==void 0&&!a){c.img=s;for(const f of c.cells)this.loadedLocations.push(Ys(f));o=!0,this.sendLoaded()}}catch{l.cancel()}}),this.cache[i]=l}loadOrGetImage(t,n,r){const i=t,o=this.cache[i];if(o!==void 0){const s=Wn(n,r);return o.cells.includes(s)||o.cells.push(s),o.img}else this.loadImage(t,n,r,i)}}const Bv=(e,t)=>{const n=d.useMemo(()=>({...Pp,...e.headerIcons}),[e.headerIcons]),r=d.useMemo(()=>e.renderers??Hv,[e.renderers]),i=d.useMemo(()=>e.imageWindowLoader??new $v,[e.imageWindowLoader]);return d.createElement(nv,{...e,renderers:r,headerIcons:n,ref:t,imageWindowLoader:i})},Wv=d.forwardRef(Bv);function Au(e,t){const n=d.useRef(null),r=d.useRef(),i=d.useCallback(()=>{n.current&&(clearTimeout(n.current),n.current=null)},[]);return d.useEffect(()=>i,[i]),{debouncedCallback:d.useCallback((...s)=>{r.current=s,i(),n.current=setTimeout(()=>{r.current&&(e(...r.current),r.current=void 0)},t)},[e,t,i]),cancel:i}}const Ad=mi("div",{target:"ee4nolw0"})(({theme:e})=>({paddingTop:e.spacing.xs,paddingBottom:e.spacing.xs})),yr=mi("div",{target:"ee4nolw1"})(({theme:e,isActive:t,hasSubmenu:n})=>({display:"flex",alignItems:"center",justifyContent:"flex-start",gap:e.spacing.sm,paddingLeft:e.spacing.sm,paddingRight:e.spacing.sm,paddingTop:e.spacing.twoXS,paddingBottom:e.spacing.twoXS,cursor:"pointer",backgroundColor:t?e.colors.darkenedBgMix15:void 0,"&:hover":{backgroundColor:e.colors.darkenedBgMix15},minWidth:e.sizes.minMenuWidth,...n&&{justifyContent:"space-between","& > :first-of-type":{display:"flex",alignItems:"center",gap:e.spacing.sm}}})),Uv=mi("div",{target:"ee4nolw2"})(({theme:e})=>({height:e.sizes.borderWidth,backgroundColor:e.colors.borderColor,marginTop:e.spacing.xs,marginBottom:e.spacing.xs})),Hu=[{format:"",label:"Automatic",icon:":material/123:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"plain",label:"Plain",icon:":material/speed_1_75:"},{format:"compact",label:"Compact",icon:":material/1k:"},{format:"dollar",label:"Dollar",icon:":material/attach_money:"},{format:"euro",label:"Euro",icon:":material/euro:"},{format:"yen",label:"Yen",icon:":material/currency_yen:"},{format:"percent",label:"Percent",icon:":material/percent:"},{format:"scientific",label:"Scientific",icon:":material/experiment:"},{format:"accounting",label:"Accounting",icon:":material/finance_chip:"}],qv={number:Hu,progress:Hu,datetime:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"},{format:"calendar",label:"Calendar",icon:":material/today:"}],date:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"}],time:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"}]};function Gv({columnKind:e,isOpen:t,onMouseEnter:n,onMouseLeave:r,onChangeFormat:i,onCloseMenu:o,children:s}){const a=$r(),{colors:l,fontSizes:u,radii:c,fontWeights:f}=a,g=qv[e]||[];return g.length===0?Qe(Ec,{}):Qe(va,{triggerType:Ic.hover,returnFocus:!0,autoFocus:!0,focusLock:!0,isOpen:t,onMouseEnter:n,onMouseLeave:r,ignoreBoundary:!0,content:Qe(Ad,{role:"menu",children:g.map(h=>In(yr,{onClick:()=>{i(h.format),o()},role:"menuitem",children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:h.icon}),h.label]},h.format))}),placement:ma.right,showArrow:!1,popoverMargin:2,overrides:{Body:{props:{"data-testid":"stDataFrameColumnFormattingMenu"},style:{borderTopLeftRadius:c.default,borderTopRightRadius:c.default,borderBottomLeftRadius:c.default,borderBottomRightRadius:c.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${a.sizes.borderWidth} solid ${a.colors.borderColor}`}},Inner:{style:{backgroundColor:pa(a)?l.bgColor:l.secondaryBg,color:l.bodyText,fontSize:u.sm,fontWeight:f.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:s})}const Yv=d.memo(Gv);function Xv({top:e,left:t,isColumnPinned:n,onPinColumn:r,onUnpinColumn:i,onCloseMenu:o,onSortColumn:s,onHideColumn:a,columnKind:l,onChangeFormat:u,onAutosize:c}){const f=$r(),[g,h]=d.useState(!1),{colors:m,fontSizes:p,radii:w,fontWeights:b}=f;d.useEffect(()=>{function S(O){O.preventDefault()}return document.addEventListener("wheel",S,{passive:!1}),document.addEventListener("touchmove",S,{passive:!1}),()=>{document.removeEventListener("wheel",S),document.removeEventListener("touchmove",S)}},[]);const v=d.useCallback(()=>{o()},[o]);return Qe(va,{autoFocus:!0,"aria-label":"Dataframe column menu",content:In(Ad,{children:[s&&In(Ec,{children:[In(yr,{onClick:()=>{s("asc"),v()},role:"menuitem",children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_upward:"}),"Sort ascending"]}),In(yr,{onClick:()=>{s("desc"),v()},role:"menuitem",children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_downward:"}),"Sort descending"]}),Qe(Uv,{})]}),u&&Qe(Yv,{columnKind:l,isOpen:g,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),onChangeFormat:u,onCloseMenu:v,children:In(yr,{onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),isActive:g,hasSubmenu:!0,children:[In("div",{children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/format_list_numbered:"}),"Format"]}),Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/chevron_right:"})]})}),c&&In(yr,{onClick:()=>{c(),v()},children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrows_outward:"}),"Autosize"]}),n&&In(yr,{onClick:()=>{i(),v()},children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep_off:"}),"Unpin column"]}),!n&&In(yr,{onClick:()=>{r(),v()},children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep:"}),"Pin column"]}),a&&In(yr,{onClick:()=>{a(),v()},children:[Qe(nr,{size:"base",margin:"0",color:"inherit",iconValue:":material/visibility_off:"}),"Hide column"]})]}),placement:ma.bottomRight,accessibilityType:Tc.menu,showArrow:!1,popoverMargin:cn("0.375rem"),onClickOutside:g?void 0:v,onEsc:v,overrides:{Body:{props:{"data-testid":"stDataFrameColumnMenu"},style:{paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{border:`${f.sizes.borderWidth} solid ${f.colors.borderColor}`,backgroundColor:pa(f)?m.bgColor:m.secondaryBg,color:m.bodyText,fontSize:p.sm,fontWeight:b.normal,borderTopLeftRadius:w.default,borderTopRightRadius:w.default,borderBottomLeftRadius:w.default,borderBottomRightRadius:w.default,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:!0,children:Qe("div",{"data-testid":"stDataFrameColumnMenuTarget",style:{position:"fixed",top:e,left:t,visibility:"hidden",transform:"unset"}})})}const jv=d.memo(Xv),Kv="(index)",Zv=({label:e,initialValue:t,onChange:n})=>{const r=$r();return Qe(dg,{checked:t,onChange:i=>{n(i.target.checked)},"aria-label":e,checkmarkType:cg.default,labelPlacement:ug.right,overrides:{Root:{style:({$isFocusVisible:i})=>({marginBottom:r.spacing.none,marginTop:r.spacing.none,paddingLeft:r.spacing.md,paddingRight:r.spacing.md,paddingTop:r.spacing.twoXS,paddingBottom:r.spacing.twoXS,backgroundColor:i?r.colors.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Checkmark:{style:({$isFocusVisible:i,$checked:o})=>{const s=o?r.colors.primary:r.colors.fadedText40;return{outline:0,width:r.sizes.checkbox,height:r.sizes.checkbox,marginTop:r.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:i&&o?`0 0 0 0.2rem ${ai(r.colors.primary,.5)}`:"",borderLeftWidth:r.sizes.borderWidth,borderRightWidth:r.sizes.borderWidth,borderTopWidth:r.sizes.borderWidth,borderBottomWidth:r.sizes.borderWidth,borderLeftColor:s,borderRightColor:s,borderTopColor:s,borderBottomColor:s}}},Label:{style:{lineHeight:r.lineHeights.small,paddingLeft:r.spacing.sm,position:"relative",color:r.colors.bodyText,fontSize:r.fontSizes.sm,fontWeight:r.fontWeights.normal}}},children:e})},Jv=({columns:e,columnOrder:t,setColumnOrder:n,hideColumn:r,showColumn:i,children:o,isOpen:s,onClose:a})=>{const l=$r();return Qe(va,{triggerType:Ic.click,placement:ma.bottomRight,autoFocus:!0,focusLock:!0,content:()=>Qe("div",{style:{paddingTop:l.spacing.sm,paddingBottom:l.spacing.sm},children:e.map(u=>{const c=t.length&&!u.isIndex?!t.includes(u.id)&&!t.includes(u.name):!1;return Qe(Zv,{label:!u.title&&u.isIndex?Kv:u.title,initialValue:!(u.isHidden===!0||c),onChange:f=>{f?(i(u.id),c&&n(g=>[...g,u.id])):r(u.id)}},u.id)})}),isOpen:s,onClickOutside:a,onClick:()=>s?a():void 0,onEsc:a,ignoreBoundary:!1,overrides:{Body:{props:{"data-testid":"stDataFrameColumnVisibilityMenu"},style:{borderTopLeftRadius:l.radii.default,borderTopRightRadius:l.radii.default,borderBottomLeftRadius:l.radii.default,borderBottomRightRadius:l.radii.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${l.sizes.borderWidth} solid ${l.colors.borderColor}`}},Inner:{style:{backgroundColor:pa(l)?l.colors.bgColor:l.colors.secondaryBg,color:l.colors.bodyText,fontSize:l.fontSizes.sm,fontWeight:l.fontWeights.normal,minWidth:l.sizes.minMenuWidth,maxWidth:`calc(${l.sizes.minMenuWidth} * 2)`,maxHeight:l.sizes.maxDropdownHeight,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:Qe("div",{children:o})})},Qv=d.memo(Jv);var e1=ph();const t1=ar(e1);var Zo={exports:{}};/*! Moment Duration Format v2.2.2
 *  https://github.com/jsmreese/moment-duration-format
 *  Date: 2018-02-16
 *
 *  Duration format plugin function for the Moment.js library
 *  http://momentjs.com/
 *
 *  Copyright 2018 John Madhavan-Reese
 *  Released under the MIT license
 */var n1=Zo.exports,zu;function r1(){return zu||(zu=1,function(e,t){(function(n,r){try{e.exports=r(vh)}catch{e.exports=r}n&&(n.momentDurationFormatSetup=n.moment?r(n.moment):r)})(n1,function(n){var r=!1,i=!1,o=!1,s=!1,a="escape years months weeks days hours minutes seconds milliseconds general".split(" "),l=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function u(H,P){return P.length>H.length?!1:H.indexOf(P)!==-1}function c(H){for(var P="";H;)P+="0",H-=1;return P}function f(H){for(var P=H.split("").reverse(),G=0,ue=!0;ue&&G<P.length;)G?P[G]==="9"?P[G]="0":(P[G]=(parseInt(P[G],10)+1).toString(),ue=!1):(parseInt(P[G],10)<5&&(ue=!1),P[G]="0"),G+=1;return ue&&P.push("1"),P.reverse().join("")}function g(H,P){var G=_(x(P).sort(),function(he){return he+":"+P[he]}).join(","),ue=H+"+"+G;return g.cache[ue]||(g.cache[ue]=Intl.NumberFormat(H,P)),g.cache[ue]}g.cache={};function h(H,P,G){var ue=P.useToLocaleString,he=P.useGrouping,ke=he&&P.grouping.slice(),Se=P.maximumSignificantDigits,et=P.minimumIntegerDigits||1,Re=P.fractionDigits||0,Xe=P.groupingSeparator,yt=P.decimalSeparator;if(ue&&G){var De={minimumIntegerDigits:et,useGrouping:he};if(Re&&(De.maximumFractionDigits=Re,De.minimumFractionDigits=Re),Se&&H>0&&(De.maximumSignificantDigits=Se),o){if(!s){var ne=T({},P);ne.useGrouping=!1,ne.decimalSeparator=".",H=parseFloat(h(H,ne),10)}return g(G,De).format(H)}else{if(!i){var ne=T({},P);ne.useGrouping=!1,ne.decimalSeparator=".",H=parseFloat(h(H,ne),10)}return H.toLocaleString(G,De)}}var Ee;Se?Ee=H.toPrecision(Se+1):Ee=H.toFixed(Re+1);var xe,ce,pe,ze=Ee.split("e");pe=ze[1]||"",ze=ze[0].split("."),ce=ze[1]||"",xe=ze[0]||"";var Ie=xe.length,tt=ce.length,Te=Ie+tt,Oe=xe+ce;(Se&&Te===Se+1||!Se&&tt===Re+1)&&(Oe=f(Oe),Oe.length===Te+1&&(Ie=Ie+1),tt&&(Oe=Oe.slice(0,-1)),xe=Oe.slice(0,Ie),ce=Oe.slice(Ie)),Se&&(ce=ce.replace(/0*$/,""));var Je=parseInt(pe,10);Je>0?ce.length<=Je?(ce=ce+c(Je-ce.length),xe=xe+ce,ce=""):(xe=xe+ce.slice(0,Je),ce=ce.slice(Je)):Je<0&&(ce=c(Math.abs(Je)-xe.length)+xe+ce,xe="0"),Se||(ce=ce.slice(0,Re),ce.length<Re&&(ce=ce+c(Re-ce.length)),xe.length<et&&(xe=c(et-xe.length)+xe));var ye="";if(he){ze=xe;for(var qe;ze.length;)ke.length&&(qe=ke.shift()),ye&&(ye=Xe+ye),ye=ze.slice(-qe)+ye,ze=ze.slice(0,-qe)}else ye=xe;return ce&&(ye=ye+yt+ce),ye}function m(H,P){return H.label.length>P.label.length?-1:H.label.length<P.label.length?1:0}function p(H,P){var G=[];return M(x(P),function(ue){if(ue.slice(0,15)==="_durationLabels"){var he=ue.slice(15).toLowerCase();M(x(P[ue]),function(ke){ke.slice(0,1)===H&&G.push({type:he,key:ke,label:P[ue][ke]})})}}),G}function w(H,P,G){return P===1&&G===null?H:H+H}var b={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:w};function v(H){return Object.prototype.toString.call(H)==="[object Array]"}function S(H){return Object.prototype.toString.call(H)==="[object Object]"}function O(H,P){for(var G=H.length;G-=1;)if(P(H[G]))return H[G]}function R(H,P){var G=0,ue=H&&H.length||0,he;for(typeof P!="function"&&(he=P,P=function(ke){return ke===he});G<ue;){if(P(H[G]))return H[G];G+=1}}function M(H,P){var G=0,ue=H.length;if(!(!H||!ue))for(;G<ue;){if(P(H[G],G)===!1)return;G+=1}}function _(H,P){var G=0,ue=H.length,he=[];if(!H||!ue)return he;for(;G<ue;)he[G]=P(H[G],G),G+=1;return he}function E(H,P){return _(H,function(G){return G[P]})}function k(H){var P=[];return M(H,function(G){G&&P.push(G)}),P}function F(H){var P=[];return M(H,function(G){R(P,G)||P.push(G)}),P}function D(H,P){var G=[];return M(H,function(ue){M(P,function(he){ue===he&&G.push(ue)})}),F(G)}function C(H,P){var G=[];return M(H,function(ue,he){if(!P(ue))return G=H.slice(he),!1}),G}function I(H,P){var G=H.slice().reverse();return C(G,P).reverse()}function T(H,P){for(var G in P)P.hasOwnProperty(G)&&(H[G]=P[G]);return H}function x(H){var P=[];for(var G in H)H.hasOwnProperty(G)&&P.push(G);return P}function $(H,P){var G=0,ue=H.length;if(!H||!ue)return!1;for(;G<ue;){if(P(H[G],G)===!0)return!0;G+=1}return!1}function q(H){var P=[];return M(H,function(G){P=P.concat(G)}),P}function X(){var H=0;try{H.toLocaleString("i")}catch(P){return P.name==="RangeError"}return!1}function oe(H){return H(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})==="3.6"}function Q(H){var P=!0;return P=P&&H(1,"en",{minimumIntegerDigits:1})==="1",P=P&&H(1,"en",{minimumIntegerDigits:2})==="01",P=P&&H(1,"en",{minimumIntegerDigits:3})==="001",!(!P||(P=P&&H(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0})==="100",P=P&&H(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1})==="100.0",P=P&&H(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2})==="99.99",P=P&&H(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3})==="99.990",!P)||(P=P&&H(99.99,"en",{maximumSignificantDigits:1})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:2})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:3})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:4})==="99.99",P=P&&H(99.99,"en",{maximumSignificantDigits:5})==="99.99",!P)||(P=P&&H(1e3,"en",{useGrouping:!0})==="1,000",P=P&&H(1e3,"en",{useGrouping:!1})==="1000",!P))}function J(){var H=[].slice.call(arguments),P={},G;if(M(H,function(Se,et){if(!et){if(!v(Se))throw"Expected array as the first argument to durationsFormat.";G=Se}if(typeof Se=="string"||typeof Se=="function"){P.template=Se;return}if(typeof Se=="number"){P.precision=Se;return}S(Se)&&T(P,Se)}),!G||!G.length)return[];P.returnMomentTypes=!0;var ue=_(G,function(Se){return Se.format(P)}),he=D(a,F(E(q(ue),"type"))),ke=P.largest;return ke&&(he=he.slice(0,ke)),P.returnMomentTypes=!1,P.outputTypes=he,_(G,function(Se){return Se.format(P)})}function te(){var H=[].slice.call(arguments),P=T({},this.format.defaults),G=this.asMilliseconds(),ue=this.asMonths();typeof this.isValid=="function"&&this.isValid()===!1&&(G=0,ue=0);var he=G<0,ke=n.duration(Math.abs(G),"milliseconds"),Se=n.duration(Math.abs(ue),"months");M(H,function(N){if(typeof N=="string"||typeof N=="function"){P.template=N;return}if(typeof N=="number"){P.precision=N;return}S(N)&&T(P,N)});var et={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},Re={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};P.types=a;var Xe=function(N){return R(a,function(Be){return Re[Be].test(N)})},yt=new RegExp(_(a,function(N){return Re[N].source}).join("|"),"g");P.duration=this;var De=typeof P.template=="function"?P.template.apply(P):P.template,ne=P.outputTypes,Ee=P.returnMomentTypes,xe=P.largest,ce=[];ne||(v(P.stopTrim)&&(P.stopTrim=P.stopTrim.join("")),P.stopTrim&&M(P.stopTrim.match(yt),function(N){var Be=Xe(N);Be==="escape"||Be==="general"||ce.push(Be)}));var pe=n.localeData();pe||(pe={}),M(x(b),function(N){if(typeof b[N]=="function"){pe[N]||(pe[N]=b[N]);return}pe["_"+N]||(pe["_"+N]=b[N])}),M(x(pe._durationTimeTemplates),function(N){De=De.replace("_"+N+"_",pe._durationTimeTemplates[N])});var ze=P.userLocale||n.locale(),Ie=P.useLeftUnits,tt=P.usePlural,Te=P.precision,Oe=P.forceLength,Je=P.useGrouping,ye=P.trunc,qe=P.useSignificantDigits&&Te>0,pt=qe?P.precision:0,ft=pt,We=P.minValue,xt=!1,qt=P.maxValue,Tt=!1,_t=P.useToLocaleString,tn=P.groupingSeparator,zt=P.decimalSeparator,Sn=P.grouping;_t=_t&&(r||o);var Vt=P.trim;v(Vt)&&(Vt=Vt.join(" ")),Vt===null&&(xe||qt||qe)&&(Vt="all"),(Vt===null||Vt===!0||Vt==="left"||Vt==="right")&&(Vt="large"),Vt===!1&&(Vt="");var ut=function(N){return N.test(Vt)},nn=/large/,Ct=/small/,Ft=/both/,Yt=/mid/,je=/^all|[^sm]all/,Dt=/final/,Ot=xe>0||$([nn,Ft,je],ut),ln=$([Ct,Ft,je],ut),rn=$([Yt,je],ut),hn=$([Dt,je],ut),bn=_(De.match(yt),function(N,Be){var Pe=Xe(N);return N.slice(0,1)==="*"&&(N=N.slice(1),Pe!=="escape"&&Pe!=="general"&&ce.push(Pe)),{index:Be,length:N.length,text:"",token:Pe==="escape"?N.replace(Re.escape,"$1"):N,type:Pe==="escape"||Pe==="general"?null:Pe}}),Xt={index:0,length:0,token:"",text:"",type:null},Jt=[];Ie&&bn.reverse(),M(bn,function(N){if(N.type){(Xt.type||Xt.text)&&Jt.push(Xt),Xt=N;return}Ie?Xt.text=N.token+Xt.text:Xt.text+=N.token}),(Xt.type||Xt.text)&&Jt.push(Xt),Ie&&Jt.reverse();var Ge=D(a,F(k(E(Jt,"type"))));if(!Ge.length)return E(Jt,"text").join("");Ge=_(Ge,function(N,Be){var Pe=Be+1===Ge.length,Pt=!Be,wn;N==="years"||N==="months"?wn=Se.as(N):wn=ke.as(N);var ge=Math.floor(wn),bt=wn-ge,en=R(Jt,function(kt){return N===kt.type});return Pt&&qt&&wn>qt&&(Tt=!0),Pe&&We&&Math.abs(P.duration.as(N))<We&&(xt=!0),Pt&&Oe===null&&en.length>1&&(Oe=!0),ke.subtract(ge,N),Se.subtract(ge,N),{rawValue:wn,wholeValue:ge,decimalValue:Pe?bt:0,isSmallest:Pe,isLargest:Pt,type:N,tokenLength:en.length}});var Et=ye?Math.floor:Math.round,Qt=function(N,Be){var Pe=Math.pow(10,Be);return Et(N*Pe)/Pe},Lt=!1,At=!1,xn=function(N,Be){var Pe={useGrouping:Je,groupingSeparator:tn,decimalSeparator:zt,grouping:Sn,useToLocaleString:_t};return qe&&(pt<=0?(N.rawValue=0,N.wholeValue=0,N.decimalValue=0):(Pe.maximumSignificantDigits=pt,N.significantDigits=pt)),Tt&&!At&&(N.isLargest?(N.wholeValue=qt,N.decimalValue=0):(N.wholeValue=0,N.decimalValue=0)),xt&&!At&&(N.isSmallest?(N.wholeValue=We,N.decimalValue=0):(N.wholeValue=0,N.decimalValue=0)),N.isSmallest||N.significantDigits&&N.significantDigits-N.wholeValue.toString().length<=0?Te<0?N.value=Qt(N.wholeValue,Te):Te===0?N.value=Et(N.wholeValue+N.decimalValue):qe?(ye?N.value=Qt(N.rawValue,pt-N.wholeValue.toString().length):N.value=N.rawValue,N.wholeValue&&(pt-=N.wholeValue.toString().length)):(Pe.fractionDigits=Te,ye?N.value=N.wholeValue+Qt(N.decimalValue,Te):N.value=N.wholeValue+N.decimalValue):qe&&N.wholeValue?(N.value=Math.round(Qt(N.wholeValue,N.significantDigits-N.wholeValue.toString().length)),pt-=N.wholeValue.toString().length):N.value=N.wholeValue,N.tokenLength>1&&(Oe||Lt)&&(Pe.minimumIntegerDigits=N.tokenLength,At&&Pe.maximumSignificantDigits<N.tokenLength&&delete Pe.maximumSignificantDigits),!Lt&&(N.value>0||Vt===""||R(ce,N.type)||R(ne,N.type))&&(Lt=!0),N.formattedValue=h(N.value,Pe,ze),Pe.useGrouping=!1,Pe.decimalSeparator=".",N.formattedValueEn=h(N.value,Pe,"en"),N.tokenLength===2&&N.type==="milliseconds"&&(N.formattedValueMS=h(N.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),N};if(Ge=_(Ge,xn),Ge=k(Ge),Ge.length>1){var Un=function(N){return R(Ge,function(Be){return Be.type===N})},Hn=function(N){var Be=Un(N.type);Be&&M(N.targets,function(Pe){var Pt=Un(Pe.type);Pt&&parseInt(Be.formattedValueEn,10)===Pe.value&&(Be.rawValue=0,Be.wholeValue=0,Be.decimalValue=0,Pt.rawValue+=1,Pt.wholeValue+=1,Pt.decimalValue=0,Pt.formattedValueEn=Pt.wholeValue.toString(),At=!0)})};M(l,Hn)}return At&&(Lt=!1,pt=ft,Ge=_(Ge,xn),Ge=k(Ge)),ne&&!(Tt&&!P.trim)?(Ge=_(Ge,function(N){return R(ne,function(Be){return N.type===Be})?N:null}),Ge=k(Ge)):(Ot&&(Ge=C(Ge,function(N){return!N.isSmallest&&!N.wholeValue&&!R(ce,N.type)})),xe&&Ge.length&&(Ge=Ge.slice(0,xe)),ln&&Ge.length>1&&(Ge=I(Ge,function(N){return!N.wholeValue&&!R(ce,N.type)&&!N.isLargest})),rn&&(Ge=_(Ge,function(N,Be){return Be>0&&Be<Ge.length-1&&!N.wholeValue?null:N}),Ge=k(Ge)),hn&&Ge.length===1&&!Ge[0].wholeValue&&!(!ye&&Ge[0].isSmallest&&Ge[0].rawValue<We)&&(Ge=[])),Ee?Ge:(M(Jt,function(N){var Be=et[N.type],Pe=R(Ge,function(kt){return kt.type===N.type});if(!(!Be||!Pe)){var Pt=Pe.formattedValueEn.split(".");Pt[0]=parseInt(Pt[0],10),Pt[1]?Pt[1]=parseFloat("0."+Pt[1],10):Pt[1]=null;var wn=pe.durationPluralKey(Be,Pt[0],Pt[1]),ge=p(Be,pe),bt=!1,en={};M(pe._durationLabelTypes,function(kt){var Bt=R(ge,function(an){return an.type===kt.type&&an.key===wn});Bt&&(en[Bt.type]=Bt.label,u(N.text,kt.string)&&(N.text=N.text.replace(kt.string,Bt.label),bt=!0))}),tt&&!bt&&(ge.sort(m),M(ge,function(kt){if(en[kt.type]===kt.label)return u(N.text,kt.label)?!1:void 0;if(u(N.text,kt.label))return N.text=N.text.replace(kt.label,en[kt.type]),!1}))}}),Jt=_(Jt,function(N){if(!N.type)return N.text;var Be=R(Ge,function(Pt){return Pt.type===N.type});if(!Be)return"";var Pe="";return Ie&&(Pe+=N.text),(he&&Tt||!he&&xt)&&(Pe+="< ",Tt=!1,xt=!1),(he&&xt||!he&&Tt)&&(Pe+="> ",Tt=!1,xt=!1),he&&(Be.value>0||Vt===""||R(ce,Be.type)||R(ne,Be.type))&&(Pe+="-",he=!1),N.type==="milliseconds"&&Be.formattedValueMS?Pe+=Be.formattedValueMS:Pe+=Be.formattedValue,Ie||(Pe+=N.text),Pe}),Jt.join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function ae(){var H=this.duration,P=function(ke){return H._data[ke]},G=R(this.types,P),ue=O(this.types,P);switch(G){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(G===ue)return"d __";case"weeks":return G===ue?"w __":(this.trim===null&&(this.trim="both"),"w __, d __, h __");case"months":if(G===ue)return"M __";case"years":return G===ue?"y __":(this.trim===null&&(this.trim="both"),"y __, M __, d __");default:return this.trim===null&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function le(H){if(!H)throw"Moment Duration Format init cannot find moment instance.";H.duration.format=J,H.duration.fn.format=te,H.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:ae,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},H.updateLocale("en",b)}var fe=function(H,P,G){return H.toLocaleString(P,G)};r=X()&&Q(fe),i=r&&oe(fe);var re=function(H,P,G){if(typeof window<"u"&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(P,G).format(H)};return o=Q(re),s=o&&oe(re),le(n),le})}(Zo)),Zo.exports}r1();const i1=["true","t","yes","y","on","1"],o1=["false","f","no","n","off","0"];function It(e,t=""){return{kind:Z.Text,readonly:!0,allowOverlay:!0,data:e,displayData:e,errorDetails:t,isError:!0,style:"faded"}}function fi(e){return Object.hasOwn(e,"isError")&&e.isError}function a1(e){return Object.hasOwn(e,"tooltip")&&e.tooltip!==""}function wa(e){return Object.hasOwn(e,"isMissingValue")&&e.isMissingValue}function Es(e=!1){return e?{kind:Z.Loading,allowOverlay:!1,isMissingValue:!0,copyData:""}:{kind:Z.Loading,allowOverlay:!1,copyData:""}}function s1(e,t){const n=t?"faded":"normal";return{kind:Z.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function Is(e){return{id:e.id,title:e.title,hasMenu:!1,menuIcon:"dots",themeOverride:e.themeOverride,icon:e.icon,group:e.group,...e.isStretched&&!e.isPinned&&{grow:1},...e.width&&{width:e.width}}}function uo(e,t){return Ae(e)?t||{}:Ae(t)?e||{}:Dc(e,t)}function Hd(e){if(Ae(e))return[];if(typeof e=="number"||typeof e=="boolean")return[e];if(typeof e=="string"){if(e==="")return[];if(e.trim().startsWith("[")&&e.trim().endsWith("]"))try{return JSON.parse(e)}catch{return[e]}else return e.split(",")}try{const t=JSON.parse(JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r));return Array.isArray(t)?t.map(n=>["string","number","boolean","null"].includes(typeof n)?n:gt(n)):[gt(t)]}catch{return[gt(e)]}}function l1(e){return e&&e.startsWith("{")&&e.endsWith("}")}function gt(e){try{try{return t1(e)}catch{return JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r)}}catch{return`[${typeof e}]`}}function zd(e){if(Ae(e))return null;if(typeof e=="boolean")return e;const t=gt(e).toLowerCase().trim();if(t==="")return null;if(i1.includes(t))return!0;if(o1.includes(t))return!1}function ro(e){if(Ae(e))return null;if(Array.isArray(e))return NaN;if(typeof e=="string"){if(e.trim().length===0)return null;try{const t=qi.unformat(e.trim());if(st(t))return t}catch{}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function sa(e){if(Ae(e))return"";if(typeof e=="string")return e;try{return JSON.stringify(e,(t,n)=>typeof n=="bigint"?Number(n):n)}catch{return gt(e)}}function u1(e){if(e===0||Math.abs(e)>=1e-4)return 4;const n=e.toExponential().split("e");return Math.abs(parseInt(n[1],10))}function Hr(e,t={}){const n=navigator.languages;try{return new Intl.NumberFormat(n,t).format(e)}catch(r){if(r instanceof RangeError)return new Intl.NumberFormat(void 0,t).format(e);throw r}}function la(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":Ae(t)||t===""?st(n)?(n===0&&(e=Math.round(e)),qi(e).format({thousandSeparated:!1,mantissa:n,trimMantissa:!1})):qi(e).format({thousandSeparated:!1,mantissa:u1(e),trimMantissa:!0}):t==="plain"?qi(e).format({thousandSeparated:!1,mantissa:20,trimMantissa:!0}):t==="localized"?Hr(e,{minimumFractionDigits:n??void 0,maximumFractionDigits:n??void 0}):t==="percent"?Hr(e,{style:"percent",minimumFractionDigits:st(n)?Math.max(n-2,0):0,maximumFractionDigits:st(n)?Math.max(n-2,0):2}):t==="dollar"?Hr(e,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol",minimumFractionDigits:n??2,maximumFractionDigits:n??2}):t==="euro"?Hr(e,{style:"currency",currency:"EUR",minimumFractionDigits:n??2,maximumFractionDigits:n??2}):t==="yen"?Hr(e,{style:"currency",currency:"JPY",minimumFractionDigits:n??0,maximumFractionDigits:n??0}):["compact","scientific","engineering"].includes(t)?Hr(e,{notation:t}):t==="accounting"?qi(e).format({thousandSeparated:!0,negative:"parenthesis",mantissa:n??2,trimMantissa:!1}):t==="bytes"?Hr(e,{notation:"compact",style:"unit",unit:"byte",unitDisplay:"narrow",maximumFractionDigits:1}).replace("BB","GB"):hg.sprintf(t,e)}function Vu(e,t,n="datetime"){if(t==="localized"){const r=navigator.languages,i=n==="time"?void 0:"medium",o=n==="date"?void 0:"medium";try{return new Intl.DateTimeFormat(r,{dateStyle:i,timeStyle:o}).format(e.toDate())}catch(s){if(s instanceof RangeError)return new Intl.DateTimeFormat(void 0,{dateStyle:i,timeStyle:o}).format(e.toDate());throw s}}else{if(t==="distance")return e.fromNow();if(t==="calendar")return e.calendar();if(t==="iso8601")return n==="date"?e.format("YYYY-MM-DD"):n==="time"?e.format("HH:mm:ss.SSS[Z]"):e.toISOString()}return e.format(t)}function $o(e){if(Ae(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if(typeof e=="string"&&e.trim().length===0)return null;try{const t=Number(e);if(!isNaN(t)){let n=t;t>=10**18?n=t/1e3**3:t>=10**15?n=t/1e3**2:t>=10**12&&(n=t/1e3);const r=zr.unix(n).utc();if(r.isValid())return r.toDate()}if(typeof e=="string"){const n=zr.utc(e);if(n.isValid())return n.toDate();const r=zr.utc(e,[zr.HTML5_FMT.TIME_MS,zr.HTML5_FMT.TIME_SECONDS,zr.HTML5_FMT.TIME]);if(r.isValid())return r.toDate()}}catch{return}}function Vd(e){if(e%1===0)return 0;let t=e.toString();return t.indexOf("e")!==-1&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),t.indexOf(".")===-1?0:t.split(".")[1].length}function c1(e,t){if(!Number.isFinite(e))return e;if(t<=0)return Math.trunc(e);const n=10**t,r=e*n,i=Number.EPSILON*Math.abs(r)*10;return Math.trunc(r+Math.sign(r)*i)/n}const d1=new RegExp(/(\r\n|\n|\r)/gm);function io(e){return e.indexOf(`
`)!==-1?e.replace(d1," "):e}function f1(e,t){if(Ae(t))return"";try{const n=t.match(e);return n&&n[1]!==void 0?decodeURIComponent(n[1].replace(/\+/g,"%20")):t}catch{return t}}const h1=mi("div",{target:"e48kivf0"})(({theme:e})=>({overflowY:"auto",padding:e.spacing.sm,".react-json-view .copy-icon svg":{fontSize:"0.9em !important",marginRight:`${e.spacing.threeXS} !important`,verticalAlign:"middle !important"}})),Nd=({jsonValue:e,theme:t})=>{let n;if(e)try{n=typeof e=="string"?Ba.parse(e):Ba.parse(Ba.stringify(e))}catch{n=void 0}return Ae(n)?Qe(Wr,{highlight:!0,autoFocus:!1,disabled:!0,value:sa(e)??"",onChange:()=>{}}):Qe(h1,{"data-testid":"stJsonColumnViewer",children:Qe(bh,{src:n,collapsed:2,theme:wh(t.bgCell)>.5?"rjv-default":"monokai",displayDataTypes:!1,displayObjectSize:!1,name:!1,enableClipboard:!0,style:{fontFamily:t.fontFamily,fontSize:t.baseFontStyle,backgroundColor:t.bgCell,whiteSpace:"pre-wrap"}})})},g1=e=>{const t=e.theme,n=e.value.data;return Qe(Nd,{jsonValue:n.value||n.displayValue,theme:t})},m1=e=>{const t=e.theme,n=e.value;return Qe(Nd,{jsonValue:n.data,theme:t})},p1={kind:Z.Custom,isMatch:e=>e.data.kind==="json-cell",draw:(e,t)=>{const{value:n,displayValue:r}=t.data;return Ws(e,r??sa(n)??"",t.contentAlign),!0},measure:(e,t,n)=>{const{value:r,displayValue:i}=t.data,o=i??sa(r)??"";return(o?e.measureText(o).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:g1})},v1="line_chart",b1="area_chart",w1="bar_chart";function Zs(e,t,n){const r=uo({y_min:null,y_max:null},t.columnTypeOptions),i={kind:Z.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[r.y_min??0,r.y_max??1]}};return{...t,kind:e,sortMode:"default",isEditable:!1,getCell(o){if(Ae(o))return Es();const s=Hd(o),a=[];let l=[];if(s.length===0)return Es();let u=Number.MIN_SAFE_INTEGER,c=Number.MAX_SAFE_INTEGER;for(let h=0;h<s.length;h++){const m=ro(s[h]);if(Number.isNaN(m)||Ae(m))return It(gt(s),`The value cannot be interpreted as a numeric array. ${gt(m)} is not a number.`);m>u&&(u=m),m<c&&(c=m),a.push(m)}let f,g;if(s.length===1){let h;u<=0?h=u===0?1:0:h=u,f=r.y_max??h,g=r.y_min??(u>=0?0:u)}else f=r.y_max??u,g=r.y_min??c;return Ae(g)||Ae(f)||Number.isNaN(g)||Number.isNaN(f)||g>=f?It("Invalid min/max y-axis configuration",`The y_min (${g}) and y_max (${f}) configuration options must be valid numbers.`):(a.length>0&&(u>f||c<g)?l=a.map(h=>u-c===0?u>(f||1)?f:g:(f-g)*((h-c)/(u-c))+g):l=a,{...i,copyData:a.join(","),data:{...i.data,values:l,displayValues:a.map(h=>la(h)),yAxis:[g,f]},isMissingValue:Ae(o)})},getCellValue(o){return o.kind===Z.Loading||o.data?.values===void 0?null:o.data?.values}}}function $d(e){return Zs(v1,e,"line")}$d.isEditableType=!1;function Bd(e){return Zs(w1,e,"bar")}Bd.isEditableType=!1;function Wd(e){return Zs(b1,e,"area")}Wd.isEditableType=!1;function Js(e){const t={kind:Z.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:"normal"};return{...e,kind:"checkbox",sortMode:"default",getCell(n){let r=null;return r=zd(n),r===void 0?It(gt(n),"The value cannot be interpreted as boolean."):{...t,data:r,isMissingValue:Ae(r)}},getCellValue(n){return n.data===void 0?null:n.data}}}Js.isEditableType=!0;function Nu(e,t){return t.startsWith("+")||t.startsWith("-")?e=e.utcOffset(t,!1):e=e.tz(t),e}function Qs(e,t,n,r,i,o,s){const a=uo({format:n,step:r,timezone:s},t.columnTypeOptions);let l;if(st(a.timezone))try{l=Nu(Il(),a.timezone)?.utcOffset()||void 0}catch{}let u;st(a.min_value)&&(u=$o(a.min_value)||void 0);let c;st(a.max_value)&&(c=$o(a.max_value)||void 0);const f={kind:Z.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isPinned?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:a.step?.toString()||"1",format:i,min:u,max:c}},g=h=>{const m=$o(h);return m===null?!t.isRequired:!(m===void 0||st(u)&&o(m)<o(u)||st(c)&&o(m)>o(c))};return{...t,kind:e,sortMode:"default",validateInput:g,getCell(h,m){if(m===!0){const S=g(h);if(S===!1)return It(gt(h),"Invalid input.");S instanceof Date&&(h=S)}const p=$o(h);let w="",b="",v=l;if(p===void 0)return It(gt(h),"The value cannot be interpreted as a datetime object.");if(p!==null){let S=Il.utc(p);if(!S.isValid())return It(gt(p),`Invalid moment date. This should never happen. Please report this bug. 
Error: ${S.toString()}`);if(a.timezone){try{S=Nu(S,a.timezone)}catch(O){return It(S.toISOString(),`Failed to adjust to the provided timezone: ${a.timezone}. 
Error: ${O}`)}v=S.utcOffset()}try{b=Vu(S,a.format||n,e)}catch(O){return It(S.toISOString(),`Failed to format the date for rendering with: ${a.format}. 
Error: ${O}`)}w=Vu(S,n,e)}return{...f,copyData:w,isMissingValue:Ae(p),data:{...f.data,date:p,displayDate:b,timezoneOffset:v}}},getCellValue(h){return Ae(h?.data?.date)?null:o(h.data.date)}}}function el(e){let t="YYYY-MM-DD HH:mm:ss";e.columnTypeOptions?.step>=60?t="YYYY-MM-DD HH:mm":e.columnTypeOptions?.step<1&&(t="YYYY-MM-DD HH:mm:ss.SSS");const n=yh(e.arrowType),r=st(n)||st(e?.columnTypeOptions?.timezone);return Qs("datetime",e,r?t+"Z":t,1,"datetime-local",i=>r?i.toISOString():i.toISOString().replace("Z",""),n)}el.isEditableType=!0;function tl(e){let t="HH:mm:ss";return e.columnTypeOptions?.step>=60?t="HH:mm":e.columnTypeOptions?.step<1&&(t="HH:mm:ss.SSS"),Qs("time",e,t,1,"time",n=>n.toISOString().split("T")[1].replace("Z",""))}tl.isEditableType=!0;function nl(e){return Qs("date",e,"YYYY-MM-DD",1,"date",t=>t.toISOString().split("T")[0])}nl.isEditableType=!0;function Ud(e){const t={kind:Z.Image,data:[],displayData:[],readonly:!0,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:"normal"};return{...e,kind:"image",sortMode:"default",isEditable:!1,getCell(n){const r=st(n)?[gt(n)]:[];return{...t,data:r,isMissingValue:!st(n),displayData:r}},getCellValue(n){return n.data===void 0||n.data.length===0?null:n.data[0]}}}Ud.isEditableType=!1;function qd(e){const t={kind:Z.Custom,allowOverlay:!0,contentAlign:e.contentAlignment,readonly:!0,style:e.isPinned?"faded":"normal",copyData:"",data:{kind:"json-cell",value:""}};return{...e,kind:"json",sortMode:"default",isEditable:!1,getCell(n){try{const r=st(n)?io(sa(n)):"";return{...t,copyData:r,isMissingValue:Ae(n),data:{...t.data,value:n,displayValue:r}}}catch(r){return It(gt(n),`The value cannot be interpreted as a JSON string. Error: ${r}`)}},getCellValue(n){return n.data?.value??null}}}qd.isEditableType=!1;function Gd(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(l){n=`Invalid validate regex: ${t.validate}.
Error: ${l}`}let r=!1,i=t.display_text,o;if(!Ae(i)){if(i.startsWith(":material/")&&Ch(i))i=Sh(i).icon,r=!0;else if(i.includes("(")&&i.includes(")"))try{o=new RegExp(i,"us")}catch{o=void 0}}const s={kind:Z.Uri,readonly:!e.isEditable,allowOverlay:!r,contentAlign:e.contentAlignment??(r?"center":void 0),style:"normal",hoverEffect:!0,data:"",displayData:"",copyData:"",...r&&{themeOverride:{fontFamily:xh.iconFont,linkColor:void 0}}},a=l=>{if(Ae(l))return!e.isRequired;const u=gt(l);return!(t.max_chars&&u.length>t.max_chars||n instanceof RegExp&&n.test(u)===!1)};return{...e,kind:"link",sortMode:"default",validateInput:a,getCell(l,u){if(Ae(l))return{...s,data:null,isMissingValue:!0,onClickUri:()=>{},themeOverride:void 0};const c=l;if(typeof n=="string")return It(gt(c),n);if(u&&a(c)===!1)return It(gt(c),"Invalid input.");let f="";return c&&(o!==void 0?f=f1(o,c):f=i||c),{...s,data:c,displayData:f,isMissingValue:Ae(c),onClickUri:g=>{window.open(c.startsWith("www.")?`https://${c}`:c,"_blank","noopener,noreferrer"),g.preventDefault()},copyData:c}},getCellValue(l){return Ae(l.data)?null:l.data}}}Gd.isEditableType=!0;function rl(e){const t={kind:Z.Bubble,data:[],allowOverlay:!0,contentAlign:e.contentAlignment,style:"normal"};return{...e,kind:"list",sortMode:"default",isEditable:!1,getCell(n){const r=Ae(n)?[]:Hd(n);return{...t,data:r,isMissingValue:Ae(n),copyData:Ae(n)?"":gt(r.map(i=>typeof i=="string"&&i.includes(",")?i.replace(/,/g," "):i))}},getCellValue(n){return Ae(n.data)||wa(n)?null:n.data}}}rl.isEditableType=!1;function il(e){const t=uo({step:Oc(e.arrowType)?1:void 0,min_value:kh(e.arrowType)?0:void 0},e.columnTypeOptions),n=!t.format&&(Mh(e.arrowType)||Rh(e.arrowType)),r=Ae(t.min_value)||t.min_value<0,i=st(t.step)&&!Number.isNaN(t.step)?Vd(t.step):void 0,o={kind:Z.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||n?"left":"right",style:e.isPinned?"faded":"normal",allowNegative:r,fixedDecimals:i,thousandSeparator:""},s=a=>{let l=ro(a);if(Ae(l))return!e.isRequired;if(Number.isNaN(l))return!1;let u=!1;return st(t.max_value)&&l>t.max_value&&(l=t.max_value,u=!0),st(t.min_value)&&l<t.min_value?!1:u?l:!0};return{...e,kind:"number",sortMode:"smart",validateInput:s,getCell(a,l){if(l===!0){const f=s(a);if(f===!1)return It(gt(a),"Invalid input.");typeof f=="number"&&(a=f)}let u=ro(a),c="";if(st(u)){if(Number.isNaN(u))return It(gt(a),"The value cannot be interpreted as a number.");if(st(i)&&(u=c1(u,i)),Number.isInteger(u)&&!Number.isSafeInteger(u))return It(gt(a),"The value is larger than the maximum supported integer values in number columns (2^53).");try{n?c=bs(u,e.arrowType):c=la(u,t.format,i)}catch(f){return It(gt(u),st(t.format)?`Failed to format the number based on the provided format configuration: (${t.format}). Error: ${f}`:`Failed to format the number. Error: ${f}`)}}return{...o,data:u,displayData:c,isMissingValue:Ae(u),copyData:Ae(u)?"":gt(u)}},getCellValue(a){return a.data===void 0?null:a.data}}}il.isEditableType=!0;function oo(e){const t={kind:Z.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!0,style:e.isPinned?"faded":"normal"};return{...e,kind:"object",sortMode:"default",isEditable:!1,getCell(n){try{const r=st(n)?gt(n):null,i=st(r)?io(r):"";return{...t,data:r,displayData:i,isMissingValue:Ae(n)}}catch(r){return It(gt(n),`The value cannot be interpreted as a string. Error: ${r}`)}},getCellValue(n){return n.data===void 0?null:n.data}}}oo.isEditableType=!1;function Yd(e){const t=Oc(e.arrowType),n=uo({min_value:0,max_value:t?100:1,format:t?"%3d%%":"percent",step:t?1:void 0},e.columnTypeOptions),r=Ae(n.step)||Number.isNaN(n.step)?void 0:Vd(n.step);let i;try{i=la(n.max_value,n.format,r)}catch{i=gt(n.max_value)}const o={kind:Z.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,readonly:!0,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step??.01,value:n.min_value,label:String(n.min_value),measureLabel:i}};return{...e,kind:"progress",sortMode:"smart",isEditable:!1,getCell(s){if(Ae(s))return Es();if(Ae(n.min_value)||Ae(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return It("Invalid min/max parameters",`The min_value (${n.min_value}) and max_value (${n.max_value}) parameters must be valid numbers.`);if(st(n.step)&&Number.isNaN(n.step))return It("Invalid step parameter",`The step parameter (${n.step}) must be a valid number.`);const a=ro(s);if(Number.isNaN(a)||Ae(a))return It(gt(s),"The value cannot be interpreted as a number.");if(Number.isInteger(a)&&!Number.isSafeInteger(a))return It(gt(s),"The value is larger than the maximum supported integer values in number columns (2^53).");let l="";try{l=la(a,n.format,r)}catch(c){return It(gt(a),st(n.format)?`Failed to format the number based on the provided format configuration: (${n.format}). Error: ${c}`:`Failed to format the number. Error: ${c}`)}const u=Math.min(n.max_value,Math.max(n.min_value,a));return{...o,isMissingValue:Ae(s),copyData:String(a),data:{...o.data,value:u,label:l,measureLabel:l.length>i.length?l:i}}},getCellValue(s){return s.kind===Z.Loading||s.data?.value===void 0?null:s.data?.value}}}Yd.isEditableType=!1;function ol(e){let t="string";const n=uo({options:Pc(e.arrowType)?[!0,!1]:e.arrowType.categoricalOptions??[]},e.columnTypeOptions),r=new Set(n.options.map(o=>typeof o));r.size===1&&(r.has("number")||r.has("bigint")?t="number":r.has("boolean")&&(t="boolean"));const i={kind:Z.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isPinned?"faded":"normal",data:{kind:"dropdown-cell",allowedValues:[...e.isRequired!==!0?[null]:[],...n.options.filter(o=>o!==null&&o!=="").map(o=>gt(o))],value:""}};return{...e,kind:"selectbox",sortMode:"default",getCell(o,s){let a=null;return st(o)&&o!==""&&(a=gt(o)),s&&!i.data.allowedValues.includes(a)?It(gt(a),"The value is not part of the allowed options."):{...i,isMissingValue:a===null,copyData:a||"",data:{...i.data,value:a}}},getCellValue(o){return Ae(o.data?.value)||o.data?.value===""?null:t==="number"?ro(o.data?.value)??null:t==="boolean"?zd(o.data?.value)??null:o.data?.value}}}ol.isEditableType=!0;function al(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(o){n=`Invalid validate regex: ${t.validate}.
Error: ${o}`}const r={kind:Z.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!e.isEditable,style:e.isPinned?"faded":"normal"},i=o=>{if(Ae(o))return!e.isRequired;let s=gt(o),a=!1;return t.max_chars&&s.length>t.max_chars&&(s=s.slice(0,t.max_chars),a=!0),n instanceof RegExp&&n.test(s)===!1?!1:a?s:!0};return{...e,kind:"text",sortMode:"default",validateInput:i,getCell(o,s){if(typeof n=="string")return It(gt(o),n);if(s){const a=i(o);if(a===!1)return It(gt(o),"Invalid input.");typeof a=="string"&&(o=a)}try{const a=st(o)?gt(o):null,l=st(a)?io(a):"";return{...r,isMissingValue:Ae(a),data:a,displayData:l}}catch(a){return It("Incompatible value",`The value cannot be interpreted as string. Error: ${a}`)}},getCellValue(o){return o.data===void 0?null:o.data}}}al.isEditableType=!0;const $u=mi("img",{target:"e17fx5ar0"})({maxWidth:"100%",maxHeight:"37.5rem",objectFit:"scale-down"}),y1=({urls:e})=>{const t=e&&e.length>0?e[0]:"";return t.startsWith("http")?Qe("a",{href:t,target:"_blank",rel:"noreferrer noopener",children:Qe($u,{src:t})}):Qe($u,{src:t})},Bu=new Map(Object.entries({object:oo,text:al,checkbox:Js,selectbox:ol,list:rl,number:il,link:Gd,datetime:el,date:nl,time:tl,line_chart:$d,bar_chart:Bd,area_chart:Wd,image:Ud,progress:Yd,json:qd})),C1=[p1];var S1=_c();const x1=ar(S1);var us,Wu;function k1(){if(Wu)return us;Wu=1;var e=Eh(),t=Ih(),n=Th(),r=_c(),i=Dh(),o=Oh(),s=Ph(),a=_h(),l="[object Map]",u="[object Set]",c=Object.prototype,f=c.hasOwnProperty;function g(h){if(h==null)return!0;if(i(h)&&(r(h)||typeof h=="string"||typeof h.splice=="function"||o(h)||a(h)||n(h)))return!h.length;var m=t(h);if(m==l||m==u)return!h.size;if(s(h))return!e(h).length;for(var p in h)if(f.call(h,p))return!1;return!0}return us=g,us}var M1=k1();const R1=ar(M1);function cs(e,t,n){if(!n.includes(t))return;const r=new RegExp(`${e}[,\\s].*{(?:[^}]*[\\s;]{1})?${t}:\\s*([^;}]+)[;]?.*}`,"gm");n=n.replace(/{/g," {");const i=r.exec(n);if(i)return i[1].trim()}function E1(e,t,n){const r={};if(!n.includes(t))return e;const i=cs(t,"color",n);i&&(r.textDark=i,e.kind===Z.Bubble&&(r.textBubble=i),e.kind===Z.Uri&&(r.linkColor=i));const o=cs(t,"background-color",n);o&&(r.bgCell=o),o==="yellow"&&i===void 0&&(r.textDark="#31333F");const s=cs(t,"font-weight",n);return s&&(r.baseFontStyle=`${s} ${Xh.sm}`),r?{...e,themeOverride:r}:e}function I1(e){return Vh(e)||Nh(e)?al:$h(e)?el:Fc(e)?tl:Bh(e)?nl:Wh(e)||Uh(e)?oo:Pc(e)?Js:qh(e)?il:Gh(e)?ol:Yh(e)?rl:oo}function Xd(e){const t=e.length>0?e[e.length-1]:"",n=e.length>1?e.slice(0,-1).filter(r=>r!=="").join(" / "):void 0;return{title:t,group:n}}function sl(e){return{group:void 0,isEditable:!1,isIndex:!1,isPinned:!1,isHidden:!1,isStretched:!1,...e}}function T1(e,t){const n=e.columnNames.map(a=>a[t]),{title:r,group:i}=Xd(n),o=e.columnTypes[t];let s=!0;return zh(o)&&(s=!1),sl({id:`_index-${t}`,indexNumber:t,name:r,title:r,group:i,isEditable:s,arrowType:o,isIndex:!0,isPinned:!0})}function D1(e,t){const n=e.columnNames.map(s=>s[t]),{title:r,group:i}=Xd(n),o=e.columnTypes[t];return sl({id:`_column-${r}-${t}`,indexNumber:t,name:r,isEditable:!0,title:r,arrowType:o,group:i})}function jd(){return sl({id:"_empty-index",indexNumber:0,title:"",name:"",isEditable:!1,isIndex:!0,isPinned:!0,arrowType:{type:jh.INDEX,arrowField:new Fh("",new Lh,!0),pandasType:void 0}})}function Uu(e){const t=[],{dimensions:n}=e,r=n.numIndexColumns,i=n.numDataColumns;if(r===0&&i===0)return t.push(jd()),t;for(let o=0;o<r;o++)t.push(T1(e,o));for(let o=0;o<i;o++)t.push(D1(e,o+r));return t}function O1(e,t,n,r=void 0){let i;if(e.kind==="object"||e.kind==="json")i=e.getCell(st(t.content)?io(bs(t.content,t.contentType)):null);else if(["time","date","datetime"].includes(e.kind)&&st(t.content)&&(typeof t.content=="number"||typeof t.content=="bigint")){let o;Fc(t.contentType)&&st(t.field?.type?.unit)?o=Ah(t.content,t.field):o=zr.utc(Number(t.content)).toDate(),i=e.getCell(o)}else if(Hh(t.contentType)){const o=Ae(t.content)?null:bs(t.content,t.contentType);i=e.getCell(o)}else i=e.getCell(t.content);if(fi(i))return i;if(!e.isEditable){if(n&&st(n?.displayContent)){const o=io(n.displayContent);i.kind===Z.Text?i={...i,displayData:o}:i.kind===Z.Number&&Ae(e.columnTypeOptions?.format)?i={...i,displayData:o}:i.kind===Z.Uri&&Ae(e.columnTypeOptions?.display_text)?i={...i,displayData:o}:i.kind===Z.Custom&&i.data?.kind==="date-picker-cell"&&Ae(e.columnTypeOptions?.format)&&(i={...i,data:{...i.data,displayDate:o}})}r&&n?.cssId&&(i=E1(i,n.cssId,r))}return i}const Jo="_index",qu="_pos:",Gu={small:75,medium:200,large:400},Kd=Fs.getLogger("useColumnLoader");function P1(e){if(!Ae(e)){if(typeof e=="number")return e;if(e in Gu)return Gu[e]}}const Bo=(e,t)=>fg(e,t,(r,i)=>{if(x1(i))return i});function Yu(e,t){if(!t)return e;let n={};return e.isIndex&&t.has(Jo)&&(n=Bo(n,t.get(Jo)??{})),t.has(`${qu}${e.indexNumber}`)&&(n=Bo(n,t.get(`${qu}${e.indexNumber}`)??{})),t.has(e.name)&&e.name!==Jo&&(n=Bo(n,t.get(e.name)??{})),t.has(e.id)&&(n=Bo(n,t.get(e.id)??{})),R1(n)?e:Dc({...e},{title:n.label,width:P1(n.width),isEditable:st(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isPinned:n.pinned,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help})}function _1(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return Kd.error(t),new Map}}function Xu(e){const t=e.columnTypeOptions?.type;let n;return st(t)&&(Bu.has(t)?n=Bu.get(t):Kd.warn(`Unknown column type configured in column configuration: ${t}`)),Ae(n)&&(n=I1(e.arrowType)),n}function F1(e,t,n,r){const i=$r(),o=d.useMemo(()=>_1(e.columns),[e.columns]),[s,a]=d.useState(o);d.useEffect(()=>{a(o)},[o]);const l=e.useContainerWidth||st(e.width)&&e.width>0,u=st(e.rowHeight)&&e.rowHeight>cn("4rem"),c=d.useMemo(()=>Uu(t).map(g=>{let h={...g,...Yu(g,s),isStretched:l};const m=Xu(h);return(e.editingMode===Cn.EditingMode.READ_ONLY||n||m.isEditableType===!1)&&(h={...h,isEditable:!1}),e.editingMode!==Cn.EditingMode.READ_ONLY&&h.isEditable==!0&&(h={...h,icon:"editable"},h.isRequired&&e.editingMode===Cn.EditingMode.DYNAMIC&&(h={...h,isHidden:!1})),m(h,i)}),[t,s,l,e.editingMode,n,i]);return{columns:d.useMemo(()=>{const g=Uu(t).map(w=>{let b={...w,...Yu(w,s),isStretched:l,isWrappingAllowed:u};const v=Xu(b);return(e.editingMode===Cn.EditingMode.READ_ONLY||n||v.isEditableType===!1)&&(b={...b,isEditable:!1}),e.editingMode!==Cn.EditingMode.READ_ONLY&&b.isEditable==!0&&(b={...b,icon:"editable"},b.isRequired&&e.editingMode===Cn.EditingMode.DYNAMIC&&(b={...b,isHidden:!1})),v(b,i)}).filter(w=>!w.isHidden),h=[],m=[];r?.length?(g.forEach(w=>{w.isIndex&&!r.includes(w.name)&&!r.includes(w.id)&&w.isPinned!==!1&&h.push(w)}),r.forEach(w=>{const b=g.find(v=>v.name===w||v.id===w);b&&(b.isPinned?h.push(b):m.push(b))})):g.forEach(w=>{w.isPinned?h.push(w):m.push(w)});const p=[...h,...m];return p.length>0?p:[oo(jd())]},[t,s,u,l,n,e.editingMode,r,i]),allColumns:c,setColumnConfigMapping:a}}function Qi(e){return e.isIndex?Jo:Ae(e.name)?"":e.name}class Wo{constructor(t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=t}toJson(t){const n=new Map;t.forEach(o=>{n.set(o.indexNumber,o)});const r={edited_rows:{},added_rows:[],deleted_rows:[]};return this.editedCells.forEach((o,s,a)=>{const l={};o.forEach((u,c)=>{const f=n.get(c);f&&(l[Qi(f)]=f.getCellValue(u))}),r.edited_rows[s]=l}),this.addedRows.forEach(o=>{const s={};let a=!1;o.forEach((l,u,c)=>{const f=n.get(u);if(f){const g=f.getCellValue(l);f.isRequired&&f.isEditable&&wa(l)&&(a=!0),st(g)&&(s[Qi(f)]=g)}}),a||r.added_rows.push(s)}),r.deleted_rows=this.deletedRows,JSON.stringify(r,(o,s)=>s===void 0?null:s)}fromJson(t,n){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const r=JSON.parse(t),i=new Map;n.forEach(s=>{i.set(s.indexNumber,s)});const o=new Map;n.forEach(s=>{o.set(Qi(s),s)}),Object.keys(r.edited_rows).forEach(s=>{const a=Number(s),l=r.edited_rows[s];Object.keys(l).forEach(u=>{const c=l[u],f=o.get(u);if(f){const g=f.getCell(c);g&&(this.editedCells.has(a)||this.editedCells.set(a,new Map),this.editedCells.get(a)?.set(f.indexNumber,g))}})}),r.added_rows.forEach(s=>{const a=new Map;n.forEach(l=>{a.set(l.indexNumber,l.getCell(null))}),Object.keys(s).forEach(l=>{const u=s[l],c=o.get(l);if(c){const f=c.getCell(u);f&&a.set(c.indexNumber,f)}}),this.addedRows.push(a)}),this.deletedRows=r.deleted_rows}isAddedRow(t){return t>=this.numRows}getCell(t,n){if(this.isAddedRow(n))return this.addedRows[n-this.numRows].get(t);const r=this.editedCells.get(n);if(r!==void 0)return r.get(t)}setCell(t,n,r){if(this.isAddedRow(n)){if(n-this.numRows>=this.addedRows.length)return;this.addedRows[n-this.numRows].set(t,r)}else this.editedCells.get(n)===void 0&&this.editedCells.set(n,new Map),this.editedCells.get(n).set(t,r)}addRow(t){this.addedRows.push(t)}deleteRows(t){t.sort((n,r)=>r-n).forEach(n=>{this.deleteRow(n)})}deleteRow(t){if(!(Ae(t)||t<0)){if(this.isAddedRow(t)){this.addedRows.splice(t-this.numRows,1);return}this.deletedRows.includes(t)||(this.deletedRows.push(t),this.deletedRows=this.deletedRows.sort((n,r)=>n-r)),this.editedCells.delete(t)}}getOriginalRowIndex(t){let n=t;for(let r=0;r<this.deletedRows.length&&!(this.deletedRows[r]>n);r++)n+=1;return n}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}}const ao=({columnId:e,columnConfigMapping:t,updatedProps:n})=>{const r=new Map(t),i=r.get(e),o={...i||{},...n||{}};return(i?.type_config||n?.type_config)&&(o.type_config={...i?.type_config||{},...n?.type_config||{}}),r.set(e,o),r};function L1(e){return{changeColumnFormat:d.useCallback((n,r)=>{e(i=>ao({columnId:n,columnConfigMapping:i,updatedProps:{type_config:{format:r}}}))},[e])}}function A1(e,t,n,r,i,o){const s=d.useMemo(()=>e.filter(c=>c.isPinned).reduce((c,f)=>c+(f.width??r*2),0)>n*.6,[e,n,r]),a=t||s?0:e.filter(c=>c.isPinned).length,l=d.useCallback(c=>{o(f=>ao({columnId:c,columnConfigMapping:f,updatedProps:{pinned:!1}})),i(!0,!1)},[i,o]);return{pinColumn:d.useCallback(c=>{o(f=>ao({columnId:c,columnConfigMapping:f,updatedProps:{pinned:!0}})),i(!0,!1)},[i,o]),unpinColumn:l,freezeColumns:a}}function H1(e,t,n,r,i){return{onColumnMoved:d.useCallback((s,a)=>{const l=[...e],[u]=l.splice(s,1);l.splice(a,0,u),a<t&&!u.isPinned?n(u.id):a>=t&&u.isPinned&&r(u.id),i(l.map(c=>c.id))},[e,t,n,r,i])}}function z1(e){const[t,n]=d.useState(()=>new Map),r=d.useCallback((o,s,a,l)=>{o.id&&n(new Map(t).set(o.id,l))},[t]);return{columns:d.useMemo(()=>e.map(o=>o.id&&t.has(o.id)&&t.get(o.id)!==void 0?{...o,width:t.get(o.id),grow:0}:o),[e,t]),onColumnResize:r}}function V1(e){switch(e.kind){case Z.Number:return e.data?.toString()??"";case Z.Boolean:return e.data?.toString()??"";case Z.Markdown:case Z.RowID:case Z.Text:case Z.Uri:return e.data??"";case Z.Bubble:case Z.Image:return e.data.join("");case Z.Drilldown:return e.data.map(t=>t.text).join("");case Z.Protected:case Z.Loading:return"";case Z.Custom:return e.copyData}}function ju(e){if(typeof e=="number")return e;if(e.length>0){const t=Number(e);isNaN(t)||(e=t)}return e}function N1(e,t){return e=ju(e),t=ju(t),typeof e=="string"&&typeof t=="string"?e.localeCompare(t):typeof e=="number"&&typeof t=="number"?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}function $1(e,t){return e>t?1:e===t?0:-1}function B1(e){const{sort:t,rows:n,getCellContent:r}=e,i=d.useMemo(()=>t===void 0?[]:Array.isArray(t)?t:[t],[t]),o=d.useMemo(()=>i.map(u=>{const c=e.columns.findIndex(f=>u.column===f||f.id!==void 0&&u.column.id===f.id);return c===-1?void 0:c}),[i,e.columns]),s=d.useMemo(()=>{const u=i.map((f,g)=>({sort:f,col:o[g]})).filter(f=>f.col!==void 0);if(u.length===0)return;const c=u.map(()=>new Array(n));for(let f=0;f<u.length;f++){const{col:g}=u[f],h=[g,0];for(let m=0;m<n;m++)h[1]=m,c[f][m]=V1(r(h))}return Cr(n).sort((f,g)=>{for(let h=0;h<u.length;h++){const{sort:m}=u[h],p=c[h][f],w=c[h][g];let b;if(m.mode==="raw"?b=$1(p,w):m.mode==="smart"?b=N1(p,w):b=p.localeCompare(w),b!==0)return(m.direction??"asc")==="desc"&&(b=-b),b}return 0})},[r,n,i,o]),a=d.useCallback(u=>s===void 0?u:s[u],[s]),l=d.useCallback(([u,c])=>s===void 0?r([u,c]):(c=s[c],r([u,c])),[r,s]);return s===void 0?{getCellContent:e.getCellContent,getOriginalIndex:a}:{getOriginalIndex:a,getCellContent:l}}function W1(e,t){return t===void 0?e:e.map(n=>n.id===t.column.id?{...n,title:t.direction==="asc"?`↑ ${n.title}`:`↓ ${n.title}`}:n)}function U1(e,t,n){const[r,i]=d.useState(),{getCellContent:o,getOriginalIndex:s}=B1({columns:t.map(u=>Is(u)),getCellContent:n,rows:e,sort:r}),a=d.useMemo(()=>W1(t,r),[t,r]),l=d.useCallback((u,c,f)=>{const g=a[u];let h;c==="auto"?(h="asc",r&&r.column.id===g.id&&(r.direction==="asc"?h="desc":h=void 0)):h=c,h===void 0||f&&h===r?.direction?i(void 0):i({column:Is(g),direction:h,mode:g.sortMode})},[r,a]);return{columns:a,sortColumn:l,getOriginalIndex:s,getCellContent:o}}function q1(e,t){const n=d.useCallback(i=>{t(o=>ao({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!0}})),e(!0,!1)},[e,t]),r=d.useCallback(i=>{t(o=>ao({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!1}})),e(!0,!1)},[e,t]);return{hideColumn:n,showColumn:r}}function G1(){return{provideEditor:d.useCallback(t=>{if(t.kind===Z.Text&&t.readonly&&l1(t.data))return{editor:m1}},[])}}const Y1={kind:Z.Custom,isMatch:e=>e.data.kind==="sparkline-cell",needsHover:!0,needsHoverPosition:!0,draw:(e,t)=>{const{ctx:n,theme:r,rect:i,hoverAmount:o,hoverX:s}=e;let{values:a,yAxis:l,color:u,graphKind:c="area",displayValues:f,hideAxis:g}=t.data;const[h,m]=l;if(a.length===0)return!0;a=a.map(M=>Math.min(1,Math.max(0,(M-h)/(m-h))));const p=r.cellHorizontalPadding,w=p+i.x,b=i.y+3,v=i.height-6,S=i.width-p*2,O=m-h,R=m<=0?b:h>=0?b+v:b+v*(m/O);if(!g&&h<=0&&m>=0&&(n.beginPath(),n.moveTo(w,R),n.lineTo(w+S,R),n.globalAlpha=.4,n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.globalAlpha=1),c==="bar"){n.beginPath();const M=2,_=(a.length-1)*M,E=(S-_)/a.length;let k=w;for(const F of a){const D=b+v-F*v;n.moveTo(k,R),n.lineTo(k+E,R),n.lineTo(k+E,D),n.lineTo(k,D),k+=E+M}n.fillStyle=t.data.color??r.accentColor,n.fill()}else{a.length===1&&(a=[a[0],a[0]],f&&(f=[f[0],f[0]])),n.beginPath();const M=(i.width-16)/(a.length-1),_=a.map((k,F)=>({x:w+M*F,y:b+v-k*v}));n.moveTo(_[0].x,_[0].y);let E=0;if(_.length>2)for(E=1;E<_.length-2;E++){const k=(_[E].x+_[E+1].x)/2,F=(_[E].y+_[E+1].y)/2;n.quadraticCurveTo(_[E].x,_[E].y,k,F)}if(n.quadraticCurveTo(_[E].x,_[E].y,_[E+1].x,_[E+1].y),n.strokeStyle=u??r.accentColor,n.lineWidth=1+o*.5,n.stroke(),n.lineTo(i.x+i.width-p,R),n.lineTo(i.x+p,R),n.closePath(),c==="area"){n.globalAlpha=.2+.2*o;const k=n.createLinearGradient(0,b,0,b+v*1.4);k.addColorStop(0,u??r.accentColor);const[F,D,C]=ra(u??r.accentColor);k.addColorStop(1,`rgba(${F}, ${D}, ${C}, 0)`),n.fillStyle=k,n.fill(),n.globalAlpha=1}if(s!==void 0&&(c==="line"||c==="area")&&f!==void 0){n.beginPath();const k=Math.min(a.length-1,Math.max(0,Math.round((s-p)/M)));n.moveTo(w+k*M,i.y+1),n.lineTo(w+k*M,i.y+i.height),n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.save(),n.font=`8px ${r.fontFamily}`,n.fillStyle=r.textMedium,n.textBaseline="top",n.fillText(f[k],w,i.y+r.cellVerticalPadding),n.restore()}}return!0},provideEditor:()=>{},onPaste:(e,t)=>t};function Ku(e,t,n,r,i,o){if(!(r<=0||i<=0)){if(typeof o=="number"&&o<=0){e.rect(t,n,r,i);return}typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.min(o.tl,i/2,r/2),tr:Math.min(o.tr,i/2,r/2),bl:Math.min(o.bl,i/2,r/2),br:Math.min(o.br,i/2,r/2)},o.tl=Math.max(0,o.tl),o.tr=Math.max(0,o.tr),o.br=Math.max(0,o.br),o.bl=Math.max(0,o.bl),e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}}function Zu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Le(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Zu(Object(n),!0).forEach(function(r){Gi(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zu(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var X1=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function j1(e){var t=e.defaultInputValue,n=t===void 0?"":t,r=e.defaultMenuIsOpen,i=r===void 0?!1:r,o=e.defaultValue,s=o===void 0?null:o,a=e.inputValue,l=e.menuIsOpen,u=e.onChange,c=e.onInputChange,f=e.onMenuClose,g=e.onMenuOpen,h=e.value,m=sr(e,X1),p=d.useState(a!==void 0?a:n),w=rr(p,2),b=w[0],v=w[1],S=d.useState(l!==void 0?l:i),O=rr(S,2),R=O[0],M=O[1],_=d.useState(h!==void 0?h:s),E=rr(_,2),k=E[0],F=E[1],D=d.useCallback(function(X,oe){typeof u=="function"&&u(X,oe),F(X)},[u]),C=d.useCallback(function(X,oe){var Q;typeof c=="function"&&(Q=c(X,oe)),v(Q!==void 0?Q:X)},[c]),I=d.useCallback(function(){typeof g=="function"&&g(),M(!0)},[g]),T=d.useCallback(function(){typeof f=="function"&&f(),M(!1)},[f]),x=a!==void 0?a:b,$=l!==void 0?l:R,q=h!==void 0?h:k;return Le(Le({},m),{},{inputValue:x,menuIsOpen:$,onChange:D,onInputChange:C,onMenuClose:T,onMenuOpen:I,value:q})}function K1(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}const Z1=Math.min,J1=Math.max,ua=Math.round,Uo=Math.floor,ca=e=>({x:e,y:e});function Q1(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function ya(){return typeof window<"u"}function Zd(e){return Qd(e)?(e.nodeName||"").toLowerCase():"#document"}function or(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Jd(e){var t;return(t=(Qd(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Qd(e){return ya()?e instanceof Node||e instanceof or(e).Node:!1}function eb(e){return ya()?e instanceof Element||e instanceof or(e).Element:!1}function ll(e){return ya()?e instanceof HTMLElement||e instanceof or(e).HTMLElement:!1}function Ju(e){return!ya()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof or(e).ShadowRoot}function ef(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=ul(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function tb(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function nb(e){return["html","body","#document"].includes(Zd(e))}function ul(e){return or(e).getComputedStyle(e)}function rb(e){if(Zd(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ju(e)&&e.host||Jd(e);return Ju(t)?t.host:t}function tf(e){const t=rb(e);return nb(t)?e.ownerDocument?e.ownerDocument.body:e.body:ll(t)&&ef(t)?t:tf(t)}function da(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=tf(e),o=i===((r=e.ownerDocument)==null?void 0:r.body),s=or(i);if(o){const a=Ts(s);return t.concat(s,s.visualViewport||[],ef(i)?i:[],a&&n?da(a):[])}return t.concat(i,da(i,[],n))}function Ts(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ib(e){const t=ul(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=ll(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,a=ua(n)!==o||ua(r)!==s;return a&&(n=o,r=s),{width:n,height:r,$:a}}function cl(e){return eb(e)?e:e.contextElement}function Qu(e){const t=cl(e);if(!ll(t))return ca(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:o}=ib(t);let s=(o?ua(n.width):n.width)/r,a=(o?ua(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const ob=ca(0);function ab(e){const t=or(e);return!tb()||!t.visualViewport?ob:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function sb(e,t,n){return!1}function ec(e,t,n,r){t===void 0&&(t=!1);const i=e.getBoundingClientRect(),o=cl(e);let s=ca(1);t&&(s=Qu(e));const a=sb()?ab(o):ca(0);let l=(i.left+a.x)/s.x,u=(i.top+a.y)/s.y,c=i.width/s.x,f=i.height/s.y;if(o){const g=or(o),h=r;let m=g,p=Ts(m);for(;p&&r&&h!==m;){const w=Qu(p),b=p.getBoundingClientRect(),v=ul(p),S=b.left+(p.clientLeft+parseFloat(v.paddingLeft))*w.x,O=b.top+(p.clientTop+parseFloat(v.paddingTop))*w.y;l*=w.x,u*=w.y,c*=w.x,f*=w.y,l+=S,u+=O,m=or(p),p=Ts(m)}}return Q1({width:c,height:f,x:l,y:u})}function lb(e,t){let n=null,r;const i=Jd(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),o();const{left:u,top:c,width:f,height:g}=e.getBoundingClientRect();if(a||t(),!f||!g)return;const h=Uo(c),m=Uo(i.clientWidth-(u+f)),p=Uo(i.clientHeight-(c+g)),w=Uo(u),v={rootMargin:-h+"px "+-m+"px "+-p+"px "+-w+"px",threshold:J1(0,Z1(1,l))||1};let S=!0;function O(R){const M=R[0].intersectionRatio;if(M!==l){if(!S)return s();M?s(!1,M):r=setTimeout(()=>{s(!1,1e-7)},1e3)}S=!1}try{n=new IntersectionObserver(O,{...v,root:i.ownerDocument})}catch{n=new IntersectionObserver(O,v)}n.observe(e)}return s(!0),o}function ub(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=cl(e),c=i||o?[...u?da(u):[],...da(t)]:[];c.forEach(b=>{i&&b.addEventListener("scroll",n,{passive:!0}),o&&b.addEventListener("resize",n)});const f=u&&a?lb(u,n):null;let g=-1,h=null;s&&(h=new ResizeObserver(b=>{let[v]=b;v&&v.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var S;(S=h)==null||S.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let m,p=l?ec(e):null;l&&w();function w(){const b=ec(e);p&&(b.x!==p.x||b.y!==p.y||b.width!==p.width||b.height!==p.height)&&n(),p=b,m=requestAnimationFrame(w)}return n(),()=>{var b;c.forEach(v=>{i&&v.removeEventListener("scroll",n),o&&v.removeEventListener("resize",n)}),f?.(),(b=h)==null||b.disconnect(),h=null,l&&cancelAnimationFrame(m)}}var Ds=d.useLayoutEffect,cb=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],fa=function(){};function db(e,t){return t?t[0]==="-"?e+t:e+"__"+t:e}function fb(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=[].concat(r);if(t&&e)for(var s in t)t.hasOwnProperty(s)&&t[s]&&o.push("".concat(db(e,s)));return o.filter(function(a){return a}).map(function(a){return String(a).trim()}).join(" ")}var tc=function(t){return Cb(t)?t.filter(Boolean):Kh(t)==="object"&&t!==null?[t]:[]},nf=function(t){t.className,t.clearValue,t.cx,t.getStyles,t.getClassNames,t.getValue,t.hasValue,t.isMulti,t.isRtl,t.options,t.selectOption,t.selectProps,t.setValue,t.theme;var n=sr(t,cb);return Le({},n)},Kt=function(t,n,r){var i=t.cx,o=t.getStyles,s=t.getClassNames,a=t.className;return{css:o(n,t),className:i(r??{},s(n,t),a)}};function Ca(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function hb(e){return Ca(e)?window.innerHeight:e.clientHeight}function rf(e){return Ca(e)?window.pageYOffset:e.scrollTop}function ha(e,t){if(Ca(e)){window.scrollTo(0,t);return}e.scrollTop=t}function gb(e){var t=getComputedStyle(e),n=t.position==="absolute",r=/(auto|scroll)/;if(t.position==="fixed")return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),!(n&&t.position==="static")&&r.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}function mb(e,t,n,r){return n*((e=e/r-1)*e*e+1)+t}function qo(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:200,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:fa,i=rf(e),o=t-i,s=10,a=0;function l(){a+=s;var u=mb(a,i,o,n);ha(e,u),a<n?window.requestAnimationFrame(l):r(e)}l()}function nc(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),i=t.offsetHeight/3;r.bottom+i>n.bottom?ha(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):r.top-i<n.top&&ha(e,Math.max(t.offsetTop-i,0))}function pb(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}function rc(){try{return document.createEvent("TouchEvent"),!0}catch{return!1}}function vb(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch{return!1}}var of=!1,bb={get passive(){return of=!0}},Go=typeof window<"u"?window:{};Go.addEventListener&&Go.removeEventListener&&(Go.addEventListener("p",fa,bb),Go.removeEventListener("p",fa,!1));var wb=of;function yb(e){return e!=null}function Cb(e){return Array.isArray(e)}function Yo(e,t,n){return e?t:n}var Sb=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=Object.entries(t).filter(function(s){var a=rr(s,1),l=a[0];return!r.includes(l)});return o.reduce(function(s,a){var l=rr(a,2),u=l[0],c=l[1];return s[u]=c,s},{})},xb=["children","innerProps"],kb=["children","innerProps"];function Mb(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,i=e.placement,o=e.shouldScroll,s=e.isFixedPosition,a=e.controlHeight,l=gb(n),u={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return u;var c=l.getBoundingClientRect(),f=c.height,g=n.getBoundingClientRect(),h=g.bottom,m=g.height,p=g.top,w=n.offsetParent.getBoundingClientRect(),b=w.top,v=s?window.innerHeight:hb(l),S=rf(l),O=parseInt(getComputedStyle(n).marginBottom,10),R=parseInt(getComputedStyle(n).marginTop,10),M=b-R,_=v-p,E=M+S,k=f-S-p,F=h-v+S+O,D=S+p-R,C=160;switch(i){case"auto":case"bottom":if(_>=m)return{placement:"bottom",maxHeight:t};if(k>=m&&!s)return o&&qo(l,F,C),{placement:"bottom",maxHeight:t};if(!s&&k>=r||s&&_>=r){o&&qo(l,F,C);var I=s?_-O:k-O;return{placement:"bottom",maxHeight:I}}if(i==="auto"||s){var T=t,x=s?M:E;return x>=r&&(T=Math.min(x-O-a,t)),{placement:"top",maxHeight:T}}if(i==="bottom")return o&&ha(l,F),{placement:"bottom",maxHeight:t};break;case"top":if(M>=m)return{placement:"top",maxHeight:t};if(E>=m&&!s)return o&&qo(l,D,C),{placement:"top",maxHeight:t};if(!s&&E>=r||s&&M>=r){var $=t;return(!s&&E>=r||s&&M>=r)&&($=s?M-R:E-R),o&&qo(l,D,C),{placement:"top",maxHeight:$}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return u}function Rb(e){var t={bottom:"top",top:"bottom"};return e?t[e]:"bottom"}var af=function(t){return t==="auto"?"bottom":t},Eb=function(t,n){var r,i=t.placement,o=t.theme,s=o.borderRadius,a=o.spacing,l=o.colors;return Le((r={label:"menu"},Gi(r,Rb(i),"100%"),Gi(r,"position","absolute"),Gi(r,"width","100%"),Gi(r,"zIndex",1),r),n?{}:{backgroundColor:l.neutral0,borderRadius:s,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:a.menuGutter,marginTop:a.menuGutter})},sf=d.createContext(null),Ib=function(t){var n=t.children,r=t.minMenuHeight,i=t.maxMenuHeight,o=t.menuPlacement,s=t.menuPosition,a=t.menuShouldScrollIntoView,l=t.theme,u=d.useContext(sf)||{},c=u.setPortalPlacement,f=d.useRef(null),g=d.useState(i),h=rr(g,2),m=h[0],p=h[1],w=d.useState(null),b=rr(w,2),v=b[0],S=b[1],O=l.spacing.controlHeight;return Ds(function(){var R=f.current;if(R){var M=s==="fixed",_=a&&!M,E=Mb({maxHeight:i,menuEl:R,minHeight:r,placement:o,shouldScroll:_,isFixedPosition:M,controlHeight:O});p(E.maxHeight),S(E.placement),c?.(E.placement)}},[i,o,s,a,r,c,O]),n({ref:f,placerProps:Le(Le({},t),{},{placement:v||af(o),maxHeight:m})})},Tb=function(t){var n=t.children,r=t.innerRef,i=t.innerProps;return _e("div",Ne({},Kt(t,"menu",{menu:!0}),{ref:r},i),n)},Db=Tb,Ob=function(t,n){var r=t.maxHeight,i=t.theme.spacing.baseUnit;return Le({maxHeight:r,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},n?{}:{paddingBottom:i,paddingTop:i})},Pb=function(t){var n=t.children,r=t.innerProps,i=t.innerRef,o=t.isMulti;return _e("div",Ne({},Kt(t,"menuList",{"menu-list":!0,"menu-list--is-multi":o}),{ref:i},r),n)},lf=function(t,n){var r=t.theme,i=r.spacing.baseUnit,o=r.colors;return Le({textAlign:"center"},n?{}:{color:o.neutral40,padding:"".concat(i*2,"px ").concat(i*3,"px")})},_b=lf,Fb=lf,Lb=function(t){var n=t.children,r=n===void 0?"No options":n,i=t.innerProps,o=sr(t,xb);return _e("div",Ne({},Kt(Le(Le({},o),{},{children:r,innerProps:i}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),i),r)},Ab=function(t){var n=t.children,r=n===void 0?"Loading...":n,i=t.innerProps,o=sr(t,kb);return _e("div",Ne({},Kt(Le(Le({},o),{},{children:r,innerProps:i}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),i),r)},Hb=function(t){var n=t.rect,r=t.offset,i=t.position;return{left:n.left,position:i,top:r,width:n.width,zIndex:1}},zb=function(t){var n=t.appendTo,r=t.children,i=t.controlElement,o=t.innerProps,s=t.menuPlacement,a=t.menuPosition,l=d.useRef(null),u=d.useRef(null),c=d.useState(af(s)),f=rr(c,2),g=f[0],h=f[1],m=d.useMemo(function(){return{setPortalPlacement:h}},[]),p=d.useState(null),w=rr(p,2),b=w[0],v=w[1],S=d.useCallback(function(){if(i){var _=pb(i),E=a==="fixed"?0:window.pageYOffset,k=_[g]+E;(k!==b?.offset||_.left!==b?.rect.left||_.width!==b?.rect.width)&&v({offset:k,rect:_})}},[i,a,g,b?.offset,b?.rect.left,b?.rect.width]);Ds(function(){S()},[S]);var O=d.useCallback(function(){typeof u.current=="function"&&(u.current(),u.current=null),i&&l.current&&(u.current=ub(i,l.current,S,{elementResize:"ResizeObserver"in window}))},[i,S]);Ds(function(){O()},[O]);var R=d.useCallback(function(_){l.current=_,O()},[O]);if(!n&&a!=="fixed"||!b)return null;var M=_e("div",Ne({ref:R},Kt(Le(Le({},t),{},{offset:b.offset,position:a,rect:b.rect}),"menuPortal",{"menu-portal":!0}),o),r);return _e(sf.Provider,{value:m},n?Lc.createPortal(M,n):M)},Vb=function(t){var n=t.isDisabled,r=t.isRtl;return{label:"container",direction:r?"rtl":void 0,pointerEvents:n?"none":void 0,position:"relative"}},Nb=function(t){var n=t.children,r=t.innerProps,i=t.isDisabled,o=t.isRtl;return _e("div",Ne({},Kt(t,"container",{"--is-disabled":i,"--is-rtl":o}),r),n)},$b=function(t,n){var r=t.theme.spacing,i=t.isMulti,o=t.hasValue,s=t.selectProps.controlShouldRenderValue;return Le({alignItems:"center",display:i&&o&&s?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},n?{}:{padding:"".concat(r.baseUnit/2,"px ").concat(r.baseUnit*2,"px")})},Bb=function(t){var n=t.children,r=t.innerProps,i=t.isMulti,o=t.hasValue;return _e("div",Ne({},Kt(t,"valueContainer",{"value-container":!0,"value-container--is-multi":i,"value-container--has-value":o}),r),n)},Wb=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},Ub=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"indicatorsContainer",{indicators:!0}),r),n)},ic,qb=["size"],Gb=["innerProps","isRtl","size"],Yb={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},uf=function(t){var n=t.size,r=sr(t,qb);return _e("svg",Ne({height:n,width:n,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:Yb},r))},dl=function(t){return _e(uf,Ne({size:20},t),_e("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},cf=function(t){return _e(uf,Ne({size:20},t),_e("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},df=function(t,n){var r=t.isFocused,i=t.theme,o=i.spacing.baseUnit,s=i.colors;return Le({label:"indicatorContainer",display:"flex",transition:"color 150ms"},n?{}:{color:r?s.neutral60:s.neutral20,padding:o*2,":hover":{color:r?s.neutral80:s.neutral40}})},Xb=df,jb=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),r),n||_e(cf,null))},Kb=df,Zb=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"clearIndicator",{indicator:!0,"clear-indicator":!0}),r),n||_e(dl,null))},Jb=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing.baseUnit,s=i.colors;return Le({label:"indicatorSeparator",alignSelf:"stretch",width:1},n?{}:{backgroundColor:r?s.neutral10:s.neutral20,marginBottom:o*2,marginTop:o*2})},Qb=function(t){var n=t.innerProps;return _e("span",Ne({},n,Kt(t,"indicatorSeparator",{"indicator-separator":!0})))},ew=Zh(ic||(ic=K1([`
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
`]))),tw=function(t,n){var r=t.isFocused,i=t.size,o=t.theme,s=o.colors,a=o.spacing.baseUnit;return Le({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:i,lineHeight:1,marginRight:i,textAlign:"center",verticalAlign:"middle"},n?{}:{color:r?s.neutral60:s.neutral20,padding:a*2})},ds=function(t){var n=t.delay,r=t.offset;return _e("span",{css:Ac({animation:"".concat(ew," 1s ease-in-out ").concat(n,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:r?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},nw=function(t){var n=t.innerProps,r=t.isRtl,i=t.size,o=i===void 0?4:i,s=sr(t,Gb);return _e("div",Ne({},Kt(Le(Le({},s),{},{innerProps:n,isRtl:r,size:o}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),n),_e(ds,{delay:0,offset:r}),_e(ds,{delay:160,offset:!0}),_e(ds,{delay:320,offset:!r}))},rw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.theme,s=o.colors,a=o.borderRadius,l=o.spacing;return Le({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:l.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},n?{}:{backgroundColor:r?s.neutral5:s.neutral0,borderColor:r?s.neutral10:i?s.primary:s.neutral20,borderRadius:a,borderStyle:"solid",borderWidth:1,boxShadow:i?"0 0 0 1px ".concat(s.primary):void 0,"&:hover":{borderColor:i?s.primary:s.neutral30}})},iw=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.innerRef,s=t.innerProps,a=t.menuIsOpen;return _e("div",Ne({ref:o},Kt(t,"control",{control:!0,"control--is-disabled":r,"control--is-focused":i,"control--menu-is-open":a}),s,{"aria-disabled":r||void 0}),n)},ow=iw,aw=["data"],sw=function(t,n){var r=t.theme.spacing;return n?{}:{paddingBottom:r.baseUnit*2,paddingTop:r.baseUnit*2}},lw=function(t){var n=t.children,r=t.cx,i=t.getStyles,o=t.getClassNames,s=t.Heading,a=t.headingProps,l=t.innerProps,u=t.label,c=t.theme,f=t.selectProps;return _e("div",Ne({},Kt(t,"group",{group:!0}),l),_e(s,Ne({},a,{selectProps:f,theme:c,getStyles:i,getClassNames:o,cx:r}),u),_e("div",null,n))},uw=function(t,n){var r=t.theme,i=r.colors,o=r.spacing;return Le({label:"group",cursor:"default",display:"block"},n?{}:{color:i.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:o.baseUnit*3,paddingRight:o.baseUnit*3,textTransform:"uppercase"})},cw=function(t){var n=nf(t);n.data;var r=sr(n,aw);return _e("div",Ne({},Kt(t,"groupHeading",{"group-heading":!0}),r))},dw=lw,fw=["innerRef","isDisabled","isHidden","inputClassName"],hw=function(t,n){var r=t.isDisabled,i=t.value,o=t.theme,s=o.spacing,a=o.colors;return Le(Le({visibility:r?"hidden":"visible",transform:i?"translateZ(0)":""},gw),n?{}:{margin:s.baseUnit/2,paddingBottom:s.baseUnit/2,paddingTop:s.baseUnit/2,color:a.neutral80})},ff={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},gw={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":Le({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},ff)},mw=function(t){return Le({label:"input",color:"inherit",background:0,opacity:t?0:1,width:"100%"},ff)},pw=function(t){var n=t.cx,r=t.value,i=nf(t),o=i.innerRef,s=i.isDisabled,a=i.isHidden,l=i.inputClassName,u=sr(i,fw);return _e("div",Ne({},Kt(t,"input",{"input-container":!0}),{"data-value":r||""}),_e("input",Ne({className:n({input:!0},l),ref:o,style:mw(a),disabled:s},u)))},vw=pw,bw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,s=r.colors;return Le({label:"multiValue",display:"flex",minWidth:0},n?{}:{backgroundColor:s.neutral10,borderRadius:o/2,margin:i.baseUnit/2})},ww=function(t,n){var r=t.theme,i=r.borderRadius,o=r.colors,s=t.cropWithEllipsis;return Le({overflow:"hidden",textOverflow:s||s===void 0?"ellipsis":void 0,whiteSpace:"nowrap"},n?{}:{borderRadius:i/2,color:o.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},yw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,s=r.colors,a=t.isFocused;return Le({alignItems:"center",display:"flex"},n?{}:{borderRadius:o/2,backgroundColor:a?s.dangerLight:void 0,paddingLeft:i.baseUnit,paddingRight:i.baseUnit,":hover":{backgroundColor:s.dangerLight,color:s.danger}})},hf=function(t){var n=t.children,r=t.innerProps;return _e("div",r,n)},Cw=hf,Sw=hf;function xw(e){var t=e.children,n=e.innerProps;return _e("div",Ne({role:"button"},n),t||_e(dl,{size:14}))}var kw=function(t){var n=t.children,r=t.components,i=t.data,o=t.innerProps,s=t.isDisabled,a=t.removeProps,l=t.selectProps,u=r.Container,c=r.Label,f=r.Remove;return _e(u,{data:i,innerProps:Le(Le({},Kt(t,"multiValue",{"multi-value":!0,"multi-value--is-disabled":s})),o),selectProps:l},_e(c,{data:i,innerProps:Le({},Kt(t,"multiValueLabel",{"multi-value__label":!0})),selectProps:l},n),_e(f,{data:i,innerProps:Le(Le({},Kt(t,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(n||"option")},a),selectProps:l}))},Mw=kw,Rw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.isSelected,s=t.theme,a=s.spacing,l=s.colors;return Le({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},n?{}:{backgroundColor:o?l.primary:i?l.primary25:"transparent",color:r?l.neutral20:o?l.neutral0:"inherit",padding:"".concat(a.baseUnit*2,"px ").concat(a.baseUnit*3,"px"),":active":{backgroundColor:r?void 0:o?l.primary:l.primary50}})},Ew=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.isSelected,s=t.innerRef,a=t.innerProps;return _e("div",Ne({},Kt(t,"option",{option:!0,"option--is-disabled":r,"option--is-focused":i,"option--is-selected":o}),{ref:s,"aria-disabled":r},a),n)},Iw=Ew,Tw=function(t,n){var r=t.theme,i=r.spacing,o=r.colors;return Le({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},n?{}:{color:o.neutral50,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},Dw=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"placeholder",{placeholder:!0}),r),n)},Ow=Dw,Pw=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing,s=i.colors;return Le({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},n?{}:{color:r?s.neutral40:s.neutral80,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},_w=function(t){var n=t.children,r=t.isDisabled,i=t.innerProps;return _e("div",Ne({},Kt(t,"singleValue",{"single-value":!0,"single-value--is-disabled":r}),i),n)},Fw=_w,gf={ClearIndicator:Zb,Control:ow,DropdownIndicator:jb,DownChevron:cf,CrossIcon:dl,Group:dw,GroupHeading:cw,IndicatorsContainer:Ub,IndicatorSeparator:Qb,Input:vw,LoadingIndicator:nw,Menu:Db,MenuList:Pb,MenuPortal:zb,LoadingMessage:Ab,NoOptionsMessage:Lb,MultiValue:Mw,MultiValueContainer:Cw,MultiValueLabel:Sw,MultiValueRemove:xw,Option:Iw,Placeholder:Ow,SelectContainer:Nb,SingleValue:Fw,ValueContainer:Bb},Lw=function(t){return Le(Le({},gf),t.components)},oc=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function Aw(e,t){return!!(e===t||oc(e)&&oc(t))}function Hw(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!Aw(e[n],t[n]))return!1;return!0}function zw(e,t){t===void 0&&(t=Hw);var n=null;function r(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];if(n&&n.lastThis===this&&t(i,n.lastArgs))return n.lastResult;var s=e.apply(this,i);return n={lastResult:s,lastArgs:i,lastThis:this},s}return r.clear=function(){n=null},r}var Vw={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},Nw=function(t){return _e("span",Ne({css:Vw},t))},ac=Nw,$w={guidance:function(t){var n=t.isSearchable,r=t.isMulti,i=t.tabSelectsValue,o=t.context,s=t.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(i?", press Tab to select the option and exit the menu":"",".");case"input":return s?"".concat(t["aria-label"]||"Select"," is focused ").concat(n?",type to refine list":"",", press Down to open the menu, ").concat(r?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(t){var n=t.action,r=t.label,i=r===void 0?"":r,o=t.labels,s=t.isDisabled;switch(n){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(i,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return s?"option ".concat(i," is disabled. Select another option."):"option ".concat(i,", selected.");default:return""}},onFocus:function(t){var n=t.context,r=t.focused,i=t.options,o=t.label,s=o===void 0?"":o,a=t.selectValue,l=t.isDisabled,u=t.isSelected,c=t.isAppleDevice,f=function(p,w){return p&&p.length?"".concat(p.indexOf(w)+1," of ").concat(p.length):""};if(n==="value"&&a)return"value ".concat(s," focused, ").concat(f(a,r),".");if(n==="menu"&&c){var g=l?" disabled":"",h="".concat(u?" selected":"").concat(g);return"".concat(s).concat(h,", ").concat(f(i,r),".")}return""},onFilter:function(t){var n=t.inputValue,r=t.resultsMessage;return"".concat(r).concat(n?" for search term "+n:"",".")}},Bw=function(t){var n=t.ariaSelection,r=t.focusedOption,i=t.focusedValue,o=t.focusableOptions,s=t.isFocused,a=t.selectValue,l=t.selectProps,u=t.id,c=t.isAppleDevice,f=l.ariaLiveMessages,g=l.getOptionLabel,h=l.inputValue,m=l.isMulti,p=l.isOptionDisabled,w=l.isSearchable,b=l.menuIsOpen,v=l.options,S=l.screenReaderStatus,O=l.tabSelectsValue,R=l.isLoading,M=l["aria-label"],_=l["aria-live"],E=d.useMemo(function(){return Le(Le({},$w),f||{})},[f]),k=d.useMemo(function(){var x="";if(n&&E.onChange){var $=n.option,q=n.options,X=n.removedValue,oe=n.removedValues,Q=n.value,J=function(P){return Array.isArray(P)?null:P},te=X||$||J(Q),ae=te?g(te):"",le=q||oe||void 0,fe=le?le.map(g):[],re=Le({isDisabled:te&&p(te,a),label:ae,labels:fe},n);x=E.onChange(re)}return x},[n,E,p,a,g]),F=d.useMemo(function(){var x="",$=r||i,q=!!(r&&a&&a.includes(r));if($&&E.onFocus){var X={focused:$,label:g($),isDisabled:p($,a),isSelected:q,options:o,context:$===r?"menu":"value",selectValue:a,isAppleDevice:c};x=E.onFocus(X)}return x},[r,i,g,p,E,o,a,c]),D=d.useMemo(function(){var x="";if(b&&v.length&&!R&&E.onFilter){var $=S({count:o.length});x=E.onFilter({inputValue:h,resultsMessage:$})}return x},[o,h,b,E,v,S,R]),C=n?.action==="initial-input-focus",I=d.useMemo(function(){var x="";if(E.guidance){var $=i?"value":b?"menu":"input";x=E.guidance({"aria-label":M,context:$,isDisabled:r&&p(r,a),isMulti:m,isSearchable:w,tabSelectsValue:O,isInitialFocus:C})}return x},[M,r,i,m,p,w,b,E,a,O,C]),T=_e(d.Fragment,null,_e("span",{id:"aria-selection"},k),_e("span",{id:"aria-focused"},F),_e("span",{id:"aria-results"},D),_e("span",{id:"aria-guidance"},I));return _e(d.Fragment,null,_e(ac,{id:u},C&&T),_e(ac,{"aria-live":_,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},s&&!C&&T))},Ww=Bw,Os=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],Uw=new RegExp("["+Os.map(function(e){return e.letters}).join("")+"]","g"),mf={};for(var fs=0;fs<Os.length;fs++)for(var hs=Os[fs],gs=0;gs<hs.letters.length;gs++)mf[hs.letters[gs]]=hs.base;var pf=function(t){return t.replace(Uw,function(n){return mf[n]})},qw=zw(pf),sc=function(t){return t.replace(/^\s+|\s+$/g,"")},Gw=function(t){return"".concat(t.label," ").concat(t.value)},Yw=function(t){return function(n,r){if(n.data.__isNew__)return!0;var i=Le({ignoreCase:!0,ignoreAccents:!0,stringify:Gw,trim:!0,matchFrom:"any"},t),o=i.ignoreCase,s=i.ignoreAccents,a=i.stringify,l=i.trim,u=i.matchFrom,c=l?sc(r):r,f=l?sc(a(n)):a(n);return o&&(c=c.toLowerCase(),f=f.toLowerCase()),s&&(c=qw(c),f=pf(f)),u==="start"?f.substr(0,c.length)===c:f.indexOf(c)>-1}},Xw=["innerRef"];function jw(e){var t=e.innerRef,n=sr(e,Xw),r=Sb(n,"onExited","in","enter","exit","appear");return _e("input",Ne({ref:t},r,{css:Ac({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var Kw=function(t){t.cancelable&&t.preventDefault(),t.stopPropagation()};function Zw(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,i=e.onTopArrive,o=e.onTopLeave,s=d.useRef(!1),a=d.useRef(!1),l=d.useRef(0),u=d.useRef(null),c=d.useCallback(function(w,b){if(u.current!==null){var v=u.current,S=v.scrollTop,O=v.scrollHeight,R=v.clientHeight,M=u.current,_=b>0,E=O-R-S,k=!1;E>b&&s.current&&(r&&r(w),s.current=!1),_&&a.current&&(o&&o(w),a.current=!1),_&&b>E?(n&&!s.current&&n(w),M.scrollTop=O,k=!0,s.current=!0):!_&&-b>S&&(i&&!a.current&&i(w),M.scrollTop=0,k=!0,a.current=!0),k&&Kw(w)}},[n,r,i,o]),f=d.useCallback(function(w){c(w,w.deltaY)},[c]),g=d.useCallback(function(w){l.current=w.changedTouches[0].clientY},[]),h=d.useCallback(function(w){var b=l.current-w.changedTouches[0].clientY;c(w,b)},[c]),m=d.useCallback(function(w){if(w){var b=wb?{passive:!1}:!1;w.addEventListener("wheel",f,b),w.addEventListener("touchstart",g,b),w.addEventListener("touchmove",h,b)}},[h,g,f]),p=d.useCallback(function(w){w&&(w.removeEventListener("wheel",f,!1),w.removeEventListener("touchstart",g,!1),w.removeEventListener("touchmove",h,!1))},[h,g,f]);return d.useEffect(function(){if(t){var w=u.current;return m(w),function(){p(w)}}},[t,m,p]),function(w){u.current=w}}var lc=["boxSizing","height","overflow","paddingRight","position"],uc={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function cc(e){e.cancelable&&e.preventDefault()}function dc(e){e.stopPropagation()}function fc(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;e===0?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function hc(){return"ontouchstart"in window||navigator.maxTouchPoints}var gc=!!(typeof window<"u"&&window.document&&window.document.createElement),Ui=0,oi={capture:!1,passive:!1};function Jw(e){var t=e.isEnabled,n=e.accountForScrollbars,r=n===void 0?!0:n,i=d.useRef({}),o=d.useRef(null),s=d.useCallback(function(l){if(gc){var u=document.body,c=u&&u.style;if(r&&lc.forEach(function(m){var p=c&&c[m];i.current[m]=p}),r&&Ui<1){var f=parseInt(i.current.paddingRight,10)||0,g=document.body?document.body.clientWidth:0,h=window.innerWidth-g+f||0;Object.keys(uc).forEach(function(m){var p=uc[m];c&&(c[m]=p)}),c&&(c.paddingRight="".concat(h,"px"))}u&&hc()&&(u.addEventListener("touchmove",cc,oi),l&&(l.addEventListener("touchstart",fc,oi),l.addEventListener("touchmove",dc,oi))),Ui+=1}},[r]),a=d.useCallback(function(l){if(gc){var u=document.body,c=u&&u.style;Ui=Math.max(Ui-1,0),r&&Ui<1&&lc.forEach(function(f){var g=i.current[f];c&&(c[f]=g)}),u&&hc()&&(u.removeEventListener("touchmove",cc,oi),l&&(l.removeEventListener("touchstart",fc,oi),l.removeEventListener("touchmove",dc,oi)))}},[r]);return d.useEffect(function(){if(t){var l=o.current;return s(l),function(){a(l)}}},[t,s,a]),function(l){o.current=l}}var Qw=function(t){var n=t.target;return n.ownerDocument.activeElement&&n.ownerDocument.activeElement.blur()},ey={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function ty(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,i=r===void 0?!0:r,o=e.onBottomArrive,s=e.onBottomLeave,a=e.onTopArrive,l=e.onTopLeave,u=Zw({isEnabled:i,onBottomArrive:o,onBottomLeave:s,onTopArrive:a,onTopLeave:l}),c=Jw({isEnabled:n}),f=function(h){u(h),c(h)};return _e(d.Fragment,null,n&&_e("div",{onClick:Qw,css:ey}),t(f))}var ny={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},ry=function(t){var n=t.name,r=t.onFocus;return _e("input",{required:!0,name:n,tabIndex:-1,"aria-hidden":"true",onFocus:r,css:ny,value:"",onChange:function(){}})},iy=ry;function fl(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function oy(){return fl(/^iPhone/i)}function vf(){return fl(/^Mac/i)}function ay(){return fl(/^iPad/i)||vf()&&navigator.maxTouchPoints>1}function sy(){return oy()||ay()}function ly(){return vf()||sy()}var uy=function(t){return t.label},cy=function(t){return t.label},dy=function(t){return t.value},fy=function(t){return!!t.isDisabled},hy={clearIndicator:Kb,container:Vb,control:rw,dropdownIndicator:Xb,group:sw,groupHeading:uw,indicatorsContainer:Wb,indicatorSeparator:Jb,input:hw,loadingIndicator:tw,loadingMessage:Fb,menu:Eb,menuList:Ob,menuPortal:Hb,multiValue:bw,multiValueLabel:ww,multiValueRemove:yw,noOptionsMessage:_b,option:Rw,placeholder:Tw,singleValue:Pw,valueContainer:$b},gy={primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},my=4,bf=4,py=38,vy=bf*2,by={baseUnit:bf,controlHeight:py,menuGutter:vy},ms={borderRadius:my,colors:gy,spacing:by},wy={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:rc(),captureMenuScroll:!rc(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:Yw(),formatGroupLabel:uy,getOptionLabel:cy,getOptionValue:dy,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:fy,loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!vb(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(t){var n=t.count;return"".concat(n," result").concat(n!==1?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function mc(e,t,n,r){var i=Cf(e,t,n),o=Sf(e,t,n),s=yf(e,t),a=ga(e,t);return{type:"option",data:t,isDisabled:i,isSelected:o,label:s,value:a,index:r}}function Qo(e,t){return e.options.map(function(n,r){if("options"in n){var i=n.options.map(function(s,a){return mc(e,s,t,a)}).filter(function(s){return vc(e,s)});return i.length>0?{type:"group",data:n,options:i,index:r}:void 0}var o=mc(e,n,t,r);return vc(e,o)?o:void 0}).filter(yb)}function wf(e){return e.reduce(function(t,n){return n.type==="group"?t.push.apply(t,Ls(n.options.map(function(r){return r.data}))):t.push(n.data),t},[])}function pc(e,t){return e.reduce(function(n,r){return r.type==="group"?n.push.apply(n,Ls(r.options.map(function(i){return{data:i.data,id:"".concat(t,"-").concat(r.index,"-").concat(i.index)}}))):n.push({data:r.data,id:"".concat(t,"-").concat(r.index)}),n},[])}function yy(e,t){return wf(Qo(e,t))}function vc(e,t){var n=e.inputValue,r=n===void 0?"":n,i=t.data,o=t.isSelected,s=t.label,a=t.value;return(!kf(e)||!o)&&xf(e,{label:s,value:a,data:i},r)}function Cy(e,t){var n=e.focusedValue,r=e.selectValue,i=r.indexOf(n);if(i>-1){var o=t.indexOf(n);if(o>-1)return n;if(i<t.length)return t[i]}return null}function Sy(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}var ps=function(t,n){var r,i=(r=t.find(function(o){return o.data===n}))===null||r===void 0?void 0:r.id;return i||null},yf=function(t,n){return t.getOptionLabel(n)},ga=function(t,n){return t.getOptionValue(n)};function Cf(e,t,n){return typeof e.isOptionDisabled=="function"?e.isOptionDisabled(t,n):!1}function Sf(e,t,n){if(n.indexOf(t)>-1)return!0;if(typeof e.isOptionSelected=="function")return e.isOptionSelected(t,n);var r=ga(e,t);return n.some(function(i){return ga(e,i)===r})}function xf(e,t,n){return e.filterOption?e.filterOption(t,n):!0}var kf=function(t){var n=t.hideSelectedOptions,r=t.isMulti;return n===void 0?r:n},xy=1,Mf=function(e){pg(n,e);var t=wg(n);function n(r){var i;if(bg(this,n),i=t.call(this,r),i.state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},i.blockOptionHover=!1,i.isComposing=!1,i.commonProps=void 0,i.initialTouchX=0,i.initialTouchY=0,i.openAfterFocus=!1,i.scrollToFocusedOptionOnUpdate=!1,i.userIsDragging=void 0,i.isAppleDevice=ly(),i.controlRef=null,i.getControlRef=function(l){i.controlRef=l},i.focusedOptionRef=null,i.getFocusedOptionRef=function(l){i.focusedOptionRef=l},i.menuListRef=null,i.getMenuListRef=function(l){i.menuListRef=l},i.inputRef=null,i.getInputRef=function(l){i.inputRef=l},i.focus=i.focusInput,i.blur=i.blurInput,i.onChange=function(l,u){var c=i.props,f=c.onChange,g=c.name;u.name=g,i.ariaOnChange(l,u),f(l,u)},i.setValue=function(l,u,c){var f=i.props,g=f.closeMenuOnSelect,h=f.isMulti,m=f.inputValue;i.onInputChange("",{action:"set-value",prevInputValue:m}),g&&(i.setState({inputIsHiddenAfterUpdate:!h}),i.onMenuClose()),i.setState({clearFocusValueOnUpdate:!0}),i.onChange(l,{action:u,option:c})},i.selectOption=function(l){var u=i.props,c=u.blurInputOnSelect,f=u.isMulti,g=u.name,h=i.state.selectValue,m=f&&i.isOptionSelected(l,h),p=i.isOptionDisabled(l,h);if(m){var w=i.getOptionValue(l);i.setValue(h.filter(function(b){return i.getOptionValue(b)!==w}),"deselect-option",l)}else if(!p)f?i.setValue([].concat(Ls(h),[l]),"select-option",l):i.setValue(l,"select-option");else{i.ariaOnChange(l,{action:"select-option",option:l,name:g});return}c&&i.blurInput()},i.removeValue=function(l){var u=i.props.isMulti,c=i.state.selectValue,f=i.getOptionValue(l),g=c.filter(function(m){return i.getOptionValue(m)!==f}),h=Yo(u,g,g[0]||null);i.onChange(h,{action:"remove-value",removedValue:l}),i.focusInput()},i.clearValue=function(){var l=i.state.selectValue;i.onChange(Yo(i.props.isMulti,[],null),{action:"clear",removedValues:l})},i.popValue=function(){var l=i.props.isMulti,u=i.state.selectValue,c=u[u.length-1],f=u.slice(0,u.length-1),g=Yo(l,f,f[0]||null);c&&i.onChange(g,{action:"pop-value",removedValue:c})},i.getFocusedOptionId=function(l){return ps(i.state.focusableOptionsWithIds,l)},i.getFocusableOptionsWithIds=function(){return pc(Qo(i.props,i.state.selectValue),i.getElementId("option"))},i.getValue=function(){return i.state.selectValue},i.cx=function(){for(var l=arguments.length,u=new Array(l),c=0;c<l;c++)u[c]=arguments[c];return fb.apply(void 0,[i.props.classNamePrefix].concat(u))},i.getOptionLabel=function(l){return yf(i.props,l)},i.getOptionValue=function(l){return ga(i.props,l)},i.getStyles=function(l,u){var c=i.props.unstyled,f=hy[l](u,c);f.boxSizing="border-box";var g=i.props.styles[l];return g?g(f,u):f},i.getClassNames=function(l,u){var c,f;return(c=(f=i.props.classNames)[l])===null||c===void 0?void 0:c.call(f,u)},i.getElementId=function(l){return"".concat(i.state.instancePrefix,"-").concat(l)},i.getComponents=function(){return Lw(i.props)},i.buildCategorizedOptions=function(){return Qo(i.props,i.state.selectValue)},i.getCategorizedOptions=function(){return i.props.menuIsOpen?i.buildCategorizedOptions():[]},i.buildFocusableOptions=function(){return wf(i.buildCategorizedOptions())},i.getFocusableOptions=function(){return i.props.menuIsOpen?i.buildFocusableOptions():[]},i.ariaOnChange=function(l,u){i.setState({ariaSelection:Le({value:l},u)})},i.onMenuMouseDown=function(l){l.button===0&&(l.stopPropagation(),l.preventDefault(),i.focusInput())},i.onMenuMouseMove=function(l){i.blockOptionHover=!1},i.onControlMouseDown=function(l){if(!l.defaultPrevented){var u=i.props.openMenuOnClick;i.state.isFocused?i.props.menuIsOpen?l.target.tagName!=="INPUT"&&l.target.tagName!=="TEXTAREA"&&i.onMenuClose():u&&i.openMenu("first"):(u&&(i.openAfterFocus=!0),i.focusInput()),l.target.tagName!=="INPUT"&&l.target.tagName!=="TEXTAREA"&&l.preventDefault()}},i.onDropdownIndicatorMouseDown=function(l){if(!(l&&l.type==="mousedown"&&l.button!==0)&&!i.props.isDisabled){var u=i.props,c=u.isMulti,f=u.menuIsOpen;i.focusInput(),f?(i.setState({inputIsHiddenAfterUpdate:!c}),i.onMenuClose()):i.openMenu("first"),l.preventDefault()}},i.onClearIndicatorMouseDown=function(l){l&&l.type==="mousedown"&&l.button!==0||(i.clearValue(),l.preventDefault(),i.openAfterFocus=!1,l.type==="touchend"?i.focusInput():setTimeout(function(){return i.focusInput()}))},i.onScroll=function(l){typeof i.props.closeMenuOnScroll=="boolean"?l.target instanceof HTMLElement&&Ca(l.target)&&i.props.onMenuClose():typeof i.props.closeMenuOnScroll=="function"&&i.props.closeMenuOnScroll(l)&&i.props.onMenuClose()},i.onCompositionStart=function(){i.isComposing=!0},i.onCompositionEnd=function(){i.isComposing=!1},i.onTouchStart=function(l){var u=l.touches,c=u&&u.item(0);c&&(i.initialTouchX=c.clientX,i.initialTouchY=c.clientY,i.userIsDragging=!1)},i.onTouchMove=function(l){var u=l.touches,c=u&&u.item(0);if(c){var f=Math.abs(c.clientX-i.initialTouchX),g=Math.abs(c.clientY-i.initialTouchY),h=5;i.userIsDragging=f>h||g>h}},i.onTouchEnd=function(l){i.userIsDragging||(i.controlRef&&!i.controlRef.contains(l.target)&&i.menuListRef&&!i.menuListRef.contains(l.target)&&i.blurInput(),i.initialTouchX=0,i.initialTouchY=0)},i.onControlTouchEnd=function(l){i.userIsDragging||i.onControlMouseDown(l)},i.onClearIndicatorTouchEnd=function(l){i.userIsDragging||i.onClearIndicatorMouseDown(l)},i.onDropdownIndicatorTouchEnd=function(l){i.userIsDragging||i.onDropdownIndicatorMouseDown(l)},i.handleInputChange=function(l){var u=i.props.inputValue,c=l.currentTarget.value;i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange(c,{action:"input-change",prevInputValue:u}),i.props.menuIsOpen||i.onMenuOpen()},i.onInputFocus=function(l){i.props.onFocus&&i.props.onFocus(l),i.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(i.openAfterFocus||i.props.openMenuOnFocus)&&i.openMenu("first"),i.openAfterFocus=!1},i.onInputBlur=function(l){var u=i.props.inputValue;if(i.menuListRef&&i.menuListRef.contains(document.activeElement)){i.inputRef.focus();return}i.props.onBlur&&i.props.onBlur(l),i.onInputChange("",{action:"input-blur",prevInputValue:u}),i.onMenuClose(),i.setState({focusedValue:null,isFocused:!1})},i.onOptionHover=function(l){if(!(i.blockOptionHover||i.state.focusedOption===l)){var u=i.getFocusableOptions(),c=u.indexOf(l);i.setState({focusedOption:l,focusedOptionId:c>-1?i.getFocusedOptionId(l):null})}},i.shouldHideSelectedOptions=function(){return kf(i.props)},i.onValueInputFocus=function(l){l.preventDefault(),l.stopPropagation(),i.focus()},i.onKeyDown=function(l){var u=i.props,c=u.isMulti,f=u.backspaceRemovesValue,g=u.escapeClearsValue,h=u.inputValue,m=u.isClearable,p=u.isDisabled,w=u.menuIsOpen,b=u.onKeyDown,v=u.tabSelectsValue,S=u.openMenuOnFocus,O=i.state,R=O.focusedOption,M=O.focusedValue,_=O.selectValue;if(!p&&!(typeof b=="function"&&(b(l),l.defaultPrevented))){switch(i.blockOptionHover=!0,l.key){case"ArrowLeft":if(!c||h)return;i.focusValue("previous");break;case"ArrowRight":if(!c||h)return;i.focusValue("next");break;case"Delete":case"Backspace":if(h)return;if(M)i.removeValue(M);else{if(!f)return;c?i.popValue():m&&i.clearValue()}break;case"Tab":if(i.isComposing||l.shiftKey||!w||!v||!R||S&&i.isOptionSelected(R,_))return;i.selectOption(R);break;case"Enter":if(l.keyCode===229)break;if(w){if(!R||i.isComposing)return;i.selectOption(R);break}return;case"Escape":w?(i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange("",{action:"menu-close",prevInputValue:h}),i.onMenuClose()):m&&g&&i.clearValue();break;case" ":if(h)return;if(!w){i.openMenu("first");break}if(!R)return;i.selectOption(R);break;case"ArrowUp":w?i.focusOption("up"):i.openMenu("last");break;case"ArrowDown":w?i.focusOption("down"):i.openMenu("first");break;case"PageUp":if(!w)return;i.focusOption("pageup");break;case"PageDown":if(!w)return;i.focusOption("pagedown");break;case"Home":if(!w)return;i.focusOption("first");break;case"End":if(!w)return;i.focusOption("last");break;default:return}l.preventDefault()}},i.state.instancePrefix="react-select-"+(i.props.instanceId||++xy),i.state.selectValue=tc(r.value),r.menuIsOpen&&i.state.selectValue.length){var o=i.getFocusableOptionsWithIds(),s=i.buildFocusableOptions(),a=s.indexOf(i.state.selectValue[0]);i.state.focusableOptionsWithIds=o,i.state.focusedOption=s[a],i.state.focusedOptionId=ps(o,s[a])}return i}return vg(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&nc(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(i){var o=this.props,s=o.isDisabled,a=o.menuIsOpen,l=this.state.isFocused;(l&&!s&&i.isDisabled||l&&a&&!i.menuIsOpen)&&this.focusInput(),l&&s&&!i.isDisabled?this.setState({isFocused:!1},this.onMenuClose):!l&&!s&&i.isDisabled&&this.inputRef===document.activeElement&&this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(nc(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(i,o){this.props.onInputChange(i,o)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(i){var o=this,s=this.state,a=s.selectValue,l=s.isFocused,u=this.buildFocusableOptions(),c=i==="first"?0:u.length-1;if(!this.props.isMulti){var f=u.indexOf(a[0]);f>-1&&(c=f)}this.scrollToFocusedOptionOnUpdate=!(l&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:u[c],focusedOptionId:this.getFocusedOptionId(u[c])},function(){return o.onMenuOpen()})}},{key:"focusValue",value:function(i){var o=this.state,s=o.selectValue,a=o.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var l=s.indexOf(a);a||(l=-1);var u=s.length-1,c=-1;if(s.length){switch(i){case"previous":l===0?c=0:l===-1?c=u:c=l-1;break;case"next":l>-1&&l<u&&(c=l+1);break}this.setState({inputIsHidden:c!==-1,focusedValue:s[c]})}}}},{key:"focusOption",value:function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"first",o=this.props.pageSize,s=this.state.focusedOption,a=this.getFocusableOptions();if(a.length){var l=0,u=a.indexOf(s);s||(u=-1),i==="up"?l=u>0?u-1:a.length-1:i==="down"?l=(u+1)%a.length:i==="pageup"?(l=u-o,l<0&&(l=0)):i==="pagedown"?(l=u+o,l>a.length-1&&(l=a.length-1)):i==="last"&&(l=a.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:a[l],focusedValue:null,focusedOptionId:this.getFocusedOptionId(a[l])})}}},{key:"getTheme",value:function(){return this.props.theme?typeof this.props.theme=="function"?this.props.theme(ms):Le(Le({},ms),this.props.theme):ms}},{key:"getCommonProps",value:function(){var i=this.clearValue,o=this.cx,s=this.getStyles,a=this.getClassNames,l=this.getValue,u=this.selectOption,c=this.setValue,f=this.props,g=f.isMulti,h=f.isRtl,m=f.options,p=this.hasValue();return{clearValue:i,cx:o,getStyles:s,getClassNames:a,getValue:l,hasValue:p,isMulti:g,isRtl:h,options:m,selectOption:u,selectProps:f,setValue:c,theme:this.getTheme()}}},{key:"hasValue",value:function(){var i=this.state.selectValue;return i.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var i=this.props,o=i.isClearable,s=i.isMulti;return o===void 0?s:o}},{key:"isOptionDisabled",value:function(i,o){return Cf(this.props,i,o)}},{key:"isOptionSelected",value:function(i,o){return Sf(this.props,i,o)}},{key:"filterOption",value:function(i,o){return xf(this.props,i,o)}},{key:"formatOptionLabel",value:function(i,o){if(typeof this.props.formatOptionLabel=="function"){var s=this.props.inputValue,a=this.state.selectValue;return this.props.formatOptionLabel(i,{context:o,inputValue:s,selectValue:a})}else return this.getOptionLabel(i)}},{key:"formatGroupLabel",value:function(i){return this.props.formatGroupLabel(i)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var i=this.props,o=i.isDisabled,s=i.isSearchable,a=i.inputId,l=i.inputValue,u=i.tabIndex,c=i.form,f=i.menuIsOpen,g=i.required,h=this.getComponents(),m=h.Input,p=this.state,w=p.inputIsHidden,b=p.ariaSelection,v=this.commonProps,S=a||this.getElementId("input"),O=Le(Le(Le({"aria-autocomplete":"list","aria-expanded":f,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":g,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},f&&{"aria-controls":this.getElementId("listbox")}),!s&&{"aria-readonly":!0}),this.hasValue()?b?.action==="initial-input-focus"&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return s?d.createElement(m,Ne({},v,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:S,innerRef:this.getInputRef,isDisabled:o,isHidden:w,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:l},O)):d.createElement(jw,Ne({id:S,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:fa,onFocus:this.onInputFocus,disabled:o,tabIndex:u,inputMode:"none",form:c,value:""},O))}},{key:"renderPlaceholderOrValue",value:function(){var i=this,o=this.getComponents(),s=o.MultiValue,a=o.MultiValueContainer,l=o.MultiValueLabel,u=o.MultiValueRemove,c=o.SingleValue,f=o.Placeholder,g=this.commonProps,h=this.props,m=h.controlShouldRenderValue,p=h.isDisabled,w=h.isMulti,b=h.inputValue,v=h.placeholder,S=this.state,O=S.selectValue,R=S.focusedValue,M=S.isFocused;if(!this.hasValue()||!m)return b?null:d.createElement(f,Ne({},g,{key:"placeholder",isDisabled:p,isFocused:M,innerProps:{id:this.getElementId("placeholder")}}),v);if(w)return O.map(function(E,k){var F=E===R,D="".concat(i.getOptionLabel(E),"-").concat(i.getOptionValue(E));return d.createElement(s,Ne({},g,{components:{Container:a,Label:l,Remove:u},isFocused:F,isDisabled:p,key:D,index:k,removeProps:{onClick:function(){return i.removeValue(E)},onTouchEnd:function(){return i.removeValue(E)},onMouseDown:function(I){I.preventDefault()}},data:E}),i.formatOptionLabel(E,"value"))});if(b)return null;var _=O[0];return d.createElement(c,Ne({},g,{data:_,isDisabled:p}),this.formatOptionLabel(_,"value"))}},{key:"renderClearIndicator",value:function(){var i=this.getComponents(),o=i.ClearIndicator,s=this.commonProps,a=this.props,l=a.isDisabled,u=a.isLoading,c=this.state.isFocused;if(!this.isClearable()||!o||l||!this.hasValue()||u)return null;var f={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return d.createElement(o,Ne({},s,{innerProps:f,isFocused:c}))}},{key:"renderLoadingIndicator",value:function(){var i=this.getComponents(),o=i.LoadingIndicator,s=this.commonProps,a=this.props,l=a.isDisabled,u=a.isLoading,c=this.state.isFocused;if(!o||!u)return null;var f={"aria-hidden":"true"};return d.createElement(o,Ne({},s,{innerProps:f,isDisabled:l,isFocused:c}))}},{key:"renderIndicatorSeparator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator,s=i.IndicatorSeparator;if(!o||!s)return null;var a=this.commonProps,l=this.props.isDisabled,u=this.state.isFocused;return d.createElement(s,Ne({},a,{isDisabled:l,isFocused:u}))}},{key:"renderDropdownIndicator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator;if(!o)return null;var s=this.commonProps,a=this.props.isDisabled,l=this.state.isFocused,u={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return d.createElement(o,Ne({},s,{innerProps:u,isDisabled:a,isFocused:l}))}},{key:"renderMenu",value:function(){var i=this,o=this.getComponents(),s=o.Group,a=o.GroupHeading,l=o.Menu,u=o.MenuList,c=o.MenuPortal,f=o.LoadingMessage,g=o.NoOptionsMessage,h=o.Option,m=this.commonProps,p=this.state.focusedOption,w=this.props,b=w.captureMenuScroll,v=w.inputValue,S=w.isLoading,O=w.loadingMessage,R=w.minMenuHeight,M=w.maxMenuHeight,_=w.menuIsOpen,E=w.menuPlacement,k=w.menuPosition,F=w.menuPortalTarget,D=w.menuShouldBlockScroll,C=w.menuShouldScrollIntoView,I=w.noOptionsMessage,T=w.onMenuScrollToTop,x=w.onMenuScrollToBottom;if(!_)return null;var $=function(ae,le){var fe=ae.type,re=ae.data,H=ae.isDisabled,P=ae.isSelected,G=ae.label,ue=ae.value,he=p===re,ke=H?void 0:function(){return i.onOptionHover(re)},Se=H?void 0:function(){return i.selectOption(re)},et="".concat(i.getElementId("option"),"-").concat(le),Re={id:et,onClick:Se,onMouseMove:ke,onMouseOver:ke,tabIndex:-1,role:"option","aria-selected":i.isAppleDevice?void 0:P};return d.createElement(h,Ne({},m,{innerProps:Re,data:re,isDisabled:H,isSelected:P,key:et,label:G,type:fe,value:ue,isFocused:he,innerRef:he?i.getFocusedOptionRef:void 0}),i.formatOptionLabel(ae.data,"menu"))},q;if(this.hasOptions())q=this.getCategorizedOptions().map(function(te){if(te.type==="group"){var ae=te.data,le=te.options,fe=te.index,re="".concat(i.getElementId("group"),"-").concat(fe),H="".concat(re,"-heading");return d.createElement(s,Ne({},m,{key:re,data:ae,options:le,Heading:a,headingProps:{id:H,data:te.data},label:i.formatGroupLabel(te.data)}),te.options.map(function(P){return $(P,"".concat(fe,"-").concat(P.index))}))}else if(te.type==="option")return $(te,"".concat(te.index))});else if(S){var X=O({inputValue:v});if(X===null)return null;q=d.createElement(f,m,X)}else{var oe=I({inputValue:v});if(oe===null)return null;q=d.createElement(g,m,oe)}var Q={minMenuHeight:R,maxMenuHeight:M,menuPlacement:E,menuPosition:k,menuShouldScrollIntoView:C},J=d.createElement(Ib,Ne({},m,Q),function(te){var ae=te.ref,le=te.placerProps,fe=le.placement,re=le.maxHeight;return d.createElement(l,Ne({},m,Q,{innerRef:ae,innerProps:{onMouseDown:i.onMenuMouseDown,onMouseMove:i.onMenuMouseMove},isLoading:S,placement:fe}),d.createElement(ty,{captureEnabled:b,onTopArrive:T,onBottomArrive:x,lockEnabled:D},function(H){return d.createElement(u,Ne({},m,{innerRef:function(G){i.getMenuListRef(G),H(G)},innerProps:{role:"listbox","aria-multiselectable":m.isMulti,id:i.getElementId("listbox")},isLoading:S,maxHeight:re,focusedOption:p}),q)}))});return F||k==="fixed"?d.createElement(c,Ne({},m,{appendTo:F,controlElement:this.controlRef,menuPlacement:E,menuPosition:k}),J):J}},{key:"renderFormField",value:function(){var i=this,o=this.props,s=o.delimiter,a=o.isDisabled,l=o.isMulti,u=o.name,c=o.required,f=this.state.selectValue;if(c&&!this.hasValue()&&!a)return d.createElement(iy,{name:u,onFocus:this.onValueInputFocus});if(!(!u||a))if(l)if(s){var g=f.map(function(p){return i.getOptionValue(p)}).join(s);return d.createElement("input",{name:u,type:"hidden",value:g})}else{var h=f.length>0?f.map(function(p,w){return d.createElement("input",{key:"i-".concat(w),name:u,type:"hidden",value:i.getOptionValue(p)})}):d.createElement("input",{name:u,type:"hidden",value:""});return d.createElement("div",null,h)}else{var m=f[0]?this.getOptionValue(f[0]):"";return d.createElement("input",{name:u,type:"hidden",value:m})}}},{key:"renderLiveRegion",value:function(){var i=this.commonProps,o=this.state,s=o.ariaSelection,a=o.focusedOption,l=o.focusedValue,u=o.isFocused,c=o.selectValue,f=this.getFocusableOptions();return d.createElement(Ww,Ne({},i,{id:this.getElementId("live-region"),ariaSelection:s,focusedOption:a,focusedValue:l,isFocused:u,selectValue:c,focusableOptions:f,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var i=this.getComponents(),o=i.Control,s=i.IndicatorsContainer,a=i.SelectContainer,l=i.ValueContainer,u=this.props,c=u.className,f=u.id,g=u.isDisabled,h=u.menuIsOpen,m=this.state.isFocused,p=this.commonProps=this.getCommonProps();return d.createElement(a,Ne({},p,{className:c,innerProps:{id:f,onKeyDown:this.onKeyDown},isDisabled:g,isFocused:m}),this.renderLiveRegion(),d.createElement(o,Ne({},p,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:g,isFocused:m,menuIsOpen:h}),d.createElement(l,Ne({},p,{isDisabled:g}),this.renderPlaceholderOrValue(),this.renderInput()),d.createElement(s,Ne({},p,{isDisabled:g}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(i,o){var s=o.prevProps,a=o.clearFocusValueOnUpdate,l=o.inputIsHiddenAfterUpdate,u=o.ariaSelection,c=o.isFocused,f=o.prevWasFocused,g=o.instancePrefix,h=i.options,m=i.value,p=i.menuIsOpen,w=i.inputValue,b=i.isMulti,v=tc(m),S={};if(s&&(m!==s.value||h!==s.options||p!==s.menuIsOpen||w!==s.inputValue)){var O=p?yy(i,v):[],R=p?pc(Qo(i,v),"".concat(g,"-option")):[],M=a?Cy(o,v):null,_=Sy(o,O),E=ps(R,_);S={selectValue:v,focusedOption:_,focusedOptionId:E,focusableOptionsWithIds:R,focusedValue:M,clearFocusValueOnUpdate:!1}}var k=l!=null&&i!==s?{inputIsHidden:l,inputIsHiddenAfterUpdate:void 0}:{},F=u,D=c&&f;return c&&!D&&(F={value:Yo(b,v,v[0]||null),options:v,action:"initial-input-focus"},D=!f),u?.action==="initial-input-focus"&&(F=null),Le(Le(Le({},S),k),{},{prevProps:i,ariaSelection:F,prevWasFocused:D})}}]),n}(d.Component);Mf.defaultProps=wy;var ky=d.forwardRef(function(e,t){var n=j1(e);return d.createElement(Mf,Ne({ref:t},n))}),My=ky;const Ry=e=>{const{Menu:t}=gf,{children:n,...r}=e;return d.createElement(t,{...r},n)},Ey=fn("div")({name:"Wrap",class:"gdg-wghi2zc",propsAsIs:!1}),Iy=fn("div")({name:"PortalWrap",class:"gdg-p13nj8j0",propsAsIs:!1}),Ty=fn("div")({name:"ReadOnlyWrap",class:"gdg-r6sia3g",propsAsIs:!1}),Dy=e=>{const{value:t,onFinishedEditing:n,initialValue:r}=e,{allowedValues:i,value:o}=t.data,[s,a]=d.useState(o),[l,u]=d.useState(r??""),c=qm(),f=d.useMemo(()=>i.map(g=>typeof g=="string"||g===null||g===void 0?{value:g,label:g?.toString()??""}:g),[i]);return t.readonly?d.createElement(Ty,null,d.createElement(Wr,{highlight:!0,autoFocus:!1,disabled:!0,value:s??"",onChange:()=>{}})):d.createElement(Ey,null,d.createElement(My,{className:"glide-select",inputValue:l,onInputChange:u,menuPlacement:"auto",value:f.find(g=>g.value===s),styles:{control:g=>({...g,border:0,boxShadow:"none"}),option:(g,{isFocused:h})=>({...g,fontSize:c.editorFontSize,fontFamily:c.fontFamily,cursor:h?"pointer":void 0,paddingLeft:c.cellHorizontalPadding,paddingRight:c.cellHorizontalPadding,":active":{...g[":active"],color:c.accentFg},":empty::after":{content:'"&nbsp;"',visibility:"hidden"}})},theme:g=>({...g,colors:{...g.colors,neutral0:c.bgCell,neutral5:c.bgCell,neutral10:c.bgCell,neutral20:c.bgCellMedium,neutral30:c.bgCellMedium,neutral40:c.bgCellMedium,neutral50:c.textLight,neutral60:c.textMedium,neutral70:c.textMedium,neutral80:c.textDark,neutral90:c.textDark,neutral100:c.textDark,primary:c.accentColor,primary75:c.accentColor,primary50:c.accentColor,primary25:c.accentLight}}),menuPortalTarget:document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:g=>d.createElement(Iy,null,d.createElement(Ry,{className:"click-outside-ignore",...g}))},options:f,onChange:async g=>{g!==null&&(a(g.value),await new Promise(h=>window.requestAnimationFrame(h)),n({...t,data:{...t.data,value:g.value}}))}}))},Oy={kind:Z.Custom,isMatch:e=>e.data.kind==="dropdown-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{value:o}=t.data,s=t.data.allowedValues.find(l=>typeof l=="string"||l===null||l===void 0?l===o:l.value===o),a=typeof s=="string"?s:s?.label??"";return a&&(n.fillStyle=r.textDark,n.fillText(a,i.x+r.cellHorizontalPadding,i.y+i.height/2+Zn(n,r))),!0},measure:(e,t,n)=>{const{value:r}=t.data;return(r?e.measureText(r).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Dy,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,value:""}})}),onPaste:(e,t)=>({...t,value:t.allowedValues.some(n=>n==null?!1:typeof n=="string"?n===e:n.value===e)?e:t.value})};function Py(e,t){const n=/(\d+\.?\d*)\s*(px|rem|em|%|pt)/,r=e.match(n);if(r){const i=parseFloat(r[1]),o=r[2],s=i*t;return e.replace(n,`${Number(s.toPrecision(3))}${o}`)}return e}const _y={marginRight:8},Fy={display:"flex",alignItems:"center",flexGrow:1},Ly={kind:Z.Custom,isMatch:e=>e.data.kind==="range-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{min:o,max:s,value:a,label:l,measureLabel:u}=t.data,c=i.x+r.cellHorizontalPadding,f=i.y+i.height/2,g=s-o,h=(a-o)/g,m=`${Py(r.baseFontStyle,.9)} ${r.fontFamily}`,w=Us(n,m)/2;n.save();let b=0;l!==void 0&&(n.font=m,b=qr(u??l,n,m).width+r.cellHorizontalPadding);const v=i.width-r.cellHorizontalPadding*2-b;if(v>=w){const S=n.createLinearGradient(c,f,c+v,f);S.addColorStop(0,r.accentColor),S.addColorStop(h,r.accentColor),S.addColorStop(h,r.bgBubble),S.addColorStop(1,r.bgBubble),n.beginPath(),n.fillStyle=S,Ku(n,c,f-w/2,v,w,w/2),n.fill(),n.beginPath(),Ku(n,c+.5,f-w/2+.5,v-1,w-1,(w-1)/2),n.strokeStyle=r.accentLight,n.lineWidth=1,n.stroke()}return l!==void 0&&(n.textAlign="right",n.fillStyle=r.textDark,n.fillText(l,i.x+i.width-r.cellHorizontalPadding,f+Zn(n,m))),n.restore(),!0},provideEditor:()=>e=>{const{data:t,readonly:n}=e.value,r=t.value.toString(),i=t.min.toString(),o=t.max.toString(),s=t.step.toString(),a=l=>{e.onChange({...e.value,data:{...t,value:Number(l.target.value)}})};return d.createElement("label",{style:Fy},d.createElement("input",{style:_y,type:"range",value:r,min:i,max:o,step:s,onChange:a,disabled:n}),r)},onPaste:(e,t)=>{let n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),{...t,value:n}}},Ay=fn("input")({name:"StyledInputBox",class:"gdg-s1wtovjx",propsAsIs:!1}),vs=(e,t,n)=>{if(t==null)return"";n&&(t=new Date(t.getTime()+n));const r=t.toISOString();switch(e){case"date":return r.split("T")[0];case"datetime-local":return r.replace("Z","");case"time":return r.split("T")[1].replace("Z","");default:throw new Error(`Unknown date kind ${e}`)}},Hy=e=>{const t=e.value.data,{format:n,displayDate:r}=t,i=t.step!==void 0&&!Number.isNaN(Number(t.step))?Number(t.step):void 0,o=t.timezoneOffset?t.timezoneOffset*60*1e3:0,s=t.min instanceof Date?vs(n,t.min,o):t.min,a=t.max instanceof Date?vs(n,t.max,o):t.max,l=vs(n,t.date,o);return e.value.readonly?Nt.createElement(Wr,{highlight:!0,autoFocus:!1,disabled:!0,value:r??"",onChange:()=>{}}):Nt.createElement(Ay,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:l,min:s,max:a,step:i,autoFocus:!0,onChange:u=>{isNaN(u.target.valueAsNumber)?e.onChange({...e.value,data:{...e.value.data,date:void 0}}):e.onChange({...e.value,data:{...e.value.data,date:new Date(u.target.valueAsNumber-o)}})}})},zy={kind:Z.Custom,isMatch:e=>e.data.kind==="date-picker-cell",draw:(e,t)=>{const{displayDate:n}=t.data;return Ws(e,n,t.contentAlign),!0},measure:(e,t,n)=>{const{displayDate:r}=t.data;return e.measureText(r).width+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Hy}),onPaste:(e,t)=>{let n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),t.format==="time"&&Number.isNaN(n)&&(n=Date.parse(`1970-01-01T${e}Z`)))),{...t,date:Number.isNaN(n)?void 0:new Date(n)}}},Vy="None";function bc(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-n.cellHorizontalPadding,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+n.cellHorizontalPadding),e.fillStyle=n.accentColor,e.fill(),e.restore()}const Ny=e=>{const{cell:t,theme:n,ctx:r}=e;Ws({...e,theme:{...n,textDark:n.textLight,headerFontFull:`${n.headerFontStyle} ${n.fontFamily}`,baseFontFull:`${n.baseFontStyle} ${n.fontFamily}`,markerFontFull:`${n.markerFontStyle} ${n.fontFamily}`}},Vy,t.contentAlign),r.fillStyle=n.textDark};function $y(e){const t=d.useCallback((r,i)=>{const{cell:o,theme:s,ctx:a,rect:l}=r,u=r.col;if(fi(o))bc(a,l,s);else if(wa(o)&&u<e.length){const c=e[u];["checkbox","line_chart","bar_chart","progress"].includes(c.kind)?i():Ny(r),c.isRequired&&c.isEditable&&bc(a,l,s);return}i()},[e]),n=d.useMemo(()=>[Y1,Oy,Ly,zy,...C1],[]);return{drawCell:t,customRenderers:n}}function By(){const e=$r();return d.useMemo(()=>{const n={editable:i=>`<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="${i.bgColor}"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>`};return{glideTheme:{accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:ai(e.colors.primary,.9),borderColor:e.colors.dataframeBorderColor,horizontalBorderColor:e.colors.dataframeBorderColor,fontFamily:e.genericFonts.bodyFont,bgSearchResult:ai(e.colors.primary,.9),resizeIndicatorColor:e.colors.primary,bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.dataframeHeaderBackgroundColor,bgHeaderHasFocus:ai(e.colors.darkenedBgMix100,.9),bgHeaderHovered:ai(e.colors.darkenedBgMix100,.9),textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerIconSize:Math.round(cn("1.125rem")),headerFontStyle:`${e.fontWeights.normal} ${cn(e.fontSizes.sm)}px`,baseFontStyle:`${e.fontWeights.normal} ${cn(e.fontSizes.sm)}px`,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:ai(e.colors.bodyText,.2),textLight:e.colors.fadedText40,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:Math.round(cn(e.spacing.sm)),cellVerticalPadding:Math.round(cn("0.1875rem")),textBubble:e.colors.fadedText60,bgBubble:e.colors.secondaryBg,bgBubbleSelected:e.colors.secondaryBg,bubbleHeight:Math.round(cn("1.25rem")),bubblePadding:Math.round(cn(e.spacing.sm)),bubbleMargin:Math.round(cn(e.spacing.twoXS)),linkColor:e.colors.link,drilldownBorder:e.colors.darkenedBgMix25,checkboxMaxSize:Math.round(cn(e.sizes.checkbox))},tableBorderRadius:e.radii.default,tableBorderWidth:1,defaultTableHeight:Math.round(cn("25rem")),minColumnWidth:Math.round(cn("3.125rem")),maxColumnWidth:Math.round(cn("62.5rem")),maxColumnAutoWidth:Math.round(cn("31.25rem")),defaultRowHeight:Math.round(cn("2.1875rem")),defaultHeaderHeight:Math.round(cn("2.1875rem")),bgRowHovered:Jh(e.colors.bgColor,e.colors.secondaryBg,.3),headerIcons:n}},[e])}const Wy=Fs.getLogger("useDataEditor");function Uy(e,t,n,r,i,o,s,a,l){const u=d.useCallback(([p,w],b)=>{const v=e[p];if(!v.isEditable)return;const S=v.indexNumber,O=n.current.getOriginalRowIndex(i(w)),R=r([p,w]),M=v.getCellValue(R),_=v.getCellValue(b);if(!fi(R)&&_===M)return;const E=v.getCell(_,!0);fi(E)?Wy.warn(`Not applying the cell edit since it causes this error:
 ${E.data}`):(n.current.setCell(S,O,{...E,lastUpdated:performance.now()}),a())},[e,n,i,r,a]),c=d.useCallback(()=>{if(t)return;const p=new Map;e.forEach(w=>{p.set(w.indexNumber,w.getCell(w.defaultValue))}),n.current.addRow(p),s()},[e,n,t,s]),f=d.useCallback(()=>{t||(c(),a())},[c,a,t]),g=d.useCallback(p=>{if(p.rows.length>0){if(t)return!0;const w=p.rows.toArray().map(b=>n.current.getOriginalRowIndex(i(b)));return n.current.deleteRows(w),s(),l(),a(),!1}if(p.current?.range){const w=[],b=p.current.range;for(let v=b.y;v<b.y+b.height;v++)for(let S=b.x;S<b.x+b.width;S++){const O=e[S];O.isEditable&&!O.isRequired&&(w.push({cell:[S,v]}),u([S,v],O.getCell(null)))}return w.length>0&&(a(),o(w)),!1}return!0},[e,n,t,o,i,a,u,l,s]),h=d.useCallback((p,w)=>{const[b,v]=p,S=[];for(let O=0;O<w.length;O++){const R=w[O];if(O+v>=n.current.getNumRows()){if(t)break;c()}for(let M=0;M<R.length;M++){const _=R[M],E=O+v,k=M+b;if(k>=e.length)break;const F=e[k];if(F.isEditable){const D=F.getCell(_,!0);if(st(D)&&!fi(D)){const C=F.indexNumber,I=n.current.getOriginalRowIndex(i(E)),T=F.getCellValue(r([k,E]));F.getCellValue(D)!==T&&(n.current.setCell(C,I,{...D,lastUpdated:performance.now()}),S.push({cell:[k,E]}))}}}S.length>0&&(a(),o(S))}return!1},[e,n,t,i,r,c,a,o]),m=d.useCallback((p,w)=>{const b=p[0];if(b>=e.length)return!0;const v=e[b];if(v.validateInput){const S=v.validateInput(v.getCellValue(w));return S===!0||S===!1?S:v.getCell(S)}return!0},[e]);return{onCellEdited:u,onPaste:h,onRowAppended:f,onDelete:g,validateCell:m}}const Rf=",",Xi='"',qy='"',Ef=`
`,Gy="\uFEFF",Yy=new RegExp(`[${[Rf,Xi,Ef].join("")}]`),wc=Fs.getLogger("useDataExporter");function yc(e){return e.map(t=>Xy(t)).join(Rf)+Ef}function Xy(e){if(Ae(e))return"";const t=gt(e);return Yy.test(t)?`${Xi}${t.replace(new RegExp(Xi,"g"),qy+Xi)}${Xi}`:t}async function Cc(e,t,n,r){const i=new TextEncoder;await e.write(i.encode(Gy));const o=n.map(s=>s.name);await e.write(i.encode(yc(o)));for(let s=0;s<r;s++){const a=[];n.forEach((l,u,c)=>{a.push(l.getCellValue(t([u,s])))}),await e.write(i.encode(yc(a)))}await e.close()}function jy(e,t,n,r){return{exportToCsv:d.useCallback(async()=>{const s=`${new Date().toISOString().slice(0,16).replace(":","-")}_export.csv`;try{const u=await(await(await _s(()=>import("./es6.CVz13CSz.js").then(c=>c.a),__vite__mapDeps([15,1,2]),import.meta.url)).showSaveFilePicker({suggestedName:s,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1})).createWritable();await Cc(u,e,t,n)}catch(a){if(a instanceof Error&&a.name==="AbortError")return;try{wc.warn("Failed to export data as CSV with FileSystem API, trying fallback method",a);let l="";const u=new WritableStream({write:h=>{l+=new TextDecoder("utf-8").decode(h)},close:async()=>{}});await Cc(u.getWriter(),e,t,n);const c=new Blob([l],{type:"text/csv;charset=utf-8;"}),f=URL.createObjectURL(c),g=gg({enforceDownloadInNewTab:r,url:f,filename:s});g.style.display="none",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(f)}catch(l){wc.error("Failed to export data as CSV",l)}}},[t,n,e,r])}}function Ky(e,t,n,r){return{getCellContent:d.useCallback(([o,s])=>{if(o>t.length-1)return It("Column index out of bounds","This error should never happen. Please report this bug.");if(s>n-1)return It("Row index out of bounds","This error should never happen. Please report this bug.");try{const a=t[o],l=a.indexNumber,u=r.current.getOriginalRowIndex(s),c=r.current.isAddedRow(u);if(a.isEditable||c){const h=r.current.getCell(l,u);if(st(h))return{...a.getCell(a.getCellValue(h),!1),lastUpdated:h.lastUpdated};if(c)return It("Error during cell creation",`This error should never happen. Please report this bug. No cell found for an added row: col=${l}; row=${u}`)}const f=e.getCell(u,l),g=Qh(e,u,l);return O1(a,f,g,e.styler?.cssStyles)}catch(a){return It("Error during cell creation",`This error should never happen. Please report this bug. 
Error: ${a}`)}},[t,n,e,r])}}function Zy(e){const[t,n]=d.useState(void 0),r=d.useCallback(o=>{if(o.kind!=="cell")n(void 0);else{const[,s]=o.location;n(s)}},[n]);return{getRowThemeOverride:d.useCallback(o=>{if(o===t)return{bgCell:e.bgRowHovered,bgCellMedium:e.bgRowHovered}},[e.bgRowHovered,t]),onItemHovered:r}}function Jy(e,t,n,r,i){const[o,s]=d.useState({columns:rt.empty(),rows:rt.empty(),current:void 0}),a=!t&&!n&&(e.selectionMode.includes(Cn.SelectionMode.MULTI_ROW)||e.selectionMode.includes(Cn.SelectionMode.SINGLE_ROW)),l=a&&e.selectionMode.includes(Cn.SelectionMode.MULTI_ROW),u=!t&&!n&&(e.selectionMode.includes(Cn.SelectionMode.SINGLE_COLUMN)||e.selectionMode.includes(Cn.SelectionMode.MULTI_COLUMN)),c=u&&e.selectionMode.includes(Cn.SelectionMode.MULTI_COLUMN),f=o.rows.length>0,g=o.columns.length>0,h=o.current!==void 0,m=d.useCallback(w=>{const b=!Wa(w.rows.toArray(),o.rows.toArray()),v=!Wa(w.columns.toArray(),o.columns.toArray()),S=!Wa(w.current,o.current);let O=a&&b||u&&v,R=w;if((a||u)&&w.current!==void 0&&S&&(R={...w,rows:o.rows,columns:o.columns},O=!1),b&&w.rows.length>0&&v&&w.columns.length===0&&(R={...R,columns:o.columns},O=!0),v&&w.columns.length>0&&b&&w.rows.length===0&&(R={...R,rows:o.rows},O=!0),v&&R.columns.length>=0){let M=R.columns;r.forEach((_,E)=>{_.isIndex&&(M=M.remove(E))}),M.length<R.columns.length&&(R={...R,columns:M})}s(R),O&&i(R)},[o,a,u,i,r]),p=d.useCallback((w=!1,b=!1)=>{const v={columns:b?o.columns:rt.empty(),rows:w?o.rows:rt.empty(),current:void 0};s(v),(!w&&a||!b&&u)&&i(v)},[o,a,u,i]);return{gridSelection:o,isRowSelectionActivated:a,isMultiRowSelectionActivated:l,isColumnSelectionActivated:u,isMultiColumnSelectionActivated:c,isRowSelected:f,isColumnSelected:g,isCellSelected:h,clearSelection:p,processSelectionChange:m}}function Qy(e,t,n,r,i,o,s){const a=e.rowHeight??t.defaultRowHeight,l=t.defaultHeaderHeight+a+2*t.tableBorderWidth,u=r?2:1,c=e.editingMode===Cn.EditingMode.DYNAMIC?1:0,f=n+c;let g=Math.max(f*a+u*t.defaultHeaderHeight+2*t.tableBorderWidth,l),h=Math.min(g,t.defaultTableHeight);e.height&&(h=Math.max(e.height,l),g=Math.max(e.height,g)),o&&(h=Math.min(h,o),g=Math.min(g,o),e.height||(h=g));const m=t.minColumnWidth+2*t.tableBorderWidth,p=Math.max(i,m);let w,b=p;e.useContainerWidth?w=p:e.width&&(w=Math.min(Math.max(e.width,m),p),b=Math.min(Math.max(e.width,b),p));const[v,S]=d.useState({width:w||"100%",height:h});return d.useLayoutEffect(()=>{e.useContainerWidth&&v.width==="100%"&&S(O=>({...O,width:p}))},[p]),d.useLayoutEffect(()=>{S(O=>({...O,width:w||"100%"}))},[w]),d.useLayoutEffect(()=>{S(O=>({...O,height:h}))},[h,n]),d.useLayoutEffect(()=>{if(s){const O=e.useContainerWidth||st(e.width)&&e.width>0;S({width:O?b:"100%",height:g})}else S({width:w||"100%",height:h})},[s]),{minHeight:l,maxHeight:g,minWidth:m,maxWidth:b,rowHeight:a,resizableSize:v,setResizableSize:S}}const eC=600,tC="⚠️ Please fill out this cell.";function nC(e,t,n=[]){const[r,i]=d.useState(),o=d.useRef(null),s=d.useCallback(l=>{if(clearTimeout(o.current),o.current=0,i(void 0),(l.kind==="header"||l.kind==="cell")&&l.location){const u=l.location[0],c=l.location[1];let f;if(u<0||u>=e.length||n.includes(c))return;const g=e[u];if(l.kind==="header"&&st(g))f=g.help;else if(l.kind==="cell"){const h=t([u,c]);fi(h)?f=h.errorDetails:g.isRequired&&g.isEditable&&wa(h)?f=tC:a1(h)&&(f=h.tooltip)}f&&(o.current=setTimeout(()=>{f&&i({content:f,left:l.bounds.x+l.bounds.width/2,top:l.bounds.y})},eC))}},[e,t,i,o,n]),a=d.useCallback(()=>{i(void 0)},[i]);return{tooltip:r,clearTooltip:a,onItemHovered:s}}function rC({top:e,left:t,content:n,clearTooltip:r}){const[i,o]=d.useState(!0),s=$r(),{colors:a,fontSizes:l,radii:u,fontWeights:c}=s,f=d.useCallback(()=>{o(!1),r()},[r,o]);return Qe(va,{content:Qe(eg,{"data-testid":"stDataFrameTooltipContent",children:Qe(tg,{style:{fontSize:l.sm},source:n,allowHTML:!1})}),placement:ma.top,accessibilityType:Tc.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:f,onEsc:f,overrides:{Body:{style:{borderTopLeftRadius:u.default,borderTopRightRadius:u.default,borderBottomLeftRadius:u.default,borderBottomRightRadius:u.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:pa(s)?a.bgColor:a.secondaryBg,color:a.bodyText,fontSize:l.sm,fontWeight:c.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:i,children:Qe("div",{"data-testid":"stDataFrameTooltipTarget",style:{position:"fixed",top:e,left:t}})})}const iC=d.memo(rC),Sc=mi("div",{target:"e1u61huh0"})(({hasCustomizedScrollbars:e,theme:t})=>({position:"relative",display:"inline-block","& .stDataFrameGlideDataEditor":{height:"100%",minWidth:"100%",borderRadius:t.radii.default},"& .dvn-scroller":{...!e&&{scrollbarWidth:"thin"},overflowX:"auto !important",overflowY:"auto !important"},"& .gdg-search-bar":{maxWidth:"19rem",width:"80%",minWidth:"6rem",top:t.spacing.sm,right:t.spacing.sm,padding:t.spacing.sm,borderRadius:t.radii.default,"& .gdg-search-status":{paddingTop:t.spacing.twoXS,fontSize:t.fontSizes.twoSm},"& .gdg-search-progress":{display:"none"},"& input":{width:"100%"},"& button":{width:t.iconSizes.xl,height:t.iconSizes.xl,"& .button-icon":{width:t.iconSizes.base,height:t.iconSizes.base}}}})),xc=150,oC=15e4,Xo=6;function aC({element:e,data:t,disabled:n,widgetMgr:r,disableFullscreenMode:i,fragmentId:o}){const{expanded:s,expand:a,collapse:l,width:u,height:c}=ng(sg),f=d.useRef(null),g=d.useRef(null),h=d.useRef(null),m=By(),{getRowThemeOverride:p,onItemHovered:w}=Zy(m),{libConfig:{enforceDownloadInNewTab:b=!1}}=d.useContext(rg),[v,S]=d.useState(!0),[O,R]=d.useState(!1),[M,_]=d.useState(!1),[E,k]=d.useState(!1),[F,D]=d.useState(),[C,I]=d.useState(!1),T=d.useCallback(()=>I(ge=>!ge),[]),x=d.useCallback(()=>I(!1),[]),$=d.useMemo(()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches,[]),q=d.useMemo(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")||window.navigator.userAgent.includes("Chrome"),[]);Ae(e.editingMode)&&(e.editingMode=Cn.EditingMode.READ_ONLY);const{READ_ONLY:X,DYNAMIC:oe}=Cn.EditingMode,Q=t.dimensions,J=Math.max(0,Q.numDataRows),te=J===0&&!(e.editingMode===oe&&Q.numDataColumns>0),ae=J>oC,le=!ae&&!te&&e.editingMode!==oe,fe=!te&&e.editingMode===oe&&!n,re=d.useRef(new Wo(J)),[H,P]=d.useState(re.current.getNumRows());d.useEffect(()=>{re.current=new Wo(J),P(re.current.getNumRows())},[J]);const G=d.useCallback(()=>{re.current=new Wo(J),P(re.current.getNumRows())},[J]),[ue,he]=d.useState(e.columnOrder);d.useEffect(()=>{he(e.columnOrder)},[e.columnOrder.join(",")]);const{columns:ke,allColumns:Se,setColumnConfigMapping:et}=F1(e,t,n,ue);d.useEffect(()=>{if(e.editingMode===X)return;const ge=r.getStringValue({id:e.id,formId:e.formId});ge&&(re.current.fromJson(ge,ke),P(re.current.getNumRows()))},[]);const{getCellContent:Re}=Ky(t,ke,H,re),{columns:Xe,sortColumn:yt,getOriginalIndex:De,getCellContent:ne}=U1(J,ke,Re),Ee=d.useCallback(ge=>{const bt={selection:{rows:[],columns:[]}};bt.selection.rows=ge.rows.toArray().map(Bt=>De(Bt)),bt.selection.columns=ge.columns.toArray().map(Bt=>Qi(Xe[Bt]));const en=JSON.stringify(bt),kt=r.getStringValue({id:e.id,formId:e.formId});(kt===void 0||kt!==en)&&r.setStringValue({id:e.id,formId:e.formId},en,{fromUi:!0},o)},[Xe,e.id,e.formId,r,o,De]),{debouncedCallback:xe}=Au(Ee,xc),{gridSelection:ce,isRowSelectionActivated:pe,isMultiRowSelectionActivated:ze,isColumnSelectionActivated:Ie,isMultiColumnSelectionActivated:tt,isRowSelected:Te,isColumnSelected:Oe,isCellSelected:Je,clearSelection:ye,processSelectionChange:qe}=Jy(e,te,n,Xe,xe);d.useEffect(()=>{ye(!0,!0)},[s]);const pt=d.useCallback(ge=>{g.current?.updateCells(ge)},[]);d.useEffect(()=>{if(!pe&&!Ie)return;const ge=r.getStringValue({id:e.id,formId:e.formId});if(ge){const bt=Xe.map(an=>Qi(an)),en=JSON.parse(ge);let kt=rt.empty(),Bt=rt.empty();en.selection?.rows?.forEach(an=>{kt=kt.add(an)}),en.selection?.columns?.forEach(an=>{Bt=Bt.add(bt.indexOf(an))}),(kt.length>0||Bt.length>0)&&qe({rows:kt,columns:Bt,current:void 0})}},[]);const ft=d.useCallback(()=>{H!==re.current.getNumRows()&&P(re.current.getNumRows())},[H]),We=d.useCallback(()=>{const ge=re.current.toJson(Xe);let bt=r.getStringValue({id:e.id,formId:e.formId});bt===void 0&&(bt=new Wo(0).toJson([])),ge!==bt&&r.setStringValue({id:e.id,formId:e.formId},ge,{fromUi:!0},o)},[Xe,e.id,e.formId,r,o]),{debouncedCallback:xt}=Au(We,xc),{exportToCsv:qt}=jy(ne,Xe,H,b),{onCellEdited:Tt,onPaste:_t,onRowAppended:tn,onDelete:zt,validateCell:Sn}=Uy(Xe,e.editingMode!==oe,re,ne,De,pt,ft,xt,ye),Vt=d.useMemo(()=>te?[0]:fe?[H]:[],[te,fe,H]),{tooltip:ut,clearTooltip:nn,onItemHovered:Ct}=nC(Xe,ne,Vt),{drawCell:Ft,customRenderers:Yt}=$y(Xe),{provideEditor:je}=G1(),Dt=d.useCallback(ge=>({...ge,hasMenu:!te}),[te]),Ot=d.useMemo(()=>Xe.map(ge=>Dt(Is(ge))),[Xe,Dt]),{columns:ln,onColumnResize:rn}=z1(Ot),hn=t.dimensions.numHeaderRows>1,{minHeight:bn,maxHeight:Xt,minWidth:Jt,maxWidth:Ge,rowHeight:Et,resizableSize:Qt,setResizableSize:Lt}=Qy(e,m,H,hn,u||0,c,s),At=d.useCallback(([ge,bt])=>({...s1(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:m.glideTheme.textLight},span:[0,Math.max(Xe.length-1,0)]}),[Xe,m.glideTheme.textLight]),xn=d.useCallback(()=>{G(),ye()},[G,ye]);og({element:e,widgetMgr:r,onFormCleared:xn});const{pinColumn:Un,unpinColumn:Hn,freezeColumns:N}=A1(Xe,te,u||0,m.minColumnWidth,ye,et),{changeColumnFormat:Be}=L1(et),{hideColumn:Pe,showColumn:Pt}=q1(ye,et),{onColumnMoved:wn}=H1(Xe,N,Un,Hn,he);return d.useEffect(()=>{setTimeout(()=>{if(h.current&&g.current){const ge=h.current?.querySelector(".dvn-stack")?.getBoundingClientRect();ge&&(_(ge.height>h.current.clientHeight),k(ge.width>h.current.clientWidth))}},1)},[Qt,H,ln]),d.useEffect(()=>{Se.length==Xe.length&&I(!1)},[Se.length,Xe.length]),In(Sc,{className:"stDataFrame","data-testid":"stDataFrame",hasCustomizedScrollbars:q,ref:h,onMouseDown:ge=>{if(h.current&&q){const bt=h.current.getBoundingClientRect();E&&bt.height-(Xo+1)<ge.clientY-bt.top&&ge.stopPropagation(),M&&bt.width-(Xo+1)<ge.clientX-bt.left&&ge.stopPropagation()}},onBlur:ge=>{!v&&!$&&!ge.currentTarget.contains(ge.relatedTarget)&&ye(!0,!0)},children:[In(lg,{isFullScreen:s,disableFullscreenMode:i,locked:Te&&!pe||Je||$&&v||C,onExpand:a,onCollapse:l,target:Sc,children:[(pe&&Te||Ie&&Oe)&&Qe(ii,{label:"Clear selection",icon:mg,onClick:()=>{ye(),nn()}}),fe&&Te&&Qe(ii,{label:"Delete row(s)",icon:yg,onClick:()=>{zt&&(zt(ce),nn())}}),fe&&!Te&&Qe(ii,{label:"Add row",icon:Hc,onClick:()=>{tn&&(S(!0),tn(),nn(),g.current?.scrollTo(0,H,"vertical"))}}),!te&&Se.length>Xe.length&&Qe(Qv,{columns:Se,columnOrder:ue,setColumnOrder:he,hideColumn:Pe,showColumn:Pt,isOpen:C,onClose:x,children:Qe(ii,{label:"Show/hide columns",icon:Vc,onClick:T})}),!ae&&!te&&Qe(ii,{label:"Download as CSV",icon:Cg,onClick:qt}),!te&&Qe(ii,{label:"Search",icon:zc,onClick:()=>{O?R(!1):(S(!0),R(!0)),nn()}})]}),Qe(ig,{"data-testid":"stDataFrameResizable",ref:f,defaultSize:Qt,style:{border:`${m.tableBorderWidth}px solid ${m.glideTheme.borderColor}`,borderRadius:`${m.tableBorderRadius}`},minHeight:bn,maxHeight:Xt,minWidth:Jt,maxWidth:Ge,size:Qt,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!0,bottomLeft:!1,topLeft:!1},grid:[1,Et],snapGap:Et/3,onResizeStop:(ge,bt,en,kt)=>{if(f.current){const Bt=2*m.tableBorderWidth;Lt({width:f.current.size.width,height:Xt-f.current.size.height===Bt?f.current.size.height+Bt:f.current.size.height})}},children:Qe(Wv,{className:"stDataFrameGlideDataEditor","data-testid":"stDataFrameGlideDataEditor",ref:g,columns:ln,rows:te?1:H,minColumnWidth:m.minColumnWidth,maxColumnWidth:m.maxColumnWidth,maxColumnAutoWidth:m.maxColumnAutoWidth,rowHeight:Et,headerHeight:m.defaultHeaderHeight,getCellContent:te?At:ne,onColumnResize:$?void 0:rn,resizeIndicator:"header",freezeColumns:N,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:!0,getCellsForSelection:!0,rowMarkers:"none",rangeSelect:$?"cell":"rect",columnSelect:"none",rowSelect:"none",onColumnMoved:Ie?void 0:wn,onItemHovered:ge=>{w?.(ge),Ct?.(ge)},keybindings:{downFill:!0},onKeyDown:ge=>{(ge.ctrlKey||ge.metaKey)&&ge.key==="f"&&(R(bt=>!bt),ge.stopPropagation(),ge.preventDefault())},showSearch:O,searchResults:O?void 0:[],onSearchClose:()=>{R(!1),nn()},onHeaderClicked:(ge,bt)=>{!le||Ie||(O&&R(!1),pe&&Te?ye():ye(!0,!0),yt(ge,"auto"))},gridSelection:ce,onGridSelectionChange:ge=>{(v||$)&&(qe(ge),ut!==void 0&&nn())},theme:m.glideTheme,getRowThemeOverride:p,onMouseMove:ge=>{ge.kind==="out-of-bounds"&&v?S(!1):ge.kind!=="out-of-bounds"&&!v&&S(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:0,...q&&{paddingBottom:E?-Xo:void 0,paddingRight:M?-Xo:void 0}},provideEditor:je,drawCell:Ft,customRenderers:Yt,imageEditorOverride:y1,headerIcons:m.headerIcons,validateCell:Sn,onHeaderMenuClick:(ge,bt)=>{D({columnIdx:ge,headerBounds:bt})},onPaste:!1,...pe&&{rowMarkers:{kind:"checkbox-visible",checkboxStyle:"square",theme:{bgCell:m.glideTheme.bgHeader,bgCellMedium:m.glideTheme.bgHeader,textMedium:m.glideTheme.textLight}},rowSelectionMode:ze?"multi":"auto",rowSelect:n?"none":ze?"multi":"single",rowSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...Ie&&{columnSelect:n?"none":tt?"multi":"single",columnSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...!te&&e.editingMode!==X&&!n&&{fillHandle:!$,onCellEdited:Tt,onPaste:_t,onDelete:zt},...!te&&e.editingMode===oe&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkers:{kind:"checkbox",checkboxStyle:"square",theme:{bgCell:m.glideTheme.bgHeader,bgCellMedium:m.glideTheme.bgHeader}},rowSelectionMode:"multi",rowSelect:n?"none":"multi",onRowAppended:n?void 0:tn,onHeaderClicked:void 0}})}),ut&&ut.content&&Qe(iC,{top:ut.top,left:ut.left,content:ut.content,clearTooltip:nn}),F&&Lc.createPortal(Qe(jv,{top:F.headerBounds.y+F.headerBounds.height,left:F.headerBounds.x+F.headerBounds.width,columnKind:ke[F.columnIdx].kind,onCloseMenu:()=>D(void 0),onSortColumn:le?ge=>{O&&R(!1),ye(!(pe&&Te),!0),yt(F.columnIdx,ge,!0)}:void 0,isColumnPinned:ke[F.columnIdx].isPinned,onUnpinColumn:()=>{Hn(ke[F.columnIdx].id)},onPinColumn:()=>{Un(ke[F.columnIdx].id)},onHideColumn:()=>{Pe(ke[F.columnIdx].id)},onChangeFormat:ge=>{Be(ke[F.columnIdx].id,ge),setTimeout(()=>{g.current?.remeasureColumns(rt.fromSingleSelection(F.columnIdx))},100)},onAutosize:()=>{g.current?.remeasureColumns(rt.fromSingleSelection(F.columnIdx))}}),document.querySelector("#portal"))]})}const sC=ag(aC),lC=d.memo(sC),CC=Object.freeze(Object.defineProperty({__proto__:null,default:lC},Symbol.toStringTag,{value:"Module"}));export{x0 as C,vd as T,ui as a,Eg as b,CC as c,si as i,Um as m,fn as s};
