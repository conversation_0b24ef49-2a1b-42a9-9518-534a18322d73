import{s as W,r,z as C,b8 as I,c3 as V,c4 as H,c5 as O,C as $,j as n,br as L,bH as P,bs as k,b9 as B,bt as E,c2 as F,c6 as N,b_ as D,c7 as A,bD as U}from"./index.CbQtRkVt.js";import{a as j}from"./useBasicWidgetState.Bx3VaRHk.js";import"./FormClearHelper.Cdw5Y7_m.js";const G=W("div",{target:"euzcfsp0"})(({theme:s})=>({"span[aria-disabled='true']":{background:s.colors.fadedText05}})),K=(s,t)=>s.getStringArrayValue(t),Y=s=>s.default.map(t=>s.options[t])??null,_=s=>s.rawValues??null,X=(s,t,a,c)=>{t.setStringArrayValue(s,a.value,{fromUi:a.fromUi},c)},q=s=>{const{element:t,widgetMgr:a,fragmentId:c}=s,e=C(),h=r.useContext(I),[i,p]=j({getStateFromWidgetMgr:K,getDefaultStateFromProto:Y,getCurrStateFromProto:_,updateWidgetMgrState:X,element:t,widgetMgr:a,fragmentId:c}),g=t.maxSelections>0&&i.length>=t.maxSelections,b=r.useMemo(()=>{if(t.maxSelections===0)return"No results";if(i.length===t.maxSelections){const o=t.maxSelections!==1?"options":"option";return`You can only select up to ${t.maxSelections} ${o}. Remove an option first.`}return"No results"},[t.maxSelections,i.length]),S=r.useMemo(()=>i.map(o=>({value:o,label:o})),[i]),m=r.useCallback(o=>{switch(o.type){case"remove":return V(i,o.option?.value);case"clear":return[];case"select":return i.concat([o.option?.value]);default:throw new Error(`State transition is unknown: ${o.type}`)}},[i]),f=r.useCallback(o=>{t.maxSelections&&o.type==="select"&&i.length>=t.maxSelections||p({value:m(o),fromUi:!0})},[t.maxSelections,m,p,i.length]),y=r.useCallback((o,l)=>{if(g)return[];const R=o.filter(T=>!i.includes(T.value));return H(R,l)},[g,i]),{options:d}=t,{placeholder:x,shouldDisable:v}=O(t.placeholder,d,t.acceptNewOptions??!1,!0),u=s.disabled||v,z=d.map((o,l)=>({label:o,value:o,id:`${o}_${l}`})),w=d.length>10,M=r.useMemo(()=>{const o=e.fontSizes.baseFontSize*1.6+14;return`${Math.round(o*4.25)}px`},[e.fontSizes.baseFontSize]);return $("div",{className:"stMultiSelect","data-testid":"stMultiSelect",children:[n(E,{label:t.label,disabled:u,labelVisibility:L(t.labelVisibility?.value),children:t.help&&n(P,{children:n(k,{content:t.help,placement:B.TOP_RIGHT})})}),n(G,{children:n(F,{creatable:t.acceptNewOptions??!1,options:z,labelKey:"label",valueKey:"value","aria-label":t.label,placeholder:x,type:A.select,multi:!0,onChange:f,value:S,disabled:u,size:"compact",noResultsMsg:b,filterOptions:y,closeOnSelect:!1,ignoreCase:!1,overrides:{Popover:{props:{ignoreBoundary:h,overrides:{Body:{style:()=>({marginTop:e.spacing.px})}}}},SelectArrow:{component:D,props:{style:{cursor:"pointer"},overrides:{Svg:{style:()=>({width:e.iconSizes.xl,height:e.iconSizes.xl})}}}},IconsContainer:{style:()=>({paddingRight:e.spacing.sm})},ControlContainer:{style:{maxHeight:M,minHeight:e.sizes.minElementHeight,borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth}},Placeholder:{style:()=>({flex:"inherit",color:u?e.colors.fadedText40:e.colors.fadedText60})},ValueContainer:{style:()=>({overflowY:"auto",paddingLeft:e.spacing.sm,paddingTop:e.spacing.none,paddingBottom:e.spacing.none,paddingRight:e.spacing.none})},ClearIcon:{props:{overrides:{Svg:{style:{color:e.colors.darkGray,padding:e.spacing.threeXS,height:e.sizes.clearIconSize,width:e.sizes.clearIconSize,cursor:"pointer",":hover":{fill:e.colors.bodyText}}}}}},SearchIcon:{style:{color:e.colors.darkGray}},Tag:{props:{overrides:{Root:{style:{fontWeight:e.fontWeights.normal,borderTopLeftRadius:e.radii.md,borderTopRightRadius:e.radii.md,borderBottomRightRadius:e.radii.md,borderBottomLeftRadius:e.radii.md,fontSize:e.fontSizes.md,paddingLeft:e.spacing.sm,marginLeft:e.spacing.none,marginRight:e.spacing.sm,height:`calc(${e.sizes.minElementHeight} - 2 * ${e.spacing.xs})`,maxWidth:`calc(100% - ${e.spacing.lg})`,cursor:"default !important"}},Action:{style:{paddingLeft:0}},ActionIcon:{props:{overrides:{Svg:{style:{width:"0.625em",height:"0.625em"}}}}}}}},MultiValue:{props:{overrides:{Root:{style:{fontSize:e.fontSizes.sm}}}}},Input:{props:{readOnly:U()&&w===!1?"readonly":null}},Dropdown:{component:N}}})})]})},ee=r.memo(q);export{ee as default};
