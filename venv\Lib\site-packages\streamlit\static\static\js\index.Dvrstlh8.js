import{s as l,r as o,L as A,j as f,l as g}from"./index.CbQtRkVt.js";const T=l("div",{target:"ea9qfvi0"})(()=>({lineHeight:0})),y=l("audio",{target:"ea9qfvi1"})(({theme:r})=>({width:"100%",height:r.sizes.minElementHeight,margin:0,padding:0})),L=g.getLogger("Audio");function h({element:r,endpoints:a,elementMgr:u}){const{libConfig:p}=o.useContext(A),i=o.useRef(null),{startTime:n,endTime:d,loop:s,autoplay:E}=r,m=o.useMemo(()=>{if(!r.id)return!0;const e=u.getElementState(r.id,"preventAutoplay");return e||u.setElementState(r.id,"preventAutoplay",!0),e??!1},[r.id,u]);o.useEffect(()=>{i.current&&(i.current.currentTime=n)},[n]),o.useEffect(()=>{const e=i.current,t=()=>{e&&(e.currentTime=r.startTime)};return e&&e.addEventListener("loadedmetadata",t),()=>{e&&e.removeEventListener("loadedmetadata",t)}},[r]),o.useEffect(()=>{const e=i.current;if(!e)return;let t=!1;const c=()=>{d>0&&e.currentTime>=d&&(s?(e.currentTime=n||0,e.play()):t||(t=!0,e.pause()))};return d>0&&e.addEventListener("timeupdate",c),()=>{e&&d>0&&e.removeEventListener("timeupdate",c)}},[d,s,n]),o.useEffect(()=>{const e=i.current;if(!e)return;const t=()=>{s&&(e.currentTime=n||0,e.play())};return e.addEventListener("ended",t),()=>{e&&e.removeEventListener("ended",t)}},[s,n]);const v=a.buildMediaURL(r.url);return f(T,{children:f(y,{className:"stAudio","data-testid":"stAudio",ref:i,controls:!0,autoPlay:E&&!m,src:v,onError:e=>{const t=e.currentTarget.src;L.error(`Client Error: Audio source error - ${t}`),a.sendClientErrorToHost("Audio","Audio source failed to load","onerror triggered",t)},crossOrigin:p.resourceCrossOriginMode})})}const S=o.memo(h);export{S as default};
