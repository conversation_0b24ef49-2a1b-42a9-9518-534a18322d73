import{bv as q,bw as J,r as c,bx as Q,z as Y,by as l,j as E,C as ee,br as te,bz as re,bs as ne,b9 as oe,bA as ie,bt as ae,bB as ue,bp as le,b4 as P,b3 as x,b6 as ce}from"./index.CbQtRkVt.js";import{a as se}from"./useBasicWidgetState.Bx3VaRHk.js";import"./FormClearHelper.Cdw5Y7_m.js";var fe={secondary:"secondary"},z={default:"default"},pe={default:"default"},k=Object.freeze({radio:"radio",checkbox:"checkbox"}),N=q("div",function(e){var t=e.$shape,r=e.$length,n=e.$theme,o=r===1?void 0:t!==z.default?"-".concat(n.sizing.scale100):"-0.5px";return{display:"flex",marginLeft:o,marginRight:o}});N.displayName="StyledRoot";N.displayName="StyledRoot";function M(e){"@babel/helpers - typeof";return M=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(e)}function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$.apply(this,arguments)}function X(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?X(Object(r),!0).forEach(function(n){V(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ye(e,t){return Se(e)||me(e,t)||he(e,t)||be()}function be(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function he(e,t){if(e){if(typeof e=="string")return K(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return K(e,t)}}function K(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function me(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,i=!1,a,d;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!(t&&n.length===t));o=!0);}catch(s){i=!0,d=s}finally{try{!o&&r.return!=null&&r.return()}finally{if(i)throw d}}return n}}function Se(e){if(Array.isArray(e))return e}function ge(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ve(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Oe(e,t,r){return t&&ve(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ee(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&G(e,t)}function G(e,t){return G=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},G(e,t)}function _e(e){var t=Le();return function(){var n=B(e),o;if(t){var i=B(this).constructor;o=Reflect.construct(n,arguments,i)}else o=n.apply(this,arguments);return we(this,o)}}function we(e,t){if(t&&(M(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return F(e)}function F(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Le(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function B(e){return B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},B(e)}function V(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Re(e,t){return!Array.isArray(e)&&typeof e!="number"?!1:Array.isArray(e)?e.includes(t):e===t}var H=function(e){Ee(r,e);var t=_e(r);function r(){var n;ge(this,r);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=t.call.apply(t,[this].concat(i)),V(F(n),"childRefs",{}),n}return Oe(r,[{key:"render",value:function(){var o=this,i=this.props,a=i.overrides,d=a===void 0?{}:a,s=i.mode,y=s===void 0?k.checkbox:s,g=i.children,b=i.selected,_=i.disabled,f=i.onClick,h=i.kind,v=i.shape,m=i.size,A=J(d.Root,N),w=ye(A,2),I=w[0],R=w[1],L=this.props["aria-label"]||this.props.ariaLabel,p=y===k.radio,W=c.Children.count(g);return c.createElement(Q.Consumer,null,function(Z){return c.createElement(I,$({"aria-label":L||Z.buttongroup.ariaLabel,"data-baseweb":"button-group",role:p?"radiogroup":"group",$shape:v,$length:g.length},R),c.Children.map(g,function(S,C){if(!c.isValidElement(S))return null;var T=S.props.isSelected?S.props.isSelected:Re(b,C);return p&&(o.childRefs[C]=c.createRef()),c.cloneElement(S,{disabled:_||S.props.disabled,isSelected:T,ref:p?o.childRefs[C]:void 0,tabIndex:!p||T||p&&(!b||b===-1)&&C===0?0:-1,onKeyDown:function(u){if(p){var O=Number(b)?Number(b):0;if(u.key==="ArrowUp"||u.key==="ArrowLeft"){u.preventDefault&&u.preventDefault();var D=O-1<0?W-1:O-1;f&&f(u,D),o.childRefs[D].current&&o.childRefs[D].current.focus()}if(u.key==="ArrowDown"||u.key==="ArrowRight"){u.preventDefault&&u.preventDefault();var j=O+1>W-1?0:O+1;f&&f(u,j),o.childRefs[j].current&&o.childRefs[j].current.focus()}}},kind:h,onClick:function(u){_||(S.props.onClick&&S.props.onClick(u),f&&f(u,C))},shape:v,size:m,overrides:de({BaseButton:{style:function(u){var O=u.$theme;return g.length===1?{}:v!==z.default?{marginLeft:O.sizing.scale100,marginRight:O.sizing.scale100}:{marginLeft:"0.5px",marginRight:"0.5px"}},props:{"aria-checked":T,role:p?"radio":"checkbox"}}},S.props.overrides)})}))})}}]),r}(c.Component);V(H,"defaultProps",{disabled:!1,onClick:function(){},shape:z.default,size:pe.default,kind:fe.secondary});function Ce(e,t){return t.includes(e)?t.filter(r=>r!==e):[...t,e]}function Pe(e,t,r){return e==l.ClickMode.MULTI_SELECT?Ce(t,r??[]):r?.includes(t)?[]:[t]}function ke(e){return e.length===0?-1:e[0]}function Be(e,t,r,n){t.setIntArrayValue(e,r.value,{fromUi:r.fromUi},n)}function Ae(e,t,r){const n=r===l.Style.PILLS?P.PILLS:r===l.Style.BORDERLESS?P.BORDERLESS_ICON:P.SEGMENTED_CONTROL,o=r===l.Style.BORDERLESS?x.XSMALL:x.MEDIUM,i=n===P.PILLS||n===P.SEGMENTED_CONTROL,a=r===l.Style.BORDERLESS?"lg":"base";return{element:E(ce,{icon:t,label:e,iconSize:a,useSmallerFont:i}),kind:n,size:o}}function Ie(e,t,r,n){return r.indexOf(n)>-1?!0:t!==l.ClickMode.SINGLE_SELECT||e!==l.SelectionVisualization.ALL_UP_TO_SELECTED?!1:r.length>0&&n<r[0]}function Te(e,t){return e&&(t=`${t}Active`),t}function De(e,t,r){const n={flexWrap:"wrap",maxWidth:"100%",margin:"0 0"},o=r?"100%":"auto",i=r?{}:{content:"''",flex:1e4};switch(e){case l.Style.BORDERLESS:return{...n,columnGap:t.threeXS,rowGap:t.threeXS};case l.Style.PILLS:return{...n,columnGap:t.twoXS,rowGap:t.twoXS,width:o};case l.Style.SEGMENTED_CONTROL:return{...n,columnGap:t.none,rowGap:t.twoXS,"::after":i,width:o};default:return n}}function je(e,t,r,n,o,i,a){const d=Ie(r,n,o,t);let s=e.content,y=e.contentIcon;return d&&(s=e.selectedContent?e.selectedContent:s,y=e.selectedContentIcon?e.selectedContentIcon:y),c.forwardRef(function(b,_){const{element:f,kind:h,size:v}=Ae(s??"",y??void 0,i),m=Te(!!(d&&!e.selectedContent&&!e.selectedContentIcon),h);return E(le,{...b,size:v,kind:m,containerWidth:a,children:f})})}function Me(e,t){return e.getIntArrayValue(t)}function $e(e){return e.default??null}function Ge(e){return e.value??null}function ze(e){const{disabled:t,element:r,fragmentId:n,widgetMgr:o,widthConfig:i}=e,{clickMode:a,options:d,selectionVisualization:s,style:y,label:g,labelVisibility:b,help:_}=r,f=Y(),[h,v]=se({getStateFromWidgetMgr:Me,getDefaultStateFromProto:$e,getCurrStateFromProto:Ge,updateWidgetMgrState:Be,element:r,widgetMgr:o,fragmentId:n}),m=!!(i?.useStretch||i?.pixelWidth),A=(R,L)=>{const p=Pe(a,L,h);v({value:p,fromUi:!0})};let w;a===l.ClickMode.SINGLE_SELECT?w=k.radio:a===l.ClickMode.MULTI_SELECT&&(w=k.checkbox);const I=c.useMemo(()=>d.map((R,L)=>{const p=je(R,L,s,a,h,y,m);return E(p,{},`${R.content}-${L}`)}),[a,d,s,y,h,m]);return ee(ue,{className:"stButtonGroup","data-testid":"stButtonGroup",containerWidth:m,children:[E(ae,{label:g,disabled:t,labelVisibility:te(b?.value??ie.LabelVisibilityOptions.COLLAPSED),children:_&&E(re,{children:E(ne,{content:_,placement:oe.TOP})})}),E(H,{disabled:t,mode:w,onClick:A,selected:a===l.ClickMode.MULTI_SELECT?h:ke(h),overrides:{Root:{style:c.useCallback(()=>De(y,f.spacing,m),[y,f.spacing,m])}},children:I})]})}const Ue=c.memo(ze);export{Ue as default};
