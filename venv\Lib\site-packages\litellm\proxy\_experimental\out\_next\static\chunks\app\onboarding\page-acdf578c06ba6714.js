(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[461],{8672:function(e,r,t){Promise.resolve().then(t.bind(t,12011))},69262:function(e,r,t){"use strict";t.d(r,{Z:function(){return m}});var n=t(5853),o=t(2265);let s=e=>{var r=(0,n._T)(e,[]);return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},r),o.createElement("path",{d:"M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM11 15V17H13V15H11ZM11 7V13H13V7H11Z"}))},l=e=>{var r=(0,n._T)(e,[]);return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},r),o.createElement("path",{d:"M1.18164 12C2.12215 6.87976 6.60812 3 12.0003 3C17.3924 3 21.8784 6.87976 22.8189 12C21.8784 17.1202 17.3924 21 12.0003 21C6.60812 21 2.12215 17.1202 1.18164 12ZM12.0003 17C14.7617 17 17.0003 14.7614 17.0003 12C17.0003 9.23858 14.7617 7 12.0003 7C9.23884 7 7.00026 9.23858 7.00026 12C7.00026 14.7614 9.23884 17 12.0003 17ZM12.0003 15C10.3434 15 9.00026 13.6569 9.00026 12C9.00026 10.3431 10.3434 9 12.0003 9C13.6571 9 15.0003 10.3431 15.0003 12C15.0003 13.6569 13.6571 15 12.0003 15Z"}))},a=e=>{var r=(0,n._T)(e,[]);return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},r),o.createElement("path",{d:"M4.52047 5.93457L1.39366 2.80777L2.80788 1.39355L22.6069 21.1925L21.1927 22.6068L17.8827 19.2968C16.1814 20.3755 14.1638 21.0002 12.0003 21.0002C6.60812 21.0002 2.12215 17.1204 1.18164 12.0002C1.61832 9.62282 2.81932 7.5129 4.52047 5.93457ZM14.7577 16.1718L13.2937 14.7078C12.902 14.8952 12.4634 15.0002 12.0003 15.0002C10.3434 15.0002 9.00026 13.657 9.00026 12.0002C9.00026 11.537 9.10522 11.0984 9.29263 10.7067L7.82866 9.24277C7.30514 10.0332 7.00026 10.9811 7.00026 12.0002C7.00026 14.7616 9.23884 17.0002 12.0003 17.0002C13.0193 17.0002 13.9672 16.6953 14.7577 16.1718ZM7.97446 3.76015C9.22127 3.26959 10.5793 3.00016 12.0003 3.00016C17.3924 3.00016 21.8784 6.87992 22.8189 12.0002C22.5067 13.6998 21.8038 15.2628 20.8068 16.5925L16.947 12.7327C16.9821 12.4936 17.0003 12.249 17.0003 12.0002C17.0003 9.23873 14.7617 7.00016 12.0003 7.00016C11.7514 7.00016 11.5068 7.01833 11.2677 7.05343L7.97446 3.76015Z"}))};var c=t(96398),d=t(97324),i=t(1153);let m=o.forwardRef((e,r)=>{let{value:t,defaultValue:m,type:u,placeholder:p="Type...",icon:g,error:f=!1,errorMessage:b,disabled:h=!1,stepper:k,makeInputClassName:x,className:w,onChange:C,onValueChange:v,autoFocus:N}=e,y=(0,n._T)(e,["value","defaultValue","type","placeholder","icon","error","errorMessage","disabled","stepper","makeInputClassName","className","onChange","onValueChange","autoFocus"]),[E,S]=(0,o.useState)(N||!1),[j,_]=(0,o.useState)(!1),Z=(0,o.useCallback)(()=>_(!j),[j,_]),I=(0,o.useRef)(null),q=(0,c.Uh)(t||m);return o.useEffect(()=>{let e=()=>S(!0),r=()=>S(!1),t=I.current;return t&&(t.addEventListener("focus",e),t.addEventListener("blur",r),N&&t.focus()),()=>{t&&(t.removeEventListener("focus",e),t.removeEventListener("blur",r))}},[N]),o.createElement(o.Fragment,null,o.createElement("div",{className:(0,d.q)(x("root"),"relative w-full flex items-center min-w-[10rem] outline-none rounded-tremor-default transition duration-100 border","shadow-tremor-input","dark:shadow-dark-tremor-input",(0,c.um)(q,h,f),E&&(0,d.q)("ring-2","border-tremor-brand-subtle ring-tremor-brand-muted","dark:border-dark-tremor-brand-subtle dark:ring-dark-tremor-brand-muted"),w)},g?o.createElement(g,{className:(0,d.q)(x("icon"),"shrink-0 h-5 w-5 ml-2.5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}):null,o.createElement("input",Object.assign({ref:(0,i.lq)([I,r]),defaultValue:m,value:t,type:j?"text":u,className:(0,d.q)(x("input"),"w-full focus:outline-none focus:ring-0 border-none bg-transparent text-tremor-default rounded-tremor-default transition duration-100 py-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis","[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",g?"pl-2":"pl-3",f?"pr-3":"pr-4",h?"placeholder:text-tremor-content-subtle dark:placeholder:text-dark-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-dark-tremor-content"),placeholder:p,disabled:h,"data-testid":"base-input",onChange:e=>{null==C||C(e),null==v||v(e.target.value)}},y)),"password"!==u||h?null:o.createElement("button",{className:(0,d.q)(x("toggleButton"),"mr-2"),type:"button",onClick:()=>Z(),"aria-label":j?"Hide password":"Show Password"},j?o.createElement(a,{className:(0,d.q)("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0}):o.createElement(l,{className:(0,d.q)("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0})),f?o.createElement(s,{className:(0,d.q)(x("errorIcon"),"text-red-500 shrink-0 w-5 h-5 mr-2.5")}):null,null!=k?k:null),f&&b?o.createElement("p",{className:(0,d.q)(x("errorMessage"),"text-sm text-red-500 mt-1")},b):null)});m.displayName="BaseInput"},49566:function(e,r,t){"use strict";t.d(r,{Z:function(){return c}});var n=t(5853),o=t(2265);t(97324);var s=t(1153),l=t(69262);let a=(0,s.fn)("TextInput"),c=o.forwardRef((e,r)=>{let{type:t="text"}=e,s=(0,n._T)(e,["type"]);return o.createElement(l.Z,Object.assign({ref:r,type:t,makeInputClassName:a},s))});c.displayName="TextInput"},96398:function(e,r,t){"use strict";t.d(r,{Uh:function(){return d},n0:function(){return a},qg:function(){return s},sl:function(){return l},um:function(){return c}});var n=t(97324),o=t(2265);let s=e=>["string","number"].includes(typeof e)?e:e instanceof Array?e.map(s).join(""):"object"==typeof e&&e?s(e.props.children):void 0;function l(e){let r=new Map;return o.Children.map(e,e=>{var t;r.set(e.props.value,null!==(t=s(e))&&void 0!==t?t:e.props.value)}),r}function a(e,r){return o.Children.map(r,r=>{var t;if((null!==(t=s(r))&&void 0!==t?t:r.props.value).toLowerCase().includes(e.toLowerCase()))return r})}let c=function(e,r){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return(0,n.q)(r?"bg-tremor-background-subtle dark:bg-dark-tremor-background-subtle":"bg-tremor-background dark:bg-dark-tremor-background",!r&&"hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted",e?"text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis":"text-tremor-content dark:text-dark-tremor-content",r&&"text-tremor-content-subtle dark:text-dark-tremor-content-subtle",t&&"text-red-500",t?"border-red-500":"border-tremor-border dark:border-dark-tremor-border")};function d(e){return null!=e&&""!==e}},49804:function(e,r,t){"use strict";t.d(r,{Z:function(){return d}});var n=t(5853),o=t(97324),s=t(1153),l=t(2265),a=t(9496);let c=(0,s.fn)("Col"),d=l.forwardRef((e,r)=>{let{numColSpan:t=1,numColSpanSm:s,numColSpanMd:d,numColSpanLg:i,children:m,className:u}=e,p=(0,n._T)(e,["numColSpan","numColSpanSm","numColSpanMd","numColSpanLg","children","className"]),g=(e,r)=>e&&Object.keys(r).includes(String(e))?r[e]:"";return l.createElement("div",Object.assign({ref:r,className:(0,o.q)(c("root"),(()=>{let e=g(t,a.PT),r=g(s,a.SP),n=g(d,a.VS),l=g(i,a._w);return(0,o.q)(e,r,n,l)})(),u)},p),m)});d.displayName="Col"},67101:function(e,r,t){"use strict";t.d(r,{Z:function(){return i}});var n=t(5853),o=t(97324),s=t(1153),l=t(2265),a=t(9496);let c=(0,s.fn)("Grid"),d=(e,r)=>e&&Object.keys(r).includes(String(e))?r[e]:"",i=l.forwardRef((e,r)=>{let{numItems:t=1,numItemsSm:s,numItemsMd:i,numItemsLg:m,children:u,className:p}=e,g=(0,n._T)(e,["numItems","numItemsSm","numItemsMd","numItemsLg","children","className"]),f=d(t,a._m),b=d(s,a.LH),h=d(i,a.l5),k=d(m,a.N4),x=(0,o.q)(f,b,h,k);return l.createElement("div",Object.assign({ref:r,className:(0,o.q)(c("root"),"grid",x,p)},g),u)});i.displayName="Grid"},9496:function(e,r,t){"use strict";t.d(r,{LH:function(){return o},N4:function(){return l},PT:function(){return a},SP:function(){return c},VS:function(){return d},_m:function(){return n},_w:function(){return i},l5:function(){return s}});let n={0:"grid-cols-none",1:"grid-cols-1",2:"grid-cols-2",3:"grid-cols-3",4:"grid-cols-4",5:"grid-cols-5",6:"grid-cols-6",7:"grid-cols-7",8:"grid-cols-8",9:"grid-cols-9",10:"grid-cols-10",11:"grid-cols-11",12:"grid-cols-12"},o={0:"sm:grid-cols-none",1:"sm:grid-cols-1",2:"sm:grid-cols-2",3:"sm:grid-cols-3",4:"sm:grid-cols-4",5:"sm:grid-cols-5",6:"sm:grid-cols-6",7:"sm:grid-cols-7",8:"sm:grid-cols-8",9:"sm:grid-cols-9",10:"sm:grid-cols-10",11:"sm:grid-cols-11",12:"sm:grid-cols-12"},s={0:"md:grid-cols-none",1:"md:grid-cols-1",2:"md:grid-cols-2",3:"md:grid-cols-3",4:"md:grid-cols-4",5:"md:grid-cols-5",6:"md:grid-cols-6",7:"md:grid-cols-7",8:"md:grid-cols-8",9:"md:grid-cols-9",10:"md:grid-cols-10",11:"md:grid-cols-11",12:"md:grid-cols-12"},l={0:"lg:grid-cols-none",1:"lg:grid-cols-1",2:"lg:grid-cols-2",3:"lg:grid-cols-3",4:"lg:grid-cols-4",5:"lg:grid-cols-5",6:"lg:grid-cols-6",7:"lg:grid-cols-7",8:"lg:grid-cols-8",9:"lg:grid-cols-9",10:"lg:grid-cols-10",11:"lg:grid-cols-11",12:"lg:grid-cols-12"},a={1:"col-span-1",2:"col-span-2",3:"col-span-3",4:"col-span-4",5:"col-span-5",6:"col-span-6",7:"col-span-7",8:"col-span-8",9:"col-span-9",10:"col-span-10",11:"col-span-11",12:"col-span-12",13:"col-span-13"},c={1:"sm:col-span-1",2:"sm:col-span-2",3:"sm:col-span-3",4:"sm:col-span-4",5:"sm:col-span-5",6:"sm:col-span-6",7:"sm:col-span-7",8:"sm:col-span-8",9:"sm:col-span-9",10:"sm:col-span-10",11:"sm:col-span-11",12:"sm:col-span-12",13:"sm:col-span-13"},d={1:"md:col-span-1",2:"md:col-span-2",3:"md:col-span-3",4:"md:col-span-4",5:"md:col-span-5",6:"md:col-span-6",7:"md:col-span-7",8:"md:col-span-8",9:"md:col-span-9",10:"md:col-span-10",11:"md:col-span-11",12:"md:col-span-12",13:"md:col-span-13"},i={1:"lg:col-span-1",2:"lg:col-span-2",3:"lg:col-span-3",4:"lg:col-span-4",5:"lg:col-span-5",6:"lg:col-span-6",7:"lg:col-span-7",8:"lg:col-span-8",9:"lg:col-span-9",10:"lg:col-span-10",11:"lg:col-span-11",12:"lg:col-span-12",13:"lg:col-span-13"}},94789:function(e,r,t){"use strict";t.d(r,{Z:function(){return d}});var n=t(5853),o=t(2265),s=t(26898),l=t(97324),a=t(1153);let c=(0,a.fn)("Callout"),d=o.forwardRef((e,r)=>{let{title:t,icon:d,color:i,className:m,children:u}=e,p=(0,n._T)(e,["title","icon","color","className","children"]);return o.createElement("div",Object.assign({ref:r,className:(0,l.q)(c("root"),"flex flex-col overflow-hidden rounded-tremor-default text-tremor-default border-l-4 py-3 pr-3 pl-4",i?(0,l.q)((0,a.bM)(i,s.K.background).bgColor,(0,a.bM)(i,s.K.darkBorder).borderColor,(0,a.bM)(i,s.K.darkText).textColor,"dark:bg-opacity-10 bg-opacity-10"):(0,l.q)("bg-tremor-brand-faint border-tremor-brand-emphasis text-tremor-brand-emphasis","dark:bg-dark-tremor-brand-muted/70 dark:border-dark-tremor-brand-emphasis dark:text-dark-tremor-brand-emphasis"),m)},p),o.createElement("div",{className:(0,l.q)(c("header"),"flex items-start")},d?o.createElement(d,{className:(0,l.q)(c("icon"),"flex-none h-5 w-5 mr-1.5")}):null,o.createElement("h4",{className:(0,l.q)(c("title"),"font-semibold")},t)),o.createElement("p",{className:(0,l.q)(c("body"),"overflow-y-auto",u?"mt-2":"")},u))});d.displayName="Callout"},12011:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return w}});var n=t(57437),o=t(2265),s=t(99376),l=t(20831),a=t(94789),c=t(12514),d=t(49804),i=t(67101),m=t(84264),u=t(49566),p=t(96761),g=t(84566),f=t(19250),b=t(14474),h=t(13634),k=t(73002),x=t(3914);function w(){let[e]=h.Z.useForm(),r=(0,s.useSearchParams)();(0,x.e)("token");let t=r.get("invitation_id"),w=r.get("action"),[C,v]=(0,o.useState)(null),[N,y]=(0,o.useState)(""),[E,S]=(0,o.useState)(""),[j,_]=(0,o.useState)(null),[Z,I]=(0,o.useState)(""),[q,L]=(0,o.useState)(""),[M,T]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{(0,f.getUiConfig)().then(e=>{console.log("ui config in onboarding.tsx:",e),T(!1)})},[]),(0,o.useEffect)(()=>{t&&!M&&(0,f.getOnboardingCredentials)(t).then(e=>{let r=e.login_url;console.log("login_url:",r),I(r);let t=e.token,n=(0,b.o)(t);L(t),console.log("decoded:",n),v(n.key),console.log("decoded user email:",n.user_email),S(n.user_email),_(n.user_id)})},[t,M]),(0,n.jsx)("div",{className:"mx-auto w-full max-w-md mt-10",children:(0,n.jsxs)(c.Z,{children:[(0,n.jsx)(p.Z,{className:"text-sm mb-5 text-center",children:"\uD83D\uDE85 LiteLLM"}),(0,n.jsx)(p.Z,{className:"text-xl",children:"reset_password"===w?"Reset Password":"Sign up"}),(0,n.jsx)(m.Z,{children:"reset_password"===w?"Reset your password to access Admin UI.":"Claim your user account to login to Admin UI."}),"reset_password"!==w&&(0,n.jsx)(a.Z,{className:"mt-4",title:"SSO",icon:g.GH$,color:"sky",children:(0,n.jsxs)(i.Z,{numItems:2,className:"flex justify-between items-center",children:[(0,n.jsx)(d.Z,{children:"SSO is under the Enterprise Tier."}),(0,n.jsx)(d.Z,{children:(0,n.jsx)(l.Z,{variant:"primary",className:"mb-2",children:(0,n.jsx)("a",{href:"https://forms.gle/W3U4PZpJGFHWtHyA9",target:"_blank",children:"Get Free Trial"})})})]})}),(0,n.jsxs)(h.Z,{className:"mt-10 mb-5 mx-auto",layout:"vertical",onFinish:e=>{console.log("in handle submit. accessToken:",C,"token:",q,"formValues:",e),C&&q&&(e.user_email=E,j&&t&&(0,f.claimOnboardingToken)(C,t,j,e.password).then(e=>{let r="/ui/";r+="?login=success",document.cookie="token="+q,console.log("redirecting to:",r);let t=(0,f.getProxyBaseUrl)();console.log("proxyBaseUrl:",t),t?window.location.href=t+r:window.location.href=r}))},children:[(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h.Z.Item,{label:"Email Address",name:"user_email",children:(0,n.jsx)(u.Z,{type:"email",disabled:!0,value:E,defaultValue:E,className:"max-w-md"})}),(0,n.jsx)(h.Z.Item,{label:"Password",name:"password",rules:[{required:!0,message:"password required to sign up"}],help:"reset_password"===w?"Enter your new password":"Create a password for your account",children:(0,n.jsx)(u.Z,{placeholder:"",type:"password",className:"max-w-md"})})]}),(0,n.jsx)("div",{className:"mt-10",children:(0,n.jsx)(k.ZP,{htmlType:"submit",children:"reset_password"===w?"Reset Password":"Sign Up"})})]})]})})}},14474:function(e,r,t){"use strict";t.d(r,{o:function(){return o}});class n extends Error{}function o(e,r){let t;if("string"!=typeof e)throw new n("Invalid token specified: must be a string");r||(r={});let o=!0===r.header?0:1,s=e.split(".")[o];if("string"!=typeof s)throw new n(`Invalid token specified: missing part #${o+1}`);try{t=function(e){let r=e.replace(/-/g,"+").replace(/_/g,"/");switch(r.length%4){case 0:break;case 2:r+="==";break;case 3:r+="=";break;default:throw Error("base64 string is not of the correct length")}try{var t;return t=r,decodeURIComponent(atob(t).replace(/(.)/g,(e,r)=>{let t=r.charCodeAt(0).toString(16).toUpperCase();return t.length<2&&(t="0"+t),"%"+t}))}catch(e){return atob(r)}}(s)}catch(e){throw new n(`Invalid token specified: invalid base64 for part #${o+1} (${e.message})`)}try{return JSON.parse(t)}catch(e){throw new n(`Invalid token specified: invalid json for part #${o+1} (${e.message})`)}}n.prototype.name="InvalidTokenError"}},function(e){e.O(0,[665,445,634,338,971,117,744],function(){return e(e.s=8672)}),_N_E=e.O()}]);