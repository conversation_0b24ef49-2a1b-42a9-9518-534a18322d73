import{r,L as f,b4 as l,j as t,b5 as m,bp as B,b3 as b,b6 as p}from"./index.CbQtRkVt.js";import{c as h}from"./createDownloadLinkElement.DZMwyjvU.js";function w(n,o,e){return h({enforceDownloadInNewTab:e,url:n.buildMediaURL(o),filename:""})}function D(n){const{disabled:o,element:e,widgetMgr:s,endpoints:i,fragmentId:d}=n,{libConfig:{enforceDownloadInNewTab:c=!1}}=r.useContext(f);let a=l.SECONDARY;e.type==="primary"?a=l.PRIMARY:e.type==="tertiary"&&(a=l.TERTIARY),r.useEffect(()=>{i.checkSourceUrlResponse(e.url,"Download Button")},[e.url,i]);const u=()=>{e.ignoreRerun||s.setTriggerValue(e,{fromUi:!0},d),w(i,e.url,c).click()};return t("div",{className:"stDownloadButton","data-testid":"stDownloadButton",children:t(m,{help:e.help,containerWidth:e.useContainerWidth,children:t(B,{kind:a,size:b.SMALL,disabled:o,onClick:u,containerWidth:e.useContainerWidth,children:t(p,{icon:e.icon,label:e.label})})})})}const R=r.memo(D);export{R as default};
