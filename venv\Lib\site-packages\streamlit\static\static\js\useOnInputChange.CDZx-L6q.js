import{r as p,cu as a}from"./index.CbQtRkVt.js";import{i as b}from"./inputUtils.CptNuJwn.js";function i(r,s,u,e){p.useEffect(()=>{e||r!==s&&u(r)},[r,s,e,u])}function F(r,s,u,e,f,n=!1){return p.useCallback(t=>{const o=n?t.metaKey||t.ctrlKey:!0;!b(t)||!o||(t.preventDefault(),u&&s(),e.allowFormEnterToSubmit(r)&&e.submitForm(r,f))},[r,f,u,s,e,n])}function K({formId:r,maxChars:s,setDirty:u,setUiValue:e,setValueWithSource:f,additionalAction:n}){return p.useCallback(t=>{const{value:o}=t.target;s!==0&&o.length>s||(u(!0),e(o),a({formId:r})&&f({value:o,fromUi:!0}),n&&n())},[r,s,u,e,f,n])}export{K as a,F as b,i as u};
