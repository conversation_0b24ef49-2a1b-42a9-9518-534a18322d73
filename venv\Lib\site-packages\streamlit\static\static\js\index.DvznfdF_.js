import{r as y,E as T,_ as O,s as I,J,C as E,j as f,bu as Q,b9 as L,bp as K,b4 as Y,bo as W,bC as B,d as Z,g as ee,aO as te,z as re,bD as ie,bE as oe,bF as ne,l as ae,aF as se,N as P,bG as z,bl as le,bk as de,br as ce,bH as ue,bs as he,bt as pe,F as fe}from"./index.CbQtRkVt.js";import{F as ge}from"./FormClearHelper.Cdw5Y7_m.js";import{U as k}from"./UploadFileInfo.C-jY39rj.js";import{P as me,S as ve}from"./ProgressBar.EhJ_lCOf.js";var $=y.forwardRef(function(i,l){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return y.createElement(T,O({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},i,{ref:l}),y.createElement("path",{d:"M.5 1c-.28 0-.5.23-.5.5v4c0 .28.23.5.5.5h5c.28 0 .5-.22.5-.5V4l1 1h1V2H7L6 3V1.5c0-.28-.22-.5-.5-.5h-5z"}))});$.displayName="Video";var A=y.forwardRef(function(i,l){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return y.createElement(T,O({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},i,{ref:l}),y.createElement("path",{d:"M1.41 0L0 1.41l.72.72L2.5 3.94.72 5.72 0 6.41l1.41 1.44.72-.72 1.81-1.81 1.78 1.81.69.72 1.44-1.44-.72-.69-1.81-1.78 1.81-1.81.72-.72L6.41 0l-.69.72L3.94 2.5 2.13.72 1.41 0z"}))});A.displayName="X";var N=y.forwardRef(function(i,l){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return y.createElement(T,O({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},i,{ref:l}),y.createElement("rect",{width:24,height:24,fill:"none"}),y.createElement("path",{d:"M20 5h-3.17l-1.24-1.35A1.99 1.99 0 0014.12 3H9.88c-.56 0-1.1.24-1.48.65L7.17 5H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1.35 8.35l-2.79 2.79c-.32.32-.86.1-.86-.35v-1.75H9v1.75c0 .45-.54.67-.85.35l-2.79-2.79c-.2-.2-.2-.51 0-.71l2.79-2.79a.5.5 0 01.85.36v1.83h6v-1.83c0-.45.54-.67.85-.35l2.79 2.79c.2.19.2.51.01.7z"}))});N.displayName="SwitchCamera";function Se(i,l){switch(i){case"xsmall":return{padding:`${l.spacing.twoXS} ${l.spacing.sm}`,fontSize:l.fontSizes.sm};case"small":return{padding:`${l.spacing.twoXS} ${l.spacing.md}`};case"large":return{padding:`${l.spacing.md} ${l.spacing.md}`};default:return{padding:`${l.spacing.xs} ${l.spacing.md}`}}}const H=I("div",{target:"etzmx8d0"})({position:"relative",overflow:"hidden",width:"100%",objectFit:"contain"}),V=I("div",{target:"etzmx8d1"})(({theme:i,width:l})=>({backgroundColor:i.colors.secondaryBg,borderRadius:`${i.radii.default} ${i.radii.default} 0 0`,width:"100%",height:l*9/16,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"})),ye=I("p",{target:"etzmx8d2"})(({theme:i})=>({marginTop:i.spacing.sm,textAlign:"center"})),be=I("img",{target:"etzmx8d3"})(({theme:i,opacity:l})=>({borderRadius:`${i.radii.default} ${i.radii.default} 0 0`,objectFit:"contain",opacity:l})),Ce=I("a",{target:"etzmx8d4"})(({theme:i})=>({color:i.colors.link,textDecoration:i.linkUnderline?"underline":"none",display:"block"})),we=I("span",{target:"etzmx8d5"})({display:"flex",alignItems:"center"}),Me=I("div",{target:"etzmx8d6"})(({theme:i})=>({position:"absolute",top:i.spacing.lg,right:i.spacing.lg,zIndex:i.zIndices.priority,color:i.colors.fadedText40,mixBlendMode:"difference",opacity:.6})),Ue=I("div",{target:"etzmx8d8"})({height:"fit-content",width:"100%",position:"absolute",bottom:0}),Ie=I("button",{target:"etzmx8d9"})(({theme:i})=>({position:"relative",display:"inline-flex",flexDirection:"column",alignItems:"center",justifyContent:"center",backgroundColor:i.colors.lightenedBg05,border:`${i.sizes.borderWidth} solid ${i.colors.borderColor}`,borderRadius:`0 0 ${i.radii.default} ${i.radii.default}`,"&:hover":{borderColor:i.colors.primary,color:i.colors.primary},"&:active":{color:i.colors.white,borderColor:i.colors.primary,backgroundColor:i.colors.primary},"&:focus:not(:active)":{borderColor:i.colors.primary,color:i.colors.primary},"&:disabled, &:disabled:hover, &:disabled:active":{color:i.colors.fadedText40,borderColor:i.colors.borderColor,backgroundColor:i.colors.lightenedBg05,cursor:"not-allowed"},fontWeight:i.fontWeights.normal,padding:`${i.spacing.xs} ${i.spacing.md}`,margin:i.spacing.none,lineHeight:i.lineHeights.base,color:"inherit",width:"100%",userSelect:"none","&:focus":{outline:"none"},"&:focus-visible":{boxShadow:`0 0 0 0.2rem ${J(i.colors.primary,.5)}`},...Se("medium",i)}));function Fe({disabled:i,onClick:l,children:t,progress:n}){return E(Ie,{disabled:i||!1,onClick:l||(()=>{}),progress:n||null,"data-testid":"stCameraInputButton",children:[t,n?f(Ue,{children:f(me,{value:n,size:ve.EXTRASMALL,overrides:{Bar:{style:{borderTopLeftRadius:0,borderTopRightRadius:0}},BarProgress:{style:{borderTopLeftRadius:0,borderTopRightRadius:0}},BarContainer:{style:{borderTopLeftRadius:0,borderTopRightRadius:0}}}})}):null]})}const D=y.memo(Fe);var R=function(i){return i.USER="user",i.ENVIRONMENT="environment",i}({});const Ee=({switchFacingMode:i})=>f(Me,{"data-testid":"stCameraInputSwitchButton",children:f(Q,{content:"Switch camera",placement:L.TOP_RIGHT,children:f(K,{kind:Y.MINIMAL,onClick:i,children:f(W,{content:N,size:"twoXL",color:B.white})})})}),xe=y.memo(Ee);var _={exports:{}},Pe=_.exports,j;function Re(){return j||(j=1,function(i,l){(function(n,o){i.exports=o(Z())})(Pe,function(t){return function(n){var o={};function a(s){if(o[s])return o[s].exports;var h=o[s]={i:s,l:!1,exports:{}};return n[s].call(h.exports,h,h.exports,a),h.l=!0,h.exports}return a.m=n,a.c=o,a.d=function(s,h,g){a.o(s,h)||Object.defineProperty(s,h,{enumerable:!0,get:g})},a.r=function(s){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})},a.t=function(s,h){if(h&1&&(s=a(s)),h&8||h&4&&typeof s=="object"&&s&&s.__esModule)return s;var g=Object.create(null);if(a.r(g),Object.defineProperty(g,"default",{enumerable:!0,value:s}),h&2&&typeof s!="string")for(var U in s)a.d(g,U,(function(M){return s[M]}).bind(null,U));return g},a.n=function(s){var h=s&&s.__esModule?function(){return s.default}:function(){return s};return a.d(h,"a",h),h},a.o=function(s,h){return Object.prototype.hasOwnProperty.call(s,h)},a.p="",a(a.s="./src/react-webcam.tsx")}({"./src/react-webcam.tsx":function(n,o,a){a.r(o);var s=a("react"),h=function(){var m=function(u,e){return m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,d){r.__proto__=d}||function(r,d){for(var c in d)d.hasOwnProperty(c)&&(r[c]=d[c])},m(u,e)};return function(u,e){m(u,e);function r(){this.constructor=u}u.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),g=function(){return g=Object.assign||function(m){for(var u,e=1,r=arguments.length;e<r;e++){u=arguments[e];for(var d in u)Object.prototype.hasOwnProperty.call(u,d)&&(m[d]=u[d])}return m},g.apply(this,arguments)},U=function(m,u){var e={};for(var r in m)Object.prototype.hasOwnProperty.call(m,r)&&u.indexOf(r)<0&&(e[r]=m[r]);if(m!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,r=Object.getOwnPropertySymbols(m);d<r.length;d++)u.indexOf(r[d])<0&&Object.prototype.propertyIsEnumerable.call(m,r[d])&&(e[r[d]]=m[r[d]]);return e};(function(){typeof window>"u"||(navigator.mediaDevices===void 0&&(navigator.mediaDevices={}),navigator.mediaDevices.getUserMedia===void 0&&(navigator.mediaDevices.getUserMedia=function(u){var e=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return e?new Promise(function(r,d){e.call(navigator,u,r,d)}):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}))})();function M(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)}var F=function(m){h(u,m);function u(e){var r=m.call(this,e)||this;return r.canvas=null,r.ctx=null,r.requestUserMediaId=0,r.unmounted=!1,r.state={hasUserMedia:!1},r}return u.prototype.componentDidMount=function(){var e=this,r=e.state,d=e.props;if(this.unmounted=!1,!M()){d.onUserMediaError("getUserMedia not supported");return}r.hasUserMedia||this.requestUserMedia(),d.children&&typeof d.children!="function"&&console.warn("children must be a function")},u.prototype.componentDidUpdate=function(e){var r=this.props;if(!M()){r.onUserMediaError("getUserMedia not supported");return}var d=JSON.stringify(e.audioConstraints)!==JSON.stringify(r.audioConstraints),c=JSON.stringify(e.videoConstraints)!==JSON.stringify(r.videoConstraints),C=e.minScreenshotWidth!==r.minScreenshotWidth,S=e.minScreenshotHeight!==r.minScreenshotHeight;(c||C||S)&&(this.canvas=null,this.ctx=null),(d||c)&&(this.stopAndCleanup(),this.requestUserMedia())},u.prototype.componentWillUnmount=function(){this.unmounted=!0,this.stopAndCleanup()},u.stopMediaStream=function(e){e&&(e.getVideoTracks&&e.getAudioTracks?(e.getVideoTracks().map(function(r){e.removeTrack(r),r.stop()}),e.getAudioTracks().map(function(r){e.removeTrack(r),r.stop()})):e.stop())},u.prototype.stopAndCleanup=function(){var e=this.state;e.hasUserMedia&&(u.stopMediaStream(this.stream),e.src&&window.URL.revokeObjectURL(e.src))},u.prototype.getScreenshot=function(e){var r=this,d=r.state,c=r.props;if(!d.hasUserMedia)return null;var C=this.getCanvas(e);return C&&C.toDataURL(c.screenshotFormat,c.screenshotQuality)},u.prototype.getCanvas=function(e){var r=this,d=r.state,c=r.props;if(!this.video||!d.hasUserMedia||!this.video.videoHeight)return null;if(!this.ctx){var C=this.video.videoWidth,S=this.video.videoHeight;if(!this.props.forceScreenshotSourceSize){var v=C/S;C=c.minScreenshotWidth||this.video.clientWidth,S=C/v,c.minScreenshotHeight&&S<c.minScreenshotHeight&&(S=c.minScreenshotHeight,C=S*v)}this.canvas=document.createElement("canvas"),this.canvas.width=e?.width||C,this.canvas.height=e?.height||S,this.ctx=this.canvas.getContext("2d")}var w=this,b=w.ctx,p=w.canvas;return b&&p&&(p.width=e?.width||p.width,p.height=e?.height||p.height,c.mirrored&&(b.translate(p.width,0),b.scale(-1,1)),b.imageSmoothingEnabled=c.imageSmoothing,b.drawImage(this.video,0,0,e?.width||p.width,e?.height||p.height),c.mirrored&&(b.scale(-1,1),b.translate(-p.width,0))),p},u.prototype.requestUserMedia=function(){var e=this,r=this.props,d=function(S,v){var w={video:typeof v<"u"?v:!0};r.audio&&(w.audio=typeof S<"u"?S:!0),e.requestUserMediaId++;var b=e.requestUserMediaId;navigator.mediaDevices.getUserMedia(w).then(function(p){e.unmounted||b!==e.requestUserMediaId?u.stopMediaStream(p):e.handleUserMedia(null,p)}).catch(function(p){e.handleUserMedia(p)})};if("mediaDevices"in navigator)d(r.audioConstraints,r.videoConstraints);else{var c=function(S){return{optional:[{sourceId:S}]}},C=function(S){var v=S.deviceId;return typeof v=="string"?v:Array.isArray(v)&&v.length>0?v[0]:typeof v=="object"&&v.ideal?v.ideal:null};MediaStreamTrack.getSources(function(S){var v=null,w=null;S.forEach(function(x){x.kind==="audio"?v=x.id:x.kind==="video"&&(w=x.id)});var b=C(r.audioConstraints);b&&(v=b);var p=C(r.videoConstraints);p&&(w=p),d(c(v),c(w))})}},u.prototype.handleUserMedia=function(e,r){var d=this.props;if(e||!r){this.setState({hasUserMedia:!1}),d.onUserMediaError(e);return}this.stream=r;try{this.video&&(this.video.srcObject=r),this.setState({hasUserMedia:!0})}catch{this.setState({hasUserMedia:!0,src:window.URL.createObjectURL(r)})}d.onUserMedia(r)},u.prototype.render=function(){var e=this,r=this,d=r.state,c=r.props,C=c.audio;c.forceScreenshotSourceSize;var S=c.disablePictureInPicture;c.onUserMedia,c.onUserMediaError,c.screenshotFormat,c.screenshotQuality,c.minScreenshotWidth,c.minScreenshotHeight,c.audioConstraints,c.videoConstraints,c.imageSmoothing;var v=c.mirrored,w=c.style,b=w===void 0?{}:w,p=c.children,x=U(c,["audio","forceScreenshotSourceSize","disablePictureInPicture","onUserMedia","onUserMediaError","screenshotFormat","screenshotQuality","minScreenshotWidth","minScreenshotHeight","audioConstraints","videoConstraints","imageSmoothing","mirrored","style","children"]),G=v?g(g({},b),{transform:(b.transform||"")+" scaleX(-1)"}):b,q={getScreenshot:this.getScreenshot.bind(this)};return s.createElement(s.Fragment,null,s.createElement("video",g({autoPlay:!0,disablePictureInPicture:S,src:d.src,muted:!C,playsInline:!0,ref:function(X){e.video=X},style:G},x)),p&&p(q))},u.defaultProps={audio:!1,disablePictureInPicture:!1,forceScreenshotSourceSize:!1,imageSmoothing:!0,mirrored:!1,onUserMedia:function(){},onUserMediaError:function(){},screenshotFormat:"image/webp",screenshotQuality:.92},u}(s.Component);o.default=F},react:function(n,o){n.exports=t}}).default})}(_)),_.exports}var _e=Re();const Te=ee(_e),Oe=({width:i})=>E(V,{width:i,children:[f(W,{size:"threeXL",color:B.gray60,content:$}),E(ye,{children:["This app would like to use your camera.",f(Ce,{href:oe,rel:"noopener noreferrer",target:"_blank",children:"Learn how to allow access."})]})]}),We=({handleCapture:i,width:l,disabled:t,clearPhotoInProgress:n,setClearPhotoInProgress:o,facingMode:a,setFacingMode:s,testOverride:h})=>{const[g,U]=y.useState(h||"pending"),M=y.useRef(null),[F,m]=y.useState(l),u=y.useCallback(te(1e3,m),[]);y.useEffect(()=>{u(l)},[l,u]);function e(){if(M.current!==null){const d=M.current.getScreenshot();i(d)}}const r=re();return E(H,{"data-testid":"stCameraInputWebcamComponent",children:[g!=="success"&&!t&&!n?f(Oe,{width:F}):ie()&&f(xe,{switchFacingMode:s}),f(V,{"data-testid":"stCameraInputWebcamStyledBox",hidden:g!=="success"&&!t&&!n,width:F,children:!t&&f(Te,{audio:!1,ref:M,screenshotFormat:"image/jpeg",screenshotQuality:1,width:F,height:F*9/16,style:{borderRadius:`${r.radii.default} ${r.radii.default} 0 0`},onUserMediaError:()=>{U("error")},onUserMedia:()=>{U("success"),o(!1)},videoConstraints:{width:{ideal:F},facingMode:a}})}),f(D,{onClick:e,disabled:g!=="success"||t||n,children:"Take Photo"})]})},Ve=y.memo(We),ze=150,ke=ae.getLogger("CameraInput");class je extends y.PureComponent{constructor(l){super(l),this.localFileIdCounter=1,this.RESTORED_FROM_WIDGET_STRING="RESTORED_FROM_WIDGET",this.formClearHelper=new ge,this.getProgress=()=>{if(this.state.files.length>0&&this.state.files[this.state.files.length-1].status.type==="uploading")return this.state.files[this.state.files.length-1].status.progress},this.setClearPhotoInProgress=t=>{this.setState({clearPhotoInProgress:t})},this.setFacingMode=()=>{this.setState(t=>({facingMode:t.facingMode===R.USER?R.ENVIRONMENT:R.USER}))},this.handleCapture=t=>{if(t===null)return Promise.resolve();this.setState({imgSrc:t,shutter:!0,minShutterEffectPassed:!1});const n=o=>new Promise(a=>setTimeout(a,o));return Le(t,`camera-input-${new Date().toISOString().replace(/:/g,"_")}.jpg`).then(o=>this.props.uploadClient.fetchFileURLs([o]).then(a=>({file:o,fileUrls:a[0]}))).then(({file:o,fileUrls:a})=>this.uploadFile(a,o)).then(()=>n(ze)).then(()=>{this.setState((o,a)=>({imgSrc:t,shutter:o.shutter,minShutterEffectPassed:!0}))}).catch(o=>{ke.error(o)})},this.removeCapture=()=>{this.state.files.length!==0&&(this.state.files.forEach(t=>this.deleteFile(t.id)),this.setState({imgSrc:null,clearPhotoInProgress:!0}))},this.componentDidUpdate=()=>{if(this.status!=="ready")return;const t=this.createWidgetValue(),{element:n,widgetMgr:o,fragmentId:a}=this.props,s=o.getFileUploaderStateValue(n);se(t,s)||o.setFileUploaderStateValue(n,t,{fromUi:!0},a)},this.onFormCleared=()=>{this.setState({files:[]},()=>{const t=this.createWidgetValue();if(P(t))return;this.setState({imgSrc:null});const{widgetMgr:n,element:o,fragmentId:a}=this.props;n.setFileUploaderStateValue(o,t,{fromUi:!0},a)})},this.deleteFile=t=>{const n=this.getFile(t);P(n)||(n.status.type==="uploading"&&n.status.cancelToken.cancel(),n.status.type==="uploaded"&&n.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(n.status.fileUrls.deleteUrl),this.removeFile(t))},this.addFile=t=>{this.setState(n=>({files:[...n.files,t]}))},this.removeFile=t=>{this.setState(n=>({files:n.files.filter(o=>o.id!==t)}))},this.getFile=t=>this.state.files.find(n=>n.id===t),this.updateFile=(t,n)=>{this.setState(o=>({files:o.files.map(a=>a.id===t?n:a)}))},this.onUploadComplete=(t,n)=>{this.setState(()=>({shutter:!1}));const o=this.getFile(t);P(o)||o.status.type!=="uploading"||this.updateFile(o.id,o.setStatus({type:"uploaded",fileId:n.fileId,fileUrls:n}))},this.onUploadProgress=(t,n)=>{const o=this.getFile(n);if(P(o)||o.status.type!=="uploading")return;const a=Math.round(t.loaded*100/t.total);o.status.progress!==a&&this.updateFile(n,o.setStatus({type:"uploading",cancelToken:o.status.cancelToken,progress:a}))},this.reset=()=>{this.setState({files:[],imgSrc:null})},this.uploadFile=(t,n)=>{const o=z.CancelToken.source(),a=new k(n.name,n.size,this.nextLocalFileId(),{type:"uploading",cancelToken:o,progress:1});this.addFile(a),this.props.uploadClient.uploadFile(this.props.element,t.uploadUrl,n,s=>this.onUploadProgress(s,a.id),o.token).then(()=>this.onUploadComplete(a.id,t)).catch(s=>{z.isCancel(s)||this.updateFile(a.id,a.setStatus({type:"error",errorMessage:s?s.toString():"Unknown error"}))})},this.state=this.initialValue}get initialValue(){const l={files:[],imgSrc:null,shutter:!1,minShutterEffectPassed:!0,clearPhotoInProgress:!1,facingMode:R.USER},{widgetMgr:t,element:n}=this.props,o=t.getFileUploaderStateValue(n);if(P(o))return l;const{uploadedFileInfo:a}=o;return P(a)||a.length===0?l:{files:a.map(s=>{const h=s.name,g=s.size,U=s.fileId,M=s.fileUrls;return new k(h,g,this.nextLocalFileId(),{type:"uploaded",fileId:U,fileUrls:M})}),imgSrc:a.length===0?"":this.RESTORED_FROM_WIDGET_STRING,shutter:!1,minShutterEffectPassed:!1,clearPhotoInProgress:!1,facingMode:R.USER}}componentWillUnmount(){this.formClearHelper.disconnect()}get status(){const l=t=>t.status.type==="uploading";return this.state.files.some(l)?"updating":"ready"}componentDidMount(){const l=this.createWidgetValue(),{element:t,widgetMgr:n,fragmentId:o}=this.props;n.getFileUploaderStateValue(t)===void 0&&n.setFileUploaderStateValue(t,l,{fromUi:!1},o)}createWidgetValue(){const l=this.state.files.filter(t=>t.status.type==="uploaded").map(t=>{const{name:n,size:o,status:a}=t;return new le({fileId:a.fileId,fileUrls:a.fileUrls,name:n,size:o})});return new de({uploadedFileInfo:l})}render(){const{element:l,widgetMgr:t,disabled:n,width:o}=this.props;return this.formClearHelper.manageFormClearListener(t,l.formId,this.onFormCleared),E(H,{className:"stCameraInput","data-testid":"stCameraInput",children:[f(pe,{label:l.label,disabled:n,labelVisibility:ce(l.labelVisibility?.value),children:l.help&&f(ue,{children:f(he,{content:l.help,placement:L.TOP_RIGHT})})}),this.state.imgSrc?E(fe,{children:[f(V,{width:o,children:this.state.imgSrc!==this.RESTORED_FROM_WIDGET_STRING&&f(be,{src:this.state.imgSrc,alt:"Snapshot",opacity:this.state.shutter||!this.state.minShutterEffectPassed?"50%":"100%",width:o,height:o*9/16})}),f(D,{onClick:this.removeCapture,progress:this.getProgress(),disabled:!!this.getProgress()||n,children:this.getProgress()?"Uploading...":E(we,{children:[f(W,{content:A,margin:"0 xs 0 0",size:"sm"})," Clear photo"]})})]}):f(Ve,{handleCapture:this.handleCapture,width:o,disabled:n,clearPhotoInProgress:this.state.clearPhotoInProgress,setClearPhotoInProgress:this.setClearPhotoInProgress,facingMode:this.state.facingMode,setFacingMode:this.setFacingMode,testOverride:this.props.testOverride})]})}nextLocalFileId(){return this.localFileIdCounter++}}function Le(i,l){return fetch(i).then(t=>t.arrayBuffer()).then(t=>new File([t],l,{type:"image/jpeg"}))}const He=ne(je);export{He as default};
