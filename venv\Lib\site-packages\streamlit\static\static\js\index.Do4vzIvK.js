import{r as l,N as s,j as m,br as V,ca as x}from"./index.CbQtRkVt.js";import{a as U}from"./useBasicWidgetState.Bx3VaRHk.js";import"./FormClearHelper.Cdw5Y7_m.js";const h=(t,e)=>t.getStringValue(e),N=t=>t.options.length===0||s(t.default)?null:t.options[t.default],v=t=>t.rawValue??null,w=(t,e,o,a)=>{e.setStringValue(t,o.value,{fromUi:o.fromUi},a)},y=({disabled:t,element:e,widgetMgr:o,fragmentId:a})=>{const{options:i,help:u,label:n,labelVisibility:c,placeholder:p,acceptNewOptions:f}=e,[g,r]=U({getStateFromWidgetMgr:h,getDefaultStateFromProto:N,getCurrStateFromProto:v,updateWidgetMgrState:w,element:e,widgetMgr:o,fragmentId:a}),b=l.useCallback(d=>{r({value:d,fromUi:!0})},[r]),S=s(e.default)&&!t;return m(x,{label:n,labelVisibility:V(c?.value),options:i,disabled:t,onChange:b,value:g,help:u,placeholder:p,clearable:S,acceptNewOptions:f??!1})},P=l.memo(y);export{P as default};
