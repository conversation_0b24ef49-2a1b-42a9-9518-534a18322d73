import{c as w,e as b}from"./es6.CVz13CSz.js";import"./index.CbQtRkVt.js";const{WritableStream:m,TransformStream:y,DOMException:P,Blob:_}=w,{GONE:v}=b,E=/constructor/i.test(window.HTMLElement);class C{constructor(e="unkown"){this.name=e,this.kind="file"}async getFile(){throw new P(...v)}async isSameEntry(e){return this===e}async createWritable(e={}){const t=await navigator.serviceWorker?.getRegistration(),o=document.createElement("a"),n=new y,u=n.writable;if(o.download=this.name,E||!t){let r=[];n.readable.pipeTo(new m({write(s){r.push(new _([s]))},close(){const s=new _(r,{type:"application/octet-stream; charset=utf-8"});r=[],o.href=URL.createObjectURL(s),o.click(),setTimeout(()=>URL.revokeObjectURL(o.href),1e4)}}))}else{const{writable:r,readablePort:s}=new S(m),a=encodeURIComponent(this.name).replace(/['()]/g,escape).replace(/\*/g,"%2A"),R={"content-disposition":"attachment; filename*=UTF-8''"+a,"content-type":"application/octet-stream; charset=utf-8",...e.size?{"content-length":e.size}:{}},f=setTimeout(()=>t.active.postMessage(0),1e4);n.readable.pipeThrough(new y({transform(c,d){if(c instanceof Uint8Array)return d.enqueue(c);const g=new Response(c).body.getReader(),h=j=>g.read().then(p=>p.done?0:h(d.enqueue(p.value)));return h()}})).pipeTo(r).finally(()=>{clearInterval(f)}),t.active.postMessage({url:t.scope+a,headers:R,readablePort:s},[s]);const i=document.createElement("iframe");i.hidden=!0,i.src=t.scope+a,document.body.appendChild(i)}return u.getWriter()}}const M=0,T=0,L=1,O=1,U=2;class k{constructor(e){e.onmessage=t=>this._onMessage(t.data),this._port=e,this._resetReady()}start(e){return this._controller=e,this._readyPromise}write(e){const t={type:M,chunk:e};return this._port.postMessage(t,[e.buffer]),this._resetReady(),this._readyPromise}close(){this._port.postMessage({type:U}),this._port.close()}abort(e){this._port.postMessage({type:O,reason:e}),this._port.close()}_onMessage(e){e.type===T&&this._resolveReady(),e.type===L&&this._onError(e.reason)}_onError(e){this._controller.error(e),this._rejectReady(e),this._port.close()}_resetReady(){this._readyPromise=new Promise((e,t)=>{this._readyResolve=e,this._readyReject=t}),this._readyPending=!0}_resolveReady(){this._readyResolve(),this._readyPending=!1}_rejectReady(e){this._readyPending||this._resetReady(),this._readyPromise.catch(()=>{}),this._readyReject(e),this._readyPending=!1}}class S{constructor(e){const t=new MessageChannel;this.readablePort=t.port1,this.writable=new e(new k(t.port2))}}export{C as FileHandle};
